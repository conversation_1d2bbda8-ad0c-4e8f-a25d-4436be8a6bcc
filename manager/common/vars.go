package common

import commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"

var (
	// InterfaceDocumentBuiltinCategoryNames 接口文档内建分类名称
	InterfaceDocumentBuiltinCategoryNames = []string{ConstCategoryRootAllDocument}

	// InterfaceSchemaBuiltinCategoryNames 接口数据模型内建分类名称
	InterfaceSchemaBuiltinCategoryNames = []string{ConstCategoryRootAllSchema}

	// InterfaceDefinitionProtobufImportTypes 接口定义`protobuf`协议的导入类型
	InterfaceDefinitionProtobufImportTypes = []string{
		ConstInterfaceDefinitionImportTypeTT,
		ConstInterfaceDefinitionImportTypeTTMeta,
	}

	// ComponentGroupBuiltinCategoryNames 组件组内建分类名称
	ComponentGroupBuiltinCategoryNames = []string{
		ConstCategoryRootAllComponentGroup,
		ConstCategorySubRootBusinessComponent,
		ConstCategorySubRootSetupTeardownComponent,
	}

	// ApiCaseBuiltinCategoryNames API用例内建分类名称
	ApiCaseBuiltinCategoryNames = []string{ConstCategoryRootAllApiCase}

	// ApiSuiteBuiltinCategoryNames API集合内建分类名称
	ApiSuiteBuiltinCategoryNames = []string{ConstCategoryRootAllApiSuite}

	// ApiPlanBuiltinCategoryNames API计划内建分类名称
	ApiPlanBuiltinCategoryNames = []string{ConstCategoryRootAllApiPlan}

	// UiPlanBuiltinCategoryNames UI计划内建分类名称
	UiPlanBuiltinCategoryNames = []string{ConstCategoryRootAllUiPlan}

	// PerfCaseBuiltinCategoryNames 压测用例内建分类名称
	PerfCaseBuiltinCategoryNames = []string{ConstCategoryRootAllPerfScenario}

	// PerfPlanBuiltinCategoryNames 压测计划内建分类名称
	PerfPlanBuiltinCategoryNames = []string{ConstCategoryRootAllPerfPlan}

	// StabilityPlanBuiltinCategoryNames 稳测计划内建分类名称
	StabilityPlanBuiltinCategoryNames = []string{ConstCategoryRootAllStabilityPlan}

	// UIAgentComponentBuiltinCategoryNames UI Agent组件内建分类名称
	UIAgentComponentBuiltinCategoryNames = []string{ConstCategoryRootAllUIAgentComponent}

	// defaultPerfCaseStepOfTTAuth TT登录压测用例步骤
	defaultPerfCaseStepOfTTAuth = &commonpb.PerfCaseStepV2{
		Name:       ConstTTAuthName,
		Method:     ConstTTAuthMethod,
		Body:       ConstTTAuthBody,
		Sleep:      ConstTTAuthSleepTime,
		Cmd:        ConstTTAuthCmd,
		GrpcPath:   ConstTTAuthGrpcPath,
		Deprecated: false,
	}
)
