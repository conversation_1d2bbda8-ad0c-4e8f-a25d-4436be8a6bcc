package reporter

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/uiagentreporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

const timeoutOfCountFailedCase = 5 * time.Second

type RPCClient struct {
	conf zrpc.RpcClientConf

	api     reporter.Reporter
	uiAgent uiagentreporter.UIAgentReporter
}

func NewRPCClient(c zrpc.RpcClientConf) *RPCClient {
	return &RPCClient{
		conf: c,

		api: reporter.NewReporter(
			zrpc.MustNewClient(
				c, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		uiAgent: uiagentreporter.NewUIAgentReporter(
			zrpc.MustNewClient(
				c, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}

func (c *RPCClient) GetCaseLatestRecord(
	ctx context.Context, in *pb.GetCaseLatestRecordRequest, opts ...grpc.CallOption,
) (resp *pb.GetCaseLatestRecordResponse, err error) {
	return c.api.GetCaseLatestRecord(ctx, in, opts...)
}

func (c *RPCClient) DelCaseFailStatForPlan(
	ctx context.Context, in *pb.DelCaseFailStatForPlanReq, opts ...grpc.CallOption,
) (resp *pb.DelCaseFailStatForPlanResp, err error) {
	return c.api.DelCaseFailStatForPlan(ctx, in, opts...)
}

func (c *RPCClient) CountFailedCaseInLastNDays(
	ctx context.Context, in *pb.CountFailedCaseInLastNDaysReq, opts ...grpc.CallOption,
) (resp *pb.CountFailedCaseInLastNDaysResp, err error) {
	opts = append([]grpc.CallOption{zrpc.WithCallTimeout(timeoutOfCountFailedCase)}, opts...)
	return c.api.CountFailedCaseInLastNDays(ctx, in, opts...)
}

func (c *RPCClient) GetCountOfUIAgentComponentRecordsByImage(
	ctx context.Context, in *pb.GetCountOfUIAgentComponentRecordsByImageReq, opts ...grpc.CallOption,
) (*pb.GetCountOfUIAgentComponentRecordsByImageResp, error) {
	return c.uiAgent.GetCountOfUIAgentComponentRecordsByImage(ctx, in, opts...)
}
