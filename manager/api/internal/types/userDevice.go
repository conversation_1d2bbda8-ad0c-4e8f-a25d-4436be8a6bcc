package types

import "mime/multipart"

type CheckInputMethodReq struct {
	DeviceType    int8   `form:"device_type" validate:"oneof=1 2" zh:"设备类型"`
	PlatformType  int8   `form:"platform_type" validate:"oneof=1 2" zh:"平台类型"`
	UDID          string `form:"udid" validate:"required" copier:"Udid" zh:"设备编号"`
	RemoteAddress string `form:"remote_address" validate:"required" zh:"远程连接地址"`
	InputMethod   string `form:"input_method,omitempty,optional" zh:"待检查的输入法ID"`
}

type CheckInputMethodResp struct {
	Installed bool `json:"installed"`
}

type InstallInputMethodReq struct {
	DeviceType    int8   `form:"device_type" validate:"oneof=1 2" zh:"设备类型"`
	PlatformType  int8   `form:"platform_type" validate:"oneof=1 2" zh:"平台类型"`
	UDID          string `form:"udid" validate:"required" copier:"Udid" zh:"设备编号"`
	RemoteAddress string `form:"remote_address" validate:"required" zh:"远程连接地址"`
	InputMethod   string `form:"input_method,omitempty,optional" zh:"待安装的输入法ID"`

	InputMethodFile       multipart.File        `form:"-"` // get from `r.FormFile(formFileKey)`
	InputMethodFileHeader *multipart.FileHeader `form:"-"` // get from `r.FormFile(formFileKey)`
}

type InstallInputMethodResp struct{}
