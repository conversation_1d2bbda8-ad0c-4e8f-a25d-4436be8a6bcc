package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

type ApplicationConfiguration struct {
	ProjectId       string                 `json:"project_id"`
	ConfigId        string                 `json:"config_id"`
	Name            string                 `json:"name"`
	Description     string                 `json:"description"`
	PlatformType    int8                   `json:"platform_type"`
	AppId           string                 `json:"app_id"`
	AppDownloadLink string                 `json:"app_download_link"`
	Prompts         []*PromptConfiguration `json:"prompts"`
	CreatedBy       *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy       *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt       int64                  `json:"created_at"`
	UpdatedAt       int64                  `json:"updated_at"`
}

type SearchApplicationConfigurationReferenceItem struct {
	ProjectId     string                 `json:"project_id"`
	ConfigId      string                 `json:"config_id"`
	ReferenceType string                 `json:"reference_type"`
	ReferenceId   string                 `json:"reference_id"`
	Name          string                 `json:"name"`
	Description   string                 `json:"description"`
	CreatedBy     *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy     *userinfo.FullUserInfo `json:"updated_by"`
}

type CreateApplicationConfigReq struct {
	ProjectId       string   `json:"project_id" validate:"required" zh:"项目ID"`
	Name            string   `json:"name" validate:"gte=1,lte=64" zh:"应用配置名称"`
	Description     string   `json:"description" validate:"lte=255" zh:"应用配置描述"`
	PlatformType    int8     `json:"platform_type" validate:"required,oneof=1 2" zh:"平台类型（1: Android、2: IOS）"`
	AppId           string   `json:"app_id" validate:"gte=1,lte=64" zh:"应用ID（Android：package_name；IOS：bundle_id）"`
	AppDownloadLink string   `json:"app_download_link" validate:"omitempty,url,lte=255" zh:"应用下载链接"`
	Prompts         []string `json:"prompts" validate:"gte=0" zh:"Prompt配置ID列表"`
}

type CreateApplicationConfigResp struct {
	ConfigId string `json:"config_id"`
}

type RemoveApplicationConfigReq struct {
	ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
	ConfigIds []string `json:"config_ids" validate:"gt=0" zh:"应用配置ID列表"`
}

type RemoveApplicationConfigResp struct{}

type ModifyApplicationConfigReq struct {
	ProjectId       string   `json:"project_id" validate:"required" zh:"项目ID"`
	ConfigId        string   `json:"config_id" validate:"required" zh:"应用配置ID"`
	Name            string   `json:"name" validate:"gte=1,lte=64" zh:"应用配置名称"`
	Description     string   `json:"description" validate:"lte=255" zh:"应用配置描述"`
	PlatformType    int8     `json:"platform_type" validate:"required,oneof=1 2" zh:"平台类型（1: Android、2: IOS）"`
	AppId           string   `json:"app_id" validate:"gte=1,lte=64" zh:"应用ID（Android：package_name；IOS：bundle_id）"`
	AppDownloadLink string   `json:"app_download_link" validate:"omitempty,url,lte=255" zh:"应用下载链接"`
	Prompts         []string `json:"prompts" validate:"gte=0" zh:"Prompt配置ID列表"`
}

type ModifyApplicationConfigResp struct{}

type SearchApplicationConfigReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchApplicationConfigResp struct {
	CurrentPage uint64                      `json:"current_page"`
	PageSize    uint64                      `json:"page_size"`
	TotalCount  uint64                      `json:"total_count"`
	TotalPage   uint64                      `json:"total_page"`
	Items       []*ApplicationConfiguration `json:"items"`
}

type ViewApplicationConfigReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	ConfigId  string `form:"config_id" validate:"required" zh:"应用配置ID"`
}

type ViewApplicationConfigResp struct {
	*ApplicationConfiguration
}

type SearchApplicationConfigReferenceReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	ConfigId   string           `json:"config_id" validate:"required" zh:"应用配置ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchApplicationConfigReferenceResp struct {
	CurrentPage uint64                                         `json:"current_page"`
	PageSize    uint64                                         `json:"page_size"`
	TotalCount  uint64                                         `json:"total_count"`
	TotalPage   uint64                                         `json:"total_page"`
	Items       []*SearchApplicationConfigurationReferenceItem `json:"items"`
}
