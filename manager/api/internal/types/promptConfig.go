package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
)

type PromptConfiguration struct {
	ProjectId   string                 `json:"project_id"`
	ConfigId    string                 `json:"config_id"`
	Purpose     int8                   `json:"purpose"`
	Category    int8                   `json:"category"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Content     string                 `json:"content"`
	CreatedBy   *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy   *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt   int64                  `json:"created_at"`
	UpdatedAt   int64                  `json:"updated_at"`
}

type SearchPromptConfigurationReferenceItem struct {
	ProjectId     string                 `json:"project_id"`
	ConfigId      string                 `json:"config_id"`
	ReferenceType string                 `json:"reference_type"`
	ReferenceId   string                 `json:"reference_id"`
	Name          string                 `json:"name"`
	Description   string                 `json:"description"`
	CreatedBy     *userinfo.FullUserInfo `json:"created_by"`
	UpdatedBy     *userinfo.FullUserInfo `json:"updated_by"`
	CreatedAt     int64                  `json:"created_at"`
	UpdatedAt     int64                  `json:"updated_at"`
}

type CreatePromptConfigReq struct {
	ProjectId   string `json:"project_id" validate:"required" zh:"项目ID"`
	Purpose     int8   `json:"purpose,default=1" validate:"required,oneof=1" zh:"用途（1: UI_AGENT）"`
	Category    int8   `json:"category" validate:"required,oneof=1 2 3" zh:"分类（1: 背景、2: UI组件、3: 异常处理）"`
	Name        string `json:"name" validate:"gte=1,lte=64" zh:"Prompt配置名称"`
	Description string `json:"description" validate:"lte=255" zh:"Prompt配置描述"`
	Content     string `json:"content" validate:"gte=1" zh:"Prompt配置内容"`
}

type CreatePromptConfigResp struct {
	ConfigId string `json:"config_id"`
}

type RemovePromptConfigReq struct {
	ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
	ConfigIds []string `json:"config_ids" validate:"gt=0" zh:"Prompt配置ID列表"`
}

type RemovePromptConfigResp struct{}

type ModifyPromptConfigReq struct {
	ProjectId   string `json:"project_id" validate:"required" zh:"项目ID"`
	ConfigId    string `json:"config_id" validate:"required" zh:"Prompt配置ID"`
	Name        string `json:"name" validate:"gte=1,lte=64" zh:"Prompt配置名称"`
	Description string `json:"description" validate:"lte=255" zh:"Prompt配置描述"`
	Content     string `json:"content" validate:"gte=1" zh:"Prompt配置内容"`
}

type ModifyPromptConfigResp struct{}

type SearchPromptConfigReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Purpose    int8             `json:"purpose,default=1" validate:"required,oneof=1" zh:"用途（1: UI_AGENT）"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchPromptConfigResp struct {
	CurrentPage uint64                 `json:"current_page"`
	PageSize    uint64                 `json:"page_size"`
	TotalCount  uint64                 `json:"total_count"`
	TotalPage   uint64                 `json:"total_page"`
	Items       []*PromptConfiguration `json:"items"`
}

type ViewPromptConfigReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	ConfigId  string `form:"config_id" validate:"required" zh:"Prompt配置ID"`
}

type ViewPromptConfigResp struct {
	*PromptConfiguration
}

type SearchPromptConfigReferenceReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	ConfigId   string           `json:"config_id" validate:"required" zh:"Prompt配置ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchPromptConfigReferenceResp struct {
	CurrentPage uint64                                    `json:"current_page"`
	PageSize    uint64                                    `json:"page_size"`
	TotalCount  uint64                                    `json:"total_count"`
	TotalPage   uint64                                    `json:"total_page"`
	Items       []*SearchPromptConfigurationReferenceItem `json:"items"`
}
