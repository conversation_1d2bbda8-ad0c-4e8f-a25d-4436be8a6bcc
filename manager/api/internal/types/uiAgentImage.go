package types

import "mime/multipart"

type UploadUIAgentImageReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`

	ImageFile       multipart.File        `form:"-"` // get from `r.FormFile(formFileKey)`
	ImageFileHeader *multipart.FileHeader `form:"-"` // get from `r.FormFile(formFileKey)`
}

type UploadUIAgentImageResp struct {
	ImageId string `json:"image_id"`
}

type DownloadUIAgentImageReq struct {
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
	ImageId   string `form:"image_id" validate:"required" zh:"图片ID"`
}

type DownloadUIAgentImageResp struct{}
