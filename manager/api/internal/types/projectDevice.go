package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	devicehubcommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
)

type BindDevice struct {
	UDID  string `json:"udid" copier:"Udid" validate:"gte=1,lte=64" zh:"设备编号"`
	Usage int8   `json:"usage" validate:"oneof=1 2" zh:"设备用途"`
}

type DeviceRelationship struct {
	UDID  string `json:"udid" copier:"Udid" validate:"gte=1,lte=64" zh:"设备编号"`
	Count int64  `json:"count" validate:"gte=0" zh:"关联的计划数量"`
}

type ProjectDevice struct {
	devicehubcommon.Device

	ProjectId string `json:"project_id" zh:"项目ID"`
	Usage     int8   `json:"usage" zh:"设备用途"`
}

type SearchProjectDeviceReferenceItem struct {
	ProjectId     string                 `json:"project_id"`     // 项目ID
	Udid          string                 `json:"udid"`           // 设备编号
	ReferenceType string                 `json:"reference_type"` // 引用对象类型
	ReferenceId   string                 `json:"reference_id"`   // 引用对象ID
	CategoryId    string                 `json:"category_id"`    // 引用对象分类ID
	Name          string                 `json:"name"`           // 引用对象名称
	Description   string                 `json:"description"`    // 引用对象描述
	MaintainedBy  *userinfo.FullUserInfo `json:"maintained_by"`  // 维护者
	CreatedBy     *userinfo.FullUserInfo `json:"created_by"`     // 创建者
	UpdatedBy     *userinfo.FullUserInfo `json:"updated_by"`     // 更新者
	CreatedAt     int64                  `json:"created_at"`     // 创建时间
	UpdatedAt     int64                  `json:"updated_at"`     // 更新时间
}

type ModifyProjectDeviceReq struct {
	ProjectId string        `json:"project_id" validate:"required" zh:"项目ID"`
	Devices   []*BindDevice `json:"devices" validate:"gte=0,dive,required" zh:"设备列表"`
}

type ModifyProjectDeviceResp struct {
	CreateItems       []*BindDevice         `json:"create_items"`
	UpdateItems       []*BindDevice         `json:"update_items"`
	DeleteItems       []*BindDevice         `json:"delete_items"`
	IgnoreCreateItems []*BindDevice         `json:"ignore_create_items"`
	IgnoreUpdateItems []*DeviceRelationship `json:"ignore_update_items"`
	IgnoreDeleteItems []*DeviceRelationship `json:"ignore_delete_items"`
}

type SearchProjectDeviceReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Usage      int8             `json:"usage" validate:"oneof=1 2" zh:"用途"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchProjectDeviceResp struct {
	CurrentPage uint64           `json:"current_page"`
	PageSize    uint64           `json:"page_size"`
	TotalCount  uint64           `json:"total_count"`
	TotalPage   uint64           `json:"total_page"`
	Items       []*ProjectDevice `json:"items"`
}

type SearchUnassignedProjectDeviceReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchUnassignedProjectDeviceResp struct {
	CurrentPage uint64           `json:"current_page"`
	PageSize    uint64           `json:"page_size"`
	TotalCount  uint64           `json:"total_count"`
	TotalPage   uint64           `json:"total_page"`
	Items       []*ProjectDevice `json:"items"`
}

type SearchProjectDeviceReferenceReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Udid       string           `json:"udid" validate:"required" zh:"设备编号"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchProjectDeviceReferenceResp struct {
	CurrentPage uint64                              `json:"current_page"`
	PageSize    uint64                              `json:"page_size"`
	TotalCount  uint64                              `json:"total_count"`
	TotalPage   uint64                              `json:"total_page"`
	Items       []*SearchProjectDeviceReferenceItem `json:"items"`
}

type AcquireProjectDeviceReq struct {
	ProjectId  string `json:"project_id" validate:"required" zh:"项目ID"`
	Usage      int8   `json:"usage" validate:"oneof=1 2" zh:"用途"`
	UDID       string `json:"udid" validate:"required" copier:"Udid" zh:"设备编号"`
	Expiration uint32 `json:"expiration,omitempty,optional" zh:"最大占用时长（秒）"`
}

type AcquireProjectDeviceResp struct {
	*ProjectDevice
}

type ReleaseProjectDeviceReq struct {
	ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
	Usage     int8   `json:"usage" validate:"oneof=1 2" zh:"用途"`
	UDID      string `json:"udid" validate:"required" copier:"Udid" zh:"设备编号"`
	Token     string `json:"token" zh:"令牌"`
}

type ReleaseProjectDeviceResp struct{}
