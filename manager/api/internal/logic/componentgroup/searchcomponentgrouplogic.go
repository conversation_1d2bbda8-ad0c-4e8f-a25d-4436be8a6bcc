package componentgroup

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchComponentGroupLogic struct {
	*BaseLogic
}

func NewSearchComponentGroupLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchComponentGroupLogic {
	return &SearchComponentGroupLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchComponentGroupLogic) SearchComponentGroup(req *types.SearchComponentGroupReq) (
	resp *types.SearchComponentGroupResp, err error,
) {
	in := &pb.SearchComponentGroupReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerComponentGroupRPC.SearchComponentGroup(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchComponentGroupResp{Items: []*types.ComponentGroup{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
