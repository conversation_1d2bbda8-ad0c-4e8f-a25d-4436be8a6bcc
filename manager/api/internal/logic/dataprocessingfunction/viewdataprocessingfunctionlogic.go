package dataprocessingfunction

import (
	"context"

	"github.com/pkg/errors"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewDataProcessingFunctionLogic struct {
	*BaseLogic
}

func NewViewDataProcessingFunctionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ViewDataProcessingFunctionLogic {
	return &ViewDataProcessingFunctionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewDataProcessingFunctionLogic) ViewDataProcessingFunction(req *types.ViewDataProcessingFunctionReq) (
	resp *types.ViewDataProcessingFunctionResp, err error,
) {
	in := &pb.ViewDataProcessingFunctionReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerDataProcessingFunctionRPC.ViewDataProcessingFunction(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.ViewDataProcessingFunctionResp{
		Function: &types.Function{
			Parameters: []*types.Parameter{}, Returns: []*types.Return{},
		},
	}
	if err = utils.Copy(resp.Function, out.Function, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	if resp.ProjectId == common.ConstBuiltinGlobalProjectId || resp.Type == constants.BUILTIN {
		// desensitized builtin global project_id
		resp.ProjectId = qetconstants.SensitiveWorld
	}

	return resp, nil
}
