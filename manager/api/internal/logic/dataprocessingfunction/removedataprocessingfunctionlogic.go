package dataprocessingfunction

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveDataProcessingFunctionLogic struct {
	*BaseLogic
}

func NewRemoveDataProcessingFunctionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveDataProcessingFunctionLogic {
	return &RemoveDataProcessingFunctionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveDataProcessingFunctionLogic) RemoveDataProcessingFunction(req *types.RemoveDataProcessingFunctionReq) (resp *types.RemoveDataProcessingFunctionResp, err error) {
	in := &pb.RemoveDataProcessingFunctionReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	_, err = l.svcCtx.ManagerDataProcessingFunctionRPC.RemoveDataProcessingFunction(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemoveDataProcessingFunctionResp{}, nil
}
