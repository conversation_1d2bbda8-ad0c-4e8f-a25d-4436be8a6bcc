package dataprocessingfunction

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchDataProcessingFunctionLogic struct {
	*BaseLogic
}

func NewSearchDataProcessingFunctionLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchDataProcessingFunctionLogic {
	return &SearchDataProcessingFunctionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchDataProcessingFunctionLogic) SearchDataProcessingFunction(req *types.SearchDataProcessingFunctionReq) (
	resp *types.SearchDataProcessingFunctionResp, err error,
) {
	in := &pb.SearchDataProcessingFunctionReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerDataProcessingFunctionRPC.SearchDataProcessingFunction(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchDataProcessingFunctionResp{Items: []*types.Function{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	for _, item := range resp.Items {
		if item.ProjectId == common.ConstBuiltinGlobalProjectId || item.Type == constants.BUILTIN {
			// desensitized builtin global project_id
			item.ProjectId = qetconstants.SensitiveWorld
		}
	}

	return resp, nil
}
