package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveInterfaceCaseLogic struct {
	*BaseLogic
}

func NewRemoveInterfaceCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveInterfaceCaseLogic {
	return &RemoveInterfaceCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveInterfaceCaseLogic) RemoveInterfaceCase(req *types.RemoveInterfaceCaseReq) (
	resp *types.RemoveInterfaceCaseResp, err error,
) {
	in := &pb.RemoveInterfaceCaseReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerInterfaceDefinitionRPC.RemoveInterfaceCase(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemoveInterfaceCaseResp{}, nil
}
