package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewInterfaceCaseLogic struct {
	*BaseLogic
}

func NewViewInterfaceCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewInterfaceCaseLogic {
	return &ViewInterfaceCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewInterfaceCaseLogic) ViewInterfaceCase(req *types.ViewInterfaceCaseReq) (
	resp *types.ViewInterfaceCaseResp, err error,
) {
	in := &pb.ViewInterfaceCaseReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerInterfaceDefinitionRPC.ViewInterfaceCase(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewInterfaceCaseResp{
		InterfaceCase: &types.InterfaceCase{
			Tags: []string{}, Nodes: []*types.Node{}, Edges: []*types.Edge{}, Combos: []*types.Combo{},
		},
	}
	if err = utils.Copy(resp.InterfaceCase, out.Case, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
