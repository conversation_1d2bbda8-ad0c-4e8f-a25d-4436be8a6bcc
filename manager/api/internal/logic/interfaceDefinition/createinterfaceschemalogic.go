package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/logic/category"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateInterfaceSchemaLogic struct {
	*BaseLogic

	ccl *category.CreateCategoryLogic
}

func NewCreateInterfaceSchemaLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateInterfaceSchemaLogic {
	return &CreateInterfaceSchemaLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
		ccl:       category.NewCreateCategoryLogic(ctx, svcCtx),
	}
}

func (l *CreateInterfaceSchemaLogic) CreateInterfaceSchema(req *types.CreateInterfaceSchemaReq) (
	resp *types.CreateInterfaceSchemaResp, err error,
) {
	in := &pb.CreateInterfaceSchemaReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerInterfaceDefinitionRPC.CreateInterfaceSchema(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateInterfaceSchemaResp{SchemaId: out.GetSchema().GetSchemaId()}, nil
}

//func (l *CreateInterfaceSchemaLogic) CreateInterfaceSchemaForInternal(
//	ctx context.Context, session sqlx.Session, req CreateInterfaceSchemaInternalReq,
//) (string, error) {
//	var (
//		schemaId string
//		err      error
//	)
//
//	if req.SchemaId != "" {
//		schemaId = req.SchemaId
//	} else {
//		schemaId, err = l.generateSchemaId(req.ProjectId, nil)
//		if err != nil {
//			return "", err
//		}
//	}
//
//	if req.Mode == "" {
//		req.Mode = common.ConstInterfaceDefinitionCreateModeManual
//	}
//
//	// create interface schema in a transaction
//	fn := func(context context.Context, session sqlx.Session) error {
//		schema := &model.InterfaceSchema{
//			ProjectId:  req.ProjectId,
//			CategoryId: req.CategoryId,
//			SchemaId:   schemaId,
//			FullName:   req.FullName,
//			Name:       req.Name,
//			Description: sql.NullString{
//				String: req.Description,
//				Valid:  req.Description != "",
//			},
//			Mode: req.Mode,
//			ImportType: sql.NullString{
//				String: req.ImportType,
//				Valid:  req.ImportType != "",
//			},
//			Data:      jsonx.MarshalToStringIgnoreError(req.Data),
//			CreatedBy: "1", // 暂时hard code
//			UpdatedBy: "1", // 暂时hard code
//		}
//
//		if _, err = l.svcCtx.InterfaceSchemaModel.InsertTX(context, session, schema); err != nil {
//			return errors.Wrapf(
//				errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v",
//				l.svcCtx.InterfaceSchemaModel.Table(), schema, err,
//			)
//		}
//
//		// create a category in the interface schema category tree
//		if _, err = l.ccl.CreateCategoryForInternal(
//			context, session, category.CreateCategoryInternalReq{
//				CreateCategoryReq: types.CreateCategoryReq{
//					ProjectId:   req.ProjectId,
//					Type:        common.ConstCategoryTreeTypeInterfaceSchema,
//					Name:        req.Name,
//					Description: req.Description,
//					ParentId:    req.CategoryId,
//				},
//				CategoryType:   common.ConstCategoryTypeFile,
//				RootType:       common.GetRootTypeByNodeType(common.ConstInterfaceDefinitionTypeSchema),
//				NodeType:       common.ConstInterfaceDefinitionTypeSchema,
//				NodeId:         schemaId,
//				IsInternalCall: true,
//			},
//		); err != nil {
//			return err
//		}
//
//		// create reference relationship between schema and schema
//		return mr.MapReduceVoid[string, any](
//			func(source chan<- string) {
//				for _, sid := range findReferenceInSchema(*req.Data) {
//					source <- sid
//				}
//			}, func(item string, writer mr.Writer[any], cancel func(error)) {
//				var err error
//				defer func() {
//					if err != nil {
//						cancel(err)
//					}
//				}()
//
//				err = l.createSchemaReference(
//					context, session, &createSchemaReferenceInternalReq{
//						ProjectId:     req.ProjectId,
//						ReferenceType: common.ConstInterfaceDefinitionTypeSchema,
//						ReferenceId:   schemaId,
//						SchemaId:      item,
//					},
//				)
//			}, func(pipe <-chan any, cancel func(error)) {
//			}, mr.WithContext(l.ctx),
//		)
//	}
//
//	if session != nil {
//		if ctx == nil {
//			ctx = l.ctx
//		}
//		err = fn(ctx, session)
//	} else {
//		err = l.svcCtx.InterfaceSchemaModel.Trans(l.ctx, fn)
//	}
//	if err != nil {
//		return "", err
//	}
//
//	return schemaId, nil
//}
