package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyInterfaceConfigLogic struct {
	*BaseLogic
}

func NewModifyInterfaceConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyInterfaceConfigLogic {
	return &ModifyInterfaceConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyInterfaceConfigLogic) ModifyInterfaceConfig(req *types.ModifyInterfaceConfigReq) (
	resp *types.ModifyInterfaceConfigResp, err error,
) {
	in := &pb.ModifyInterfaceConfigReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerInterfaceDefinitionRPC.ModifyInterfaceConfig(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyInterfaceConfigResp{}, nil
}
