package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type MaintainInterfaceCaseLogic struct {
	*BaseLogic
}

func NewMaintainInterfaceCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MaintainInterfaceCaseLogic {
	return &MaintainInterfaceCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *MaintainInterfaceCaseLogic) MaintainInterfaceCase(req *types.MaintainInterfaceCaseReq) (
	resp *types.MaintainInterfaceCaseResp, err error,
) {
	in := &pb.MaintainInterfaceCaseReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerInterfaceDefinitionRPC.MaintainInterfaceCase(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.MaintainInterfaceCaseResp{}, nil
}
