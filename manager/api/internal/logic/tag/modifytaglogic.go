package tag

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyTagLogic struct {
	*BaseLogic
}

func NewModifyTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyTagLogic {
	return &ModifyTagLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyTagLogic) ModifyTag(req *types.ModifyTagReq) (resp *types.ModifyTagResp, err error) {
	in := &pb.ModifyTagReq{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	_, err = l.svcCtx.ManagerTagRPC.ModifyTag(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyTagResp{}, nil
}
