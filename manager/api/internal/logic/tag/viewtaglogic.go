package tag

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewTagLogic struct {
	*BaseLogic
}

func NewViewTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewTagLogic {
	return &ViewTagLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewTagLogic) ViewTag(req *types.ViewTagReq) (resp *types.ViewTagResp, err error) {
	in := &pb.ViewTagReq{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	out, err := l.svcCtx.ManagerTagRPC.ViewTag(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewTagResp{Tag: &types.Tag{}}
	if err = utils.Copy(resp.Tag, out.Tag, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v", out, err)
	}

	return resp, nil
}
