package project

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewProjectLogic struct {
	*BaseLogic
}

func NewViewProjectLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewProjectLogic {
	return &ViewProjectLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewProjectLogic) ViewProject(req *types.ViewProjectReq) (resp *types.ViewProjectResp, err error) {
	in := &pb.ViewProjectReq{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	out, err := l.svcCtx.ManagerProjectRPC.ViewProject(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewProjectResp{Project: &types.Project{}}
	if err = utils.Copy(resp.Project, out.Project, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v", out, err)
	}

	return resp, nil
}
