package accountconfig

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveAccountConfigLogic struct {
	*BaseLogic
}

func NewRemoveAccountConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveAccountConfigLogic {
	return &RemoveAccountConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveAccountConfigLogic) RemoveAccountConfig(req *types.RemoveAccountConfigReq) (
	resp *types.RemoveAccountConfigResp, err error,
) {
	in := &pb.RemoveAccountConfigurationReq{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerAccountConfigurationRPC.RemoveAccountConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemoveAccountConfigResp{}, nil
}
