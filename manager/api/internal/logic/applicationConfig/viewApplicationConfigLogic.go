package applicationConfig

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewApplicationConfigLogic struct {
	*BaseLogic
}

func NewViewApplicationConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewApplicationConfigLogic {
	return &ViewApplicationConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewApplicationConfigLogic) ViewApplicationConfig(req *types.ViewApplicationConfigReq) (
	resp *types.ViewApplicationConfigResp, err error,
) {
	in := &pb.ViewApplicationConfigurationReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerApplicationConfigurationRPC.ViewApplicationConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewApplicationConfigResp{ApplicationConfiguration: &types.ApplicationConfiguration{}}
	if err = utils.Copy(resp.ApplicationConfiguration, out.GetConfiguration(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
