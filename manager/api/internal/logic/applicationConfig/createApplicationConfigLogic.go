package applicationConfig

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateApplicationConfigLogic struct {
	*BaseLogic
}

func NewCreateApplicationConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateApplicationConfigLogic {
	return &CreateApplicationConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateApplicationConfigLogic) CreateApplicationConfig(req *types.CreateApplicationConfigReq) (
	resp *types.CreateApplicationConfigResp, err error,
) {
	in := &pb.CreateApplicationConfigurationReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerApplicationConfigurationRPC.CreateApplicationConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateApplicationConfigResp{ConfigId: out.GetConfiguration().GetConfigId()}, nil
}
