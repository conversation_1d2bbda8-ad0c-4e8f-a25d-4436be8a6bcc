package apiplan

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type AdvancedSearchSuiteNotInApiPlanLogic struct {
	*BaseLogic
}

func NewAdvancedSearchSuiteNotInApiPlanLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *AdvancedSearchSuiteNotInApiPlanLogic {
	return &AdvancedSearchSuiteNotInApiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *AdvancedSearchSuiteNotInApiPlanLogic) AdvancedSearchSuiteNotInApiPlan(req *types.AdvancedSearchSuiteNotInApiPlanReq) (
	resp *types.AdvancedSearchSuiteNotInApiPlanResp, err error,
) {
	in := &pb.AdvancedSearchSuiteNotInApiPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerApiPlanRPC.AdvancedSearchSuiteNotInApiPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.AdvancedSearchSuiteNotInApiPlanResp{Items: []*types.AdvancedSearchSuiteNotInApiPlanItem{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
