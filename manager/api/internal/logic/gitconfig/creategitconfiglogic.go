package gitconfig

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

type CreateGitConfigLogic struct {
	*BaseLogic
}

func NewCreateGitConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateGitConfigLogic {
	return &CreateGitConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateGitConfigLogic) CreateGitConfig(req *types.CreateGitConfigReq) (
	resp *types.CreateGitConfigResp, err error,
) {
	in := &pb.CreateGitConfigurationReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerGitConfigurationRPC.CreateGitConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateGitConfigResp{ConfigId: out.GetConfiguration().GetConfigId()}, nil
}
