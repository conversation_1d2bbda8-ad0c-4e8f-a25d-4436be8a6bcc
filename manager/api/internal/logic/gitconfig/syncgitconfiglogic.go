package gitconfig

import (
	"context"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SyncGitConfigLogic struct {
	*BaseLogic
}

func NewSyncGitConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncGitConfigLogic {
	return &SyncGitConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SyncGitConfigLogic) SyncGitConfig(req *types.SyncGitConfigReq) (resp *types.SyncGitConfigResp, err error) {
	_, err = l.svcCtx.ManagerGitConfigurationRPC.SyncGitConfiguration(l.ctx, &pb.SyncGitConfigurationReq{
		ProjectId:   req.ProjectID,
		ConfigId:    req.ConfigID,
		TriggerMode: commonpb.TriggerMode_MANUAL,
	})
	if err != nil {
		return nil, err
	}

	return &types.SyncGitConfigResp{}, nil
}
