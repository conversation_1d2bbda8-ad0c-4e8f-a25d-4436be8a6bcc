package generalconfig

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchGeneralConfigLogic struct {
	*BaseLogic
}

func NewSearchGeneralConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchGeneralConfigLogic {
	return &SearchGeneralConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchGeneralConfigLogic) SearchGeneralConfig(req *types.SearchGeneralConfigReq) (resp *types.SearchGeneralConfigResp, err error) {
	in := &pb.SearchGeneralConfigurationReq{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	out, err := l.svcCtx.ManagerGeneralConfigurationRPC.SearchGeneralConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchGeneralConfigResp{Items: []*types.GeneralConfiguration{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v", out, err)
	}

	return resp, nil
}
