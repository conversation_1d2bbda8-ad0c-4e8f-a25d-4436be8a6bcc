package review

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyReviewRecordLogic struct {
	*BaseLogic
}

func NewModifyReviewRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyReviewRecordLogic {
	return &ModifyReviewRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyReviewRecordLogic) ModifyReviewRecord(req *types.ModifyReviewRecordReq) (
	resp *types.ModifyReviewRecordResp, err error,
) {
	in := &pb.ModifyReviewRecordReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.svcCtx.ManagerReviewRPC.ModifyReviewRecord(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyReviewRecordResp{}, nil
}
