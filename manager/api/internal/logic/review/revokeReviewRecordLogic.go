package review

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RevokeReviewRecordLogic struct {
	*BaseLogic
}

func NewRevokeReviewRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RevokeReviewRecordLogic {
	return &RevokeReviewRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RevokeReviewRecordLogic) RevokeReviewRecord(req *types.RevokeReviewRecordReq) (
	resp *types.RevokeReviewRecordResp, err error,
) {
	in := &pb.RevokeReviewRecordReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.svcCtx.ManagerReviewRPC.RevokeReviewRecord(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RevokeReviewRecordResp{}, nil
}
