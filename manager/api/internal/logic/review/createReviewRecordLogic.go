package review

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateReviewRecordLogic struct {
	*BaseLogic
}

func NewCreateReviewRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateReviewRecordLogic {
	return &CreateReviewRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateReviewRecordLogic) CreateReviewRecord(req *types.CreateReviewRecordReq) (
	resp *types.CreateReviewRecordResp, err error,
) {
	in := &pb.CreateReviewRecordReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerReviewRPC.CreateReviewRecord(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateReviewRecordResp{ReviewId: out.GetRecord().GetReviewId()}, nil
}
