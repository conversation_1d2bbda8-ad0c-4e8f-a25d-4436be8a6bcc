package review

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ApproveReviewRecordLogic struct {
	*BaseLogic
}

func NewApproveReviewRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ApproveReviewRecordLogic {
	return &ApproveReviewRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ApproveReviewRecordLogic) ApproveReviewRecord(req *types.ApproveReviewRecordReq) (
	resp *types.ApproveReviewRecordResp, err error,
) {
	in := &pb.ApproveReviewRecordReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.svcCtx.ManagerReviewRPC.ApproveReviewRecord(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ApproveReviewRecordResp{}, nil
}
