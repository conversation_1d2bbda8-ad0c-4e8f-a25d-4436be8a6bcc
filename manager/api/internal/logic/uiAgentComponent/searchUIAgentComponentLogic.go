package uiAgentComponent

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchUIAgentComponentLogic struct {
	*BaseLogic
}

func NewSearchUIAgentComponentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchUIAgentComponentLogic {
	return &SearchUIAgentComponentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchUIAgentComponentLogic) SearchUIAgentComponent(req *types.SearchUIAgentComponentReq) (
	resp *types.SearchUIAgentComponentResp, err error,
) {
	in := &pb.SearchUIAgentComponentReq{}

	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerUIAgentComponentRPC.SearchUIAgentComponent(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.SearchUIAgentComponentResp{}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(out), err,
		)
	}

	return resp, nil
}
