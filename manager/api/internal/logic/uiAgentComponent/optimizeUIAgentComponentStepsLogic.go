package uiAgentComponent

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/clickPilot"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

type OptimizeUIAgentComponentStepsLogic struct {
	*BaseLogic
}

func NewOptimizeUIAgentComponentStepsLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *OptimizeUIAgentComponentStepsLogic {
	return &OptimizeUIAgentComponentStepsLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *OptimizeUIAgentComponentStepsLogic) OptimizeUIAgentComponentSteps(req *types.OptimizeUIAgentComponentStepsReq) (
	resp *types.OptimizeUIAgentComponentStepsResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, req.ProjectId); err != nil {
		return nil, err
	}

	steps, err := l.svcCtx.ClickPilotClient.OptimizeTaskSteps(req.Steps)
	if err != nil {
		var e *clickPilot.Error
		if errors.As(err, &e) {
			return nil, errorx.Errorf(
				errorx.CallExternalAPIFailure, "failed to optimize task steps, error: %s", e.Message(),
			)
		} else {
			return nil, errorx.Errorf(
				errorx.CallExternalAPIFailure, "failed to optimize task steps, error: %+v", err,
			)
		}
	}

	return &types.OptimizeUIAgentComponentStepsResp{Steps: steps}, nil
}
