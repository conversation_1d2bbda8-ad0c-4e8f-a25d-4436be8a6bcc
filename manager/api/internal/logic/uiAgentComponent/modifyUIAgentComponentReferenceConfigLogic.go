package uiAgentComponent

import (
	"context"
	"fmt"

	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

type ModifyUIAgentComponentReferenceConfigLogic struct {
	*BaseLogic
}

func NewModifyUIAgentComponentReferenceConfigLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyUIAgentComponentReferenceConfigLogic {
	return &ModifyUIAgentComponentReferenceConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyUIAgentComponentReferenceConfigLogic) ModifyUIAgentComponentReferenceConfig(req *types.ModifyUIAgentComponentReferenceConfigReq) (
	resp *types.ModifyUIAgentComponentReferenceConfigResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, req.ProjectId); err != nil {
		return nil, err
	}

	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockUIAgentComponentProjectIDComponentIDPrefix, req.ProjectId, req.ComponentId,
	)
	fn := func() error {
		// validate the component_id in req
		origin, err := model.CheckUIAgentComponentByComponentID(
			l.ctx, l.svcCtx.UIAgentComponentModel, req.ProjectId, req.ComponentId,
		)
		if err != nil {
			return err
		}

		if !origin.ReferenceId.Valid || origin.ReferenceId.String == "" {
			return errorx.Err(
				errorx.ProhibitedBehavior,
				"the ui agent component has not been bound to a reference configuration",
			)
		} else if origin.ReferenceId.String != req.ReferenceId {
			return errorx.Err(
				errorx.ProhibitedBehavior,
				"the ui agent component has not been bound to the reference configuration",
			)
		}

		records, err := l.svcCtx.ClickPilotClient.GetTaskRecord(req.ReferenceId)
		if err != nil {
			return err
		}

		fromSize := len(records)
		toSize := len(req.StepIds)
		if toSize > fromSize {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the number of steps cannot be greater than the number of records, number: %d, records: %d",
				toSize, fromSize,
			)
		}

		fromSet := set.NewHashset(uint64(fromSize), generic.Equals[int64], generic.HashInt64)
		toSet := set.NewHashset(uint64(toSize), generic.Equals[int64], generic.HashInt64, req.StepIds...)
		for _, record := range records {
			if record.StepID > 0 {
				fromSet.Put(record.StepID)
			}
		}

		if removeSet := fromSet.Difference(toSet); removeSet.Size() > 0 {
			if err = l.svcCtx.ClickPilotClient.DeleteRefCaseStep(removeSet.Keys()...); err != nil {
				return err
			}
		}

		return nil
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	return &types.ModifyUIAgentComponentReferenceConfigResp{}, nil
}
