package uiAgentComponent

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

type RemoveUIAgentComponentReferenceConfigLogic struct {
	*BaseLogic
}

func NewRemoveUIAgentComponentReferenceConfigLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *RemoveUIAgentComponentReferenceConfigLogic {
	return &RemoveUIAgentComponentReferenceConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveUIAgentComponentReferenceConfigLogic) RemoveUIAgentComponentReferenceConfig(req *types.RemoveUIAgentComponentReferenceConfigReq) (
	resp *types.RemoveUIAgentComponentReferenceConfigResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, req.ProjectId); err != nil {
		return nil, err
	}

	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockUIAgentComponentProjectIDComponentIDPrefix, req.ProjectId, req.ComponentId,
	)
	fn := func() error {
		// validate the component_id in req
		origin, err := model.CheckUIAgentComponentByComponentID(
			l.ctx, l.svcCtx.UIAgentComponentModel, req.ProjectId, req.ComponentId,
		)
		if err != nil {
			return err
		}

		if !origin.ReferenceId.Valid || origin.ReferenceId.String == "" {
			// no need to remove
			return nil
		} else if origin.ReferenceId.String != req.ReferenceId {
			return errorx.Err(
				errorx.ProhibitedBehavior,
				"the ui agent component has not been bound to the reference configuration",
			)
		}

		origin.ReferenceId = sql.NullString{}

		return l.svcCtx.UIAgentComponentModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				// 先更新再删除，删除失败不回滚
				if _, e := l.svcCtx.UIAgentComponentModel.Update(context, session, origin); e != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, e.Error()),
						"failed to update values to table, table: %s, values: %s, error: %+v",
						l.svcCtx.UIAgentComponentModel.Table(), jsonx.MarshalIgnoreError(origin), e,
					)
				}

				if e := l.svcCtx.ClickPilotClient.DeleteRefCase(req.ReferenceId); e != nil {
					l.Errorf(
						"failed to delete ref case, project_id: %s, component_id: %s, reference_id: %s, error: %+v",
						req.ProjectId, req.ComponentId, req.ReferenceId, e,
					)
				}

				return nil
			},
		)
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	return &types.RemoveUIAgentComponentReferenceConfigResp{}, nil
}
