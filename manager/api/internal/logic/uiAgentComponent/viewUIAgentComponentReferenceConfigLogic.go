package uiAgentComponent

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	common "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

type ViewUIAgentComponentReferenceConfigLogic struct {
	*BaseLogic
}

func NewViewUIAgentComponentReferenceConfigLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ViewUIAgentComponentReferenceConfigLogic {
	return &ViewUIAgentComponentReferenceConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewUIAgentComponentReferenceConfigLogic) ViewUIAgentComponentReferenceConfig(req *types.ViewUIAgentComponentReferenceConfigReq) (
	resp *types.ViewUIAgentComponentReferenceConfigResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, req.ProjectId); err != nil {
		return nil, err
	}

	// validate the component_id in req
	origin, err := model.CheckUIAgentComponentByComponentID(
		l.ctx, l.svcCtx.UIAgentComponentModel, req.ProjectId, req.ComponentId,
	)
	if err != nil {
		return nil, err
	}

	if !origin.ReferenceId.Valid || origin.ReferenceId.String == "" {
		return nil, errorx.Err(
			errorx.ProhibitedBehavior,
			"the ui agent component has not been bound to a reference configuration",
		)
	} else if origin.ReferenceId.String != req.ReferenceId {
		return nil, errorx.Err(
			errorx.ProhibitedBehavior,
			"the ui agent component has not been bound to the reference configuration",
		)
	}

	records, err := l.svcCtx.ClickPilotClient.GetTaskRecord(req.ReferenceId)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewUIAgentComponentReferenceConfigResp{
		Steps: make([]*common.UIAgentComponentStepRecord, 0, len(records)),
	}
	for _, record := range records {
		resp.Steps = append(
			resp.Steps, &common.UIAgentComponentStepRecord{
				StepID:    record.StepID,
				Index:     record.Index,
				Name:      record.Name,
				Thought:   record.Thought,
				Action:    record.Action,
				Status:    string(record.Status),
				Image:     record.Image,
				StartedAt: record.StartedAt,
				EndedAt:   record.EndedAt,
				CostTime:  record.CostTime,
			},
		)
	}

	return resp, nil
}
