package uiAgentComponent

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/clickPilot"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

type CreateUIAgentComponentReferenceConfigLogic struct {
	*BaseLogic
}

func NewCreateUIAgentComponentReferenceConfigLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateUIAgentComponentReferenceConfigLogic {
	return &CreateUIAgentComponentReferenceConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateUIAgentComponentReferenceConfigLogic) CreateUIAgentComponentReferenceConfig(req *types.CreateUIAgentComponentReferenceConfigReq) (
	resp *types.CreateUIAgentComponentReferenceConfigResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, req.ProjectId); err != nil {
		return nil, err
	}

	configID := utils.GenReferenceConfigID()
	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockUIAgentComponentProjectIDComponentIDPrefix, req.ProjectId, req.ComponentId,
	)
	fn := func() error {
		// validate the component_id in req
		origin, err := model.CheckUIAgentComponentByComponentID(
			l.ctx, l.svcCtx.UIAgentComponentModel, req.ProjectId, req.ComponentId,
		)
		if err != nil {
			return err
		}

		if origin.ReferenceId.Valid && origin.ReferenceId.String != "" {
			return errorx.Err(
				errorx.ProhibitedBehavior,
				"the ui agent component has already been bound to a reference configuration",
			)
		}

		origin.ReferenceId = sql.NullString{
			String: configID,
			Valid:  true,
		}

		return l.svcCtx.UIAgentComponentModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				var e error

				// 先创建再更新，更新失败时需要删除
				if e = l.svcCtx.ClickPilotClient.CreateRefCase(
					&clickPilot.RefCase{
						NewTaskID:    configID,
						SourceTaskID: req.ReferenceExecuteId,
						ActionIDs:    req.StepIds,
					},
				); e != nil {
					return e
				}

				defer func() {
					if e != nil {
						_ = l.svcCtx.ClickPilotClient.DeleteRefCase(configID)
					}
				}()

				if _, e = l.svcCtx.UIAgentComponentModel.Update(context, session, origin); e != nil {
					e = errors.Wrapf(
						errorx.Err(errorx.DBError, e.Error()),
						"failed to update values to table, table: %s, values: %s, error: %+v",
						l.svcCtx.UIAgentComponentModel.Table(), jsonx.MarshalIgnoreError(origin), e,
					)
				}

				return e
			},
		)
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	return &types.CreateUIAgentComponentReferenceConfigResp{
		ConfigId: configID,
	}, nil
}
