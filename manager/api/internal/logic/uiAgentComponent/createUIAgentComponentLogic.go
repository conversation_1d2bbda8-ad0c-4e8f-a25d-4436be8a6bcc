package uiAgentComponent

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateUIAgentComponentLogic struct {
	*BaseLogic
}

func NewCreateUIAgentComponentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateUIAgentComponentLogic {
	return &CreateUIAgentComponentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateUIAgentComponentLogic) CreateUIAgentComponent(req *types.CreateUIAgentComponentReq) (
	resp *types.CreateUIAgentComponentResp, err error,
) {
	in := &pb.CreateUIAgentComponentReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerUIAgentComponentRPC.CreateUIAgentComponent(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateUIAgentComponentResp{ComponentId: out.GetComponent().GetComponentId()}, nil
}
