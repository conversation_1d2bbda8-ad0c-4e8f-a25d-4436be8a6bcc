package logic

import (
	"crypto/md5"
	"fmt"
	"hash"
	"io"
	"os"
	"path/filepath"
	"strings"

	"github.com/emirpasic/gods/containers"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var (
	apiNodes  = ([]*types.Node)(nil)
	apiEdges  = ([]*types.Edge)(nil)
	apiCombos = ([]*types.Combo)(nil)
	rpcNodes  = ([]*pb.Node)(nil)
	rpcEdges  = ([]*pb.Edge)(nil)
	rpcCombos = ([]*pb.Combo)(nil)

	apiRelations = ([]*commontypes.Relation)(nil)
	rpcRelations = ([]*pb.Relation)(nil)
	apiRelation  = (*commontypes.Relation)(nil)
	rpcRelation  = (*pb.Relation)(nil)

	apiDocument = (*types.Document)(nil)
	rpcDocument = (*pb.Document)(nil)
	apiSchema   = (*types.Schema)(nil)
	rpcSchema   = (*pb.Schema)(nil)

	apiParameters = ([]*types.Parameter)(nil)
	apiReturns    = ([]*types.Return)(nil)
	rpcParameters = ([]*pb.Parameter)(nil)
	rpcReturns    = ([]*pb.Return)(nil)

	apiPerfKeepalive       = (*types.PerfKeepalive)(nil)
	rpcPerfKeepalive       = (*commonpb.PerfKeepalive)(nil)
	apiRateLimits          = ([]*commontypes.RateLimitV2)(nil)
	rpcRateLimits          = ([]*commonpb.RateLimitV2)(nil)
	apiPerfCaseStepExports = ([]*commontypes.ExportVariable)(nil)
	rpcPerfCaseStepExports = ([]*commonpb.PerfCaseStepV2_Export)(nil)
	apiStatsOfSteps        = ([]*types.StatsOfStep)(nil)
	rpcStatsOfSteps        = ([]*pb.StatsOfStep)(nil)
)

func ApiNodesToRpcNodes() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiNodes, rpcNodes, nil, nil)
}

func ApiEdgesToRpcEdges() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiEdges, rpcEdges, nil, nil)
}

func ApiCombosToRpcCombos() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiCombos, rpcCombos, nil, nil)
}

func ApiRelationsToRpcRelations() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiRelations, rpcRelations, nil, nil)
}

func ApiRelationToRpcRelation() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiRelation, rpcRelation, nil, nil)
}

func ApiDocumentToRpcDocument() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiDocument, rpcDocument, nil, nil)
}

func ApiSchemaToRpcSchema() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiSchema, rpcSchema, nil, nil)
}

func ApiPerfKeepaliveToRpcPerfKeepalive() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: apiPerfKeepalive,
		DstType: rpcPerfKeepalive,
		Fn: func(src any) (any, error) {
			v, ok := src.(*types.PerfKeepalive)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, apiPerfKeepalive)
			}

			return &commonpb.PerfKeepalive{
				Auth: &commonpb.PerfKeepalive_AuthConfig{
					RateLimit: &commonpb.RateLimit{
						TargetRps:    v.Auth.TargetRps,
						InitialRps:   v.Auth.InitialRps,
						StepHeight:   v.Auth.StepHeight,
						StepDuration: v.Auth.StepDuration,
					},
				},
				Heartbeat: &commonpb.PerfKeepalive_HeartbeatConfig{
					RateLimit: &commonpb.RateLimit{
						TargetRps:    v.Heartbeat.TargetRps,
						InitialRps:   v.Heartbeat.InitialRps,
						StepHeight:   v.Heartbeat.StepHeight,
						StepDuration: v.Heartbeat.StepDuration,
					},
					Interval: v.Heartbeat.Interval,
				},
			}, nil
		},
	}
}

func ApiRateLimitsToRpcRateLimits() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiRateLimits, rpcRateLimits, nil, nil)
}

func ApiStatsOfStepsToRpcStatsOfSteps() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiStatsOfSteps, rpcStatsOfSteps, nil, nil)
}

func RpcNodesToApiNodes() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(
		rpcNodes, apiNodes, func(v any) (string, error) {
			return protobuf.MarshalJSONWithMessagesToStringWithOptions(
				v, protobuf.ProtoJSONMarshalOptions{
					MarshalOptions: protojson.MarshalOptions{
						AllowPartial:    true,
						UseEnumNumbers:  true,
						EmitUnpopulated: true,
					},
					NilSliceToEmptySlice: true,
				},
			)
		}, nil,
	)
}

func RpcEdgesToApiEdges() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcEdges, apiEdges, nil, nil)
}

func RpcCombosToApiCombos() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcCombos, apiCombos, nil, nil)
}

func RpcRelationsToApiRelations() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcRelations, apiRelations, nil, nil)
}

func RpcRelationToApiRelation() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcRelation, apiRelation, nil, nil)
}

func RpcDocumentToApiDocument() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcDocument, apiDocument, nil, nil)
}

func RpcSchemaToApiSchema() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcSchema, apiSchema, nil, nil)
}

func RpcParametersToApiParameters() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcParameters, apiParameters, nil, nil)
}

func RpcReturnsToApiReturns() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcReturns, apiReturns, nil, nil)
}

func RpcPerfKeepaliveToApiPerfKeepalive() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: rpcPerfKeepalive,
		DstType: apiPerfKeepalive,
		Fn: func(src any) (any, error) {
			v, ok := src.(*commonpb.PerfKeepalive)
			if !ok {
				return nil, errors.Errorf("source type[%T] is not matching with [%T]", src, rpcPerfKeepalive)
			}

			a := v.GetAuth().GetRateLimit()
			h := v.GetHeartbeat().GetRateLimit()
			return &types.PerfKeepalive{
				Auth: types.AuthConfig{
					RateLimit: commontypes.RateLimit{
						TargetRps:    a.GetTargetRps(),
						InitialRps:   a.GetInitialRps(),
						StepHeight:   a.GetStepHeight(),
						StepDuration: a.GetStepDuration(),
					},
				},
				Heartbeat: types.HeartbeatConfig{
					RateLimit: commontypes.RateLimit{
						TargetRps:    h.GetTargetRps(),
						InitialRps:   h.GetInitialRps(),
						StepHeight:   h.GetStepHeight(),
						StepDuration: h.GetStepDuration(),
					},
					Interval: v.GetHeartbeat().GetInterval(),
				},
			}, nil
		},
	}
}

func RpcRateLimitsToApiRateLimits() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcRateLimits, apiRateLimits, nil, nil)
}

func RpcPerfCaseStepExportsToApiPerfCaseStepExports() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcPerfCaseStepExports, apiPerfCaseStepExports, nil, nil)
}

func RpcStatsOfStepsToApiStatsOfSteps() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcStatsOfSteps, apiStatsOfSteps, nil, nil)
}

func StringToFunctionType() utils.TypeConverter {
	return utils.StringToPBEnum(pb.FunctionType_BUILTIN)
}

func StringToCodeLanguage() utils.TypeConverter {
	return utils.StringToPBEnum(pb.CodeLanguage_GOLANG)
}

func FunctionTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(pb.FunctionType_BUILTIN)
}

func CodeLanguageToString() utils.TypeConverter {
	return utils.PBEnumToString(pb.CodeLanguage_GOLANG)
}

func StringToParameterOrReturnType() utils.TypeConverter {
	return utils.StringToPBEnum(pb.ParameterOrReturnType_STRING)
}

func ParameterOrReturnTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(pb.ParameterOrReturnType_STRING)
}

// Deprecated: Use `commonpb.StringToTriggerMode` instead.
func StringToTriggerMode() utils.TypeConverter {
	return utils.StringToPBEnum(commonpb.TriggerMode_NULL)
}

// Deprecated: Use `commonpb.TriggerModeToString` instead.
func TriggerModeToString() utils.TypeConverter {
	return utils.PBEnumToString(commonpb.TriggerMode_NULL)
}

// Deprecated: Use `commonpb.StringToPurposeType` instead.
func StringToPurposeType() utils.TypeConverter {
	return utils.StringToPBEnum(commonpb.PurposeType_UNDEFINED)
}

// Deprecated: Use `commonpb.PurposeTypeToString` instead.
func PurposeTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(commonpb.PurposeType_UNDEFINED)
}

func StringToNotifyMode() utils.TypeConverter {
	return utils.StringToPBEnum(pb.NotifyMode_ALWAYS_NOTIFY)
}

func NotifyModeToString() utils.TypeConverter {
	return utils.PBEnumToString(pb.NotifyMode_ALWAYS_NOTIFY)
}

func StringToNotifyType() utils.TypeConverter {
	return utils.StringToPBEnum(pb.NotifyType_LARK_GROUP)
}

func NotifyTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(pb.NotifyType_LARK_GROUP)
}

func StringToSceneType() utils.TypeConverter {
	return utils.StringToPBEnum(pb.SceneType_ST_NULL)
}

func SceneTypeToString() utils.TypeConverter {
	return utils.PBEnumToString(pb.SceneType_ST_NULL)
}

func StringToResourceState() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: common.ConstResourceStateEnabled,
		DstType: pb.ResourceState_RS_NULL,
		Fn: func(src any) (any, error) {
			s, ok := src.(common.ResourceState)
			if !ok {
				return pb.ResourceState_RS_NULL, errors.Errorf(
					"source type[%T] is not matching with [%T]", src, common.ConstResourceStateEnabled,
				)
			}

			enum, err := protobuf.GetEnumByString(pb.ResourceState_RS_NULL, string(s))
			if err != nil {
				return pb.ResourceState_RS_NULL, errors.Errorf("failed to get enum by src[%s]", s)
			}

			return enum, nil
		},
	}
}

func ResourceStateToString() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: pb.ResourceState_RS_NULL,
		DstType: common.ConstResourceStateEnabled,
		Fn: func(src any) (any, error) {
			s, ok := src.(pb.ResourceState)
			if !ok {
				return common.ResourceState(""), errors.Errorf(
					"source type[%T] is not matching with [%T]", src, pb.ResourceState_RS_NULL,
				)
			}

			return s.ConvertTo(), nil
		},
	}
}

func StringToReviewStatus() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: common.ConstReviewStatusPending,
		DstType: pb.ReviewStatus_REVIEW_STATUS_NULL,
		Fn: func(src any) (any, error) {
			s, ok := src.(common.ReviewStatus)
			if !ok {
				return pb.ReviewStatus_REVIEW_STATUS_NULL, errors.Errorf(
					"source type[%T] is not matching with [%T]", src, common.ConstReviewStatusPending,
				)
			}

			enum, err := protobuf.GetEnumByString(pb.ReviewStatus_REVIEW_STATUS_NULL, string(s))
			if err != nil {
				return pb.ReviewStatus_REVIEW_STATUS_NULL, errors.Errorf("failed to get enum by src[%s]", s)
			}

			return enum, nil
		},
	}
}

func ReviewStatusToString() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: pb.ReviewStatus_REVIEW_STATUS_NULL,
		DstType: common.ConstReviewStatusPending,
		Fn: func(src any) (any, error) {
			s, ok := src.(pb.ReviewStatus)
			if !ok {
				return common.ReviewStatus(""), errors.Errorf(
					"source type[%T] is not matching with [%T]", src, pb.ReviewStatus_REVIEW_STATUS_NULL,
				)
			}

			return s.ConvertTo(), nil
		},
	}
}

func StringToReviewResourceType() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: common.ConstReviewResourceTypeComponentGroup,
		DstType: pb.ReviewResourceType_RRT_NULL,
		Fn: func(src any) (any, error) {
			s, ok := src.(common.ReviewResourceType)
			if !ok {
				return pb.ReviewResourceType_RRT_NULL, errors.Errorf(
					"source type[%T] is not matching with [%T]", src, common.ConstReviewResourceTypeComponentGroup,
				)
			}

			enum, err := protobuf.GetEnumByString(pb.ReviewResourceType_RRT_NULL, string(s))
			if err != nil {
				return pb.ReviewResourceType_RRT_NULL, errors.Errorf("failed to get enum by src[%s]", s)
			}

			return enum, nil
		},
	}
}

func ReviewResourceTypeToString() utils.TypeConverter {
	return utils.TypeConverter{
		SrcType: pb.ReviewResourceType_RRT_NULL,
		DstType: common.ConstReviewResourceTypeComponentGroup,
		Fn: func(src any) (any, error) {
			s, ok := src.(pb.ReviewResourceType)
			if !ok {
				return common.ReviewResourceType(""), errors.Errorf(
					"source type[%T] is not matching with [%T]", src, pb.ReviewResourceType_RRT_NULL,
				)
			}

			return s.ConvertTo(), nil
		},
	}
}

func ContainerToString(container containers.Container) string {
	values := make([]string, 0, container.Size())
	for _, value := range container.Values() {
		values = append(values, fmt.Sprintf("%v", value))
	}

	return "[" + strings.Join(values, ", ") + "]"
}

func SaveFile(src io.ReadSeeker, targetPath string) (hash.Hash, int64, error) {
	defer func() {
		_, _ = src.Seek(0, io.SeekStart)
	}()

	dir := filepath.Dir(targetPath)
	if err := os.MkdirAll(dir, 0o755); err != nil {
		return nil, 0, errors.Wrapf(
			errorx.Err(errorx.FileOperationFailure, err.Error()),
			"failed to create directory, path: %s, error: %+v",
			dir, err,
		)
	}

	file, err := os.Create(targetPath)
	if err != nil {
		return nil, 0, errors.Wrapf(
			errorx.Err(errorx.FileOperationFailure, err.Error()),
			"failed to create file, file: %s, error: %+v",
			targetPath, err,
		)
	}
	defer func(f *os.File) {
		if f != nil {
			_ = f.Close()
		}
	}(file)

	md5Hash := md5.New()
	writer := io.MultiWriter(file, md5Hash)
	size, err := io.Copy(writer, src)
	if err != nil {
		return nil, size, errors.Wrapf(
			errorx.Err(errorx.FileOperationFailure, err.Error()),
			"failed to copy file, error: %+v",
			err,
		)
	}

	return md5Hash, size, nil
}
