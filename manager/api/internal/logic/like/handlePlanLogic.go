package like

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type HandlePlanLogic struct {
	*BaseLogic
}

func NewHandlePlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *HandlePlanLogic {
	return &HandlePlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *HandlePlanLogic) HandlePlan(req *types.LikePlanReq) (resp *types.LikePlanResp, err error) {
	in := &pb.HandleLikePlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerPlanCommonServiceRPC.HandleLikePlan(l.ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.LikePlanResp{}

	return resp, nil
}
