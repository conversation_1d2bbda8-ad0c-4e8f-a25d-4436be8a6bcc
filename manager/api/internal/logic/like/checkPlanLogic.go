package like

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CheckPlanLogic struct {
	*BaseLogic
}

func NewCheckPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CheckPlanLogic {
	return &CheckPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CheckPlanLogic) CheckPlan(req *types.CheckLikePlanReq) (resp *types.CheckLikePlanResp, err error) {
	in := &pb.CheckLikePlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerPlanCommonServiceRPC.CheckLikePlan(l.ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.CheckLikePlanResp{}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
