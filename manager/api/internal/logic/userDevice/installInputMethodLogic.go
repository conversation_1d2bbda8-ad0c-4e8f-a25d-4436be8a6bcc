package userDevice

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/device"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

type InstallInputMethodLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	checkLogic *CheckInputMethodLogic
}

func NewInstallInputMethodLogic(ctx context.Context, svcCtx *svc.ServiceContext) *InstallInputMethodLogic {
	return &InstallInputMethodLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		checkLogic: NewCheckInputMethodLogic(ctx, svcCtx),
	}
}

func (l *InstallInputMethodLogic) InstallInputMethod(req *types.InstallInputMethodReq) (
	resp *types.InstallInputMethodResp, err error,
) {
	if req.PlatformType == int8(commonpb.PlatformType_IOS) {
		return nil, errorx.Err(errorx.ProhibitedBehavior, "does not support installing input method on iOS devices")
	}

	var inputMethod, appPath string
	if req.InputMethodFile != nil {
		if req.InputMethod == "" {
			return nil, errorx.Err(
				errorx.ValidateParamError, "input method id is required when input method file is provided",
			)
		}
		inputMethod = req.InputMethod

		// save the file to the local file system
		file, err := os.CreateTemp("", "ime-*.apk")
		if err != nil {
			return nil, errorx.Errorf(
				errorx.FileOperationFailure,
				"failed to create temp file, error: %+v",
				err,
			)
		}
		defer func() {
			if file != nil {
				// close and remove the temp file
				_ = file.Close()
				_ = os.Remove(file.Name())
			}
		}()

		if _, err = io.Copy(file, req.InputMethodFile); err != nil {
			return nil, errorx.Errorf(
				errorx.FileOperationFailure,
				"failed to copy file to temp file, src: %s, error: %+v",
				req.InputMethodFileHeader.Filename, err,
			)
		}

		appPath = file.Name()
	} else {
		pwd, err := os.Getwd()
		if err != nil {
			return nil, errorx.Errorf(
				errorx.FileOperationFailure,
				"failed to get current working directory, error: %+v",
				err,
			)
		}

		inputMethod = imeOfADBKeyboard
		appPath = filepath.Join(pwd, defaultDirNameOfTools, defaultInputMethodApk)
	}
	if !utils.Exists(appPath) {
		return nil, errorx.Errorf(errorx.NotExists, "input method apk not found: %s", appPath)
	}

	d, err := device.NewAndroidDevice(l.ctx, commonpb.DeviceType(req.DeviceType), req.UDID, req.RemoteAddress)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DeviceOperationFailure, err.Error()),
			"failed to create android device, error: %+v",
			err,
		)
	}
	defer func() {
		if d != nil {
			_ = d.Close()
		}
	}()

	var (
		timer   *time.Timer
		timeout = common.ConstInstallInputMethodTimeout
	)
	if deadline, ok := l.ctx.Deadline(); ok {
		if duration := time.Until(deadline); duration > defaultInterval {
			timeout = duration - defaultInterval
		} else {
			timeout = duration
		}
	}
	timer = time.NewTimer(timeout)
	defer timer.Stop()

	resultCh := make(chan error, 1)
	threading.GoSafeCtx(
		l.ctx, func() {
			resultCh <- l.installApp(d, appPath)
		},
	)

	select {
	case <-l.ctx.Done():
		return nil, errorx.Errorf(
			errorx.DeviceOperationFailure,
			"install input method canceled, error: %s",
			l.ctx.Err(),
		)
	case <-timer.C:
		return nil, errorx.Errorf(
			errorx.DeviceOperationFailure,
			"install input method timeout, timeout: %s",
			timeout,
		)
	case err = <-resultCh:
		if err != nil {
			return nil, err
		}

		r, e := l.checkLogic.CheckInputMethod(
			&types.CheckInputMethodReq{
				DeviceType:    req.DeviceType,
				PlatformType:  req.PlatformType,
				UDID:          req.UDID,
				RemoteAddress: req.RemoteAddress,
				InputMethod:   inputMethod,
			},
		)
		if e != nil {
			return nil, e
		} else if !r.Installed {
			// 安装后没有找到目标输入法
			return nil, errorx.Errorf(
				errorx.DeviceOperationFailure,
				"not found the target input method after installation, ime: %s, installed: %t",
				inputMethod, r.Installed,
			)
		}

		return &types.InstallInputMethodResp{}, nil
	}
}

func (l *InstallInputMethodLogic) installApp(d *device.AndroidDevice, appPath string) error {
	var (
		adb    = d.ADB()
		serial = d.UDID()
	)

	// push the apk to the device
	pathOnDevice := filepath.Join(device.TempPathOnAndroid, defaultNameOfApk)
	if err := adb.PushFile(appPath, pathOnDevice); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DeviceOperationFailure, err.Error()),
			"failed to push file to device, serial: %s, file: %s, path: %s, error: %+v",
			serial, appPath, pathOnDevice, err,
		)
	}
	defer func() {
		// remove the apk from the device
		_, _ = adb.RunShellCommand(fmt.Sprintf(cmdOfRemoveFile, pathOnDevice))
	}()

	cmd := fmt.Sprintf(cmdOfInstallApk, pathOnDevice)
	output, err := adb.RunShellCommand(cmd)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DeviceOperationFailure, err.Error()),
			"failed to install the apk, serial: %s, cmd: %q, error: %+v",
			serial, cmd, err,
		)
	}

	if d.DeviceType() == commonpb.DeviceType_REAL_PHONE {
		// sleep for 3 seconds to avoid missing the pop-up window after installing the app.
		time.Sleep(3 * time.Second)
	}
	l.Infof("finish to install the apk, serial: %s, cmd: %q, result: %s", serial, cmd, output)
	return nil
}
