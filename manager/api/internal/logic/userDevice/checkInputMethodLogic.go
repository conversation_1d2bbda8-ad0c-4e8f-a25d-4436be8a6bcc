package userDevice

import (
	"context"
	"slices"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/device"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

var builtinInputMethods = []string{
	imeOfATX,
	imeOfADBKeyboard,
}

type CheckInputMethodLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCheckInputMethodLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CheckInputMethodLogic {
	return &CheckInputMethodLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CheckInputMethodLogic) CheckInputMethod(req *types.CheckInputMethodReq) (
	resp *types.CheckInputMethodResp, err error,
) {
	if req.PlatformType == int8(commonpb.PlatformType_IOS) {
		return nil, errorx.Err(errorx.ProhibitedBehavior, "does not support checking input method on iOS devices")
	}

	d, err := device.NewAndroidDevice(l.ctx, commonpb.DeviceType(req.DeviceType), req.UDID, req.RemoteAddress)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DeviceOperationFailure, err.Error()),
			"failed to create android device, error: %+v",
			err,
		)
	}
	defer func() {
		if d != nil {
			_ = d.Close()
		}
	}()

	inputMethods, err := d.GetAllIME()
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DeviceOperationFailure, err.Error()),
			"failed to get all input methods, error: %+v",
			err,
		)
	}

	var (
		installed          bool
		targetInputMethods = builtinInputMethods
	)
	if req.InputMethod != "" {
		targetInputMethods = []string{req.InputMethod}
	}
	for _, inputMethod := range inputMethods {
		if installed = slices.Contains(targetInputMethods, inputMethod); installed {
			break
		}
	}

	return &types.CheckInputMethodResp{Installed: installed}, nil
}
