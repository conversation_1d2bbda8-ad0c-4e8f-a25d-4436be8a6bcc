package category

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type MoveCategoryTreeLogic struct {
	*BaseLogic
}

func NewMoveCategoryTreeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MoveCategoryTreeLogic {
	return &MoveCategoryTreeLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// MoveCategoryTree the category tree
/*
如，当前的模块树如下：

         0
       /  \
      1    7
    / | \   \
   2  3  4   8
        / \
       5   6
            \
             9

把`6`及其子树移动到`1`下的`2`的后面时，
1. 跟`6`同层且排在其后面的全部向前移动一位
2. 删除 0-6, 0-9, 1-6, 1-9, 4-6, 4-9（不删除 6-6, 6-9, 9-9）
3. 新增 0-6, 0-9, 1-6, 1-9（注意距离的变化以及 1-6 的序号）（加上上面没有删除的 6-6, 6-9, 9-9）
4. 跟`6`同层且排在其后面的全部向后移动一位
*/
func (l *MoveCategoryTreeLogic) MoveCategoryTree(req *types.MoveCategoryTreeReq) (
	resp *types.MoveCategoryTreeResp, err error,
) {
	in := &pb.MoveCategoryTreeReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerCategoryRPC.MoveCategoryTree(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.MoveCategoryTreeResp{}, nil
}
