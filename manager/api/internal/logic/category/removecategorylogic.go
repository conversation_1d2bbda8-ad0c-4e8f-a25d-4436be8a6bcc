package category

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveCategoryLogic struct {
	*BaseLogic
}

func NewRemoveCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveCategoryLogic {
	return &RemoveCategoryLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveCategoryLogic) RemoveCategory(req *types.RemoveCategoryReq) (resp *types.RemoveCategoryResp, err error) {
	in := &pb.RemoveCategoryReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerCategoryRPC.RemoveCategory(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemoveCategoryResp{}, nil
}
