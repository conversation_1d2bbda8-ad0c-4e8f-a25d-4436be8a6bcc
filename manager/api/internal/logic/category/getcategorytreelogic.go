package category

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetCategoryTreeLogic struct {
	*BaseLogic
}

func NewGetCategoryTreeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCategoryTreeLogic {
	return &GetCategoryTreeLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetCategoryTreeLogic) GetCategoryTree(req *types.GetCategoryTreeReq) (
	resp *types.GetCategoryTreeResp, err error,
) {
	in := &pb.GetCategoryTreeReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerCategoryRPC.GetCategoryTree(
		l.ctx, in,
		zrpc.WithCallTimeout(common.ConstGetCategoryTreeTimeout),
		grpc.MaxCallRecvMsgSize(common.ConstMaxRecvSize),
	)
	if err != nil {
		return nil, err
	}

	resp = &types.GetCategoryTreeResp{}
	if err = utils.Copy(resp, out.GetCategoryTree(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
