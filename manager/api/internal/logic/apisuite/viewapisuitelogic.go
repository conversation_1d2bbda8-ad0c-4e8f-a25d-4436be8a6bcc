package apisuite

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewApiSuiteLogic struct {
	*BaseLogic
}

func NewViewApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewApiSuiteLogic {
	return &ViewApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewApiSuiteLogic) ViewApiSuite(req *types.ViewApiSuiteReq) (resp *types.ViewApiSuiteResp, err error) {
	in := &pb.ViewApiSuiteReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerApiSuiteRPC.ViewApiSuite(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewApiSuiteResp{ApiSuite: &types.ApiSuite{Tags: []string{}}}
	if err = utils.Copy(resp.ApiSuite, out.Suite, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
