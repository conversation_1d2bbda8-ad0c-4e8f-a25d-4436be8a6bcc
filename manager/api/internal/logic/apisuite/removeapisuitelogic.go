package apisuite

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveApiSuiteLogic struct {
	*BaseLogic
}

func NewRemoveApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveApiSuiteLogic {
	return &RemoveApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveApiSuiteLogic) RemoveApiSuite(req *types.RemoveApiSuiteReq) (resp *types.RemoveApiSuiteResp, err error) {
	in := &pb.RemoveApiSuiteReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerApiSuiteRPC.RemoveApiSuite(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemoveApiSuiteResp{}, nil
}
