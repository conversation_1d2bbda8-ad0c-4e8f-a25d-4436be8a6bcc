package apisuite

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchServiceCaseNotInApiSuiteLogic struct {
	*BaseLogic
}

// search service cases not in api suite
func NewSearchServiceCaseNotInApiSuiteLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchServiceCaseNotInApiSuiteLogic {
	return &SearchServiceCaseNotInApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchServiceCaseNotInApiSuiteLogic) SearchServiceCaseNotInApiSuite(req *types.SearchServiceCaseNotInApiSuiteReq) (
	resp *types.SearchServiceCaseNotInApiSuiteResp, err error,
) {
	in := &pb.SearchServiceCaseNotInApiSuiteReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerApiSuiteRPC.SearchServiceCaseNotInApiSuite(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchServiceCaseNotInApiSuiteResp{Items: []*types.SearchCaseInApiSuiteItem{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
