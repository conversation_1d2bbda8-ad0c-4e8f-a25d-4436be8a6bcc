package notify

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyNotifyLogic struct {
	*BaseLogic
}

func NewModifyNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyNotifyLogic {
	return &ModifyNotifyLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyNotifyLogic) ModifyNotify(req *types.ModifyNotifyReq) (resp *types.ModifyNotifyResp, err error) {
	in := &pb.ModifyPlanNotifyReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	_, err = l.svcCtx.ManagerNotifyRPC.ModifyPlanNotify(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyNotifyResp{}, nil
}
