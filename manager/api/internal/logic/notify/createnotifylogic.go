package notify

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateNotifyLogic struct {
	*BaseLogic
}

func NewCreateNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateNotifyLogic {
	return &CreateNotifyLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateNotifyLogic) CreateNotify(req *types.CreateNotifyReq) (resp *types.CreateNotifyResp, err error) {
	in := &pb.CreatePlanNotifyReq{}

	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerNotifyRPC.CreatePlanNotify(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateNotifyResp{NotifyIds: out.GetIds()}, nil
}
