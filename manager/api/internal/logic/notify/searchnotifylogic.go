package notify

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchNotifyLogic struct {
	*BaseLogic
}

func NewSearchNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchNotifyLogic {
	return &SearchNotifyLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchNotifyLogic) SearchNotify(req *types.SearchNotifyReq) (resp *types.SearchNotifyResp, err error) {
	in := &pb.SearchPlanNotifyReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	out, err := l.svcCtx.ManagerNotifyRPC.SearchPlanNotify(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.SearchNotifyResp{Items: []*types.Notify{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v", out, err)
	}

	return resp, nil
}
