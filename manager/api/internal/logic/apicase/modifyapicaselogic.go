package apicase

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyApiCaseLogic struct {
	*BaseLogic
}

func NewModifyApiCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyApiCaseLogic {
	return &ModifyApiCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyApiCaseLogic) ModifyApiCase(req *types.ModifyApiCaseReq) (resp *types.ModifyApiCaseResp, err error) {
	in := &pb.ModifyApiCaseReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	_, err = l.svcCtx.ManagerApiCaseRPC.ModifyApiCase(l.ctx, in, zrpc.WithCallTimeout(common.ConstModifyApiCaseTimeout))
	if err != nil {
		return nil, err
	}

	return &types.ModifyApiCaseResp{}, nil
}
