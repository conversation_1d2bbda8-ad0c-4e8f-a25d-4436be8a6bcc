package apicase

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateApiCaseLogic struct {
	*BaseLogic
}

func NewCreateApiCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateApiCaseLogic {
	return &CreateApiCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateApiCaseLogic) CreateApiCase(req *types.CreateApiCaseReq) (resp *types.CreateApiCaseResp, err error) {
	in := &pb.CreateApiCaseReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerApiCaseRPC.CreateApiCase(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateApiCaseResp{CaseId: out.GetCase().GetCaseId()}, nil
}
