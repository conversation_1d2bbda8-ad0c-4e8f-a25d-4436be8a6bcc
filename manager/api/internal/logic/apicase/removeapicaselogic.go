package apicase

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveApiCaseLogic struct {
	*BaseLogic
}

func NewRemoveApiCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveApiCaseLogic {
	return &RemoveApiCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveApiCaseLogic) RemoveApiCase(req *types.RemoveApiCaseReq) (resp *types.RemoveApiCaseResp, err error) {
	in := &pb.RemoveApiCaseReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	_, err = l.svcCtx.ManagerApiCaseRPC.RemoveApiCase(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemoveApiCaseResp{}, nil
}
