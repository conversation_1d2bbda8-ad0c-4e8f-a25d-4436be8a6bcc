package apicase

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewApiCaseLogic struct {
	*BaseLogic
}

func NewViewApiCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewApiCaseLogic {
	return &ViewApiCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewApiCaseLogic) ViewApiCase(req *types.ViewApiCaseReq) (resp *types.ViewApiCaseResp, err error) {
	in := &pb.ViewApiCaseReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerApiCaseRPC.ViewApiCase(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewApiCaseResp{
		ApiCase: &types.ApiCase{
			Tags: []string{}, Nodes: []*types.Node{}, Edges: []*types.Edge{}, Combos: []*types.Combo{},
		},
	}
	if err = utils.Copy(resp.ApiCase, out.Case, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
