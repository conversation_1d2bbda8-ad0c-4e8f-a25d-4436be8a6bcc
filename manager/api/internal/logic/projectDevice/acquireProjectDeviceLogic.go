package projectDevice

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type AcquireProjectDeviceLogic struct {
	*BaseLogic
}

func NewAcquireProjectDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AcquireProjectDeviceLogic {
	return &AcquireProjectDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *AcquireProjectDeviceLogic) AcquireProjectDevice(req *types.AcquireProjectDeviceReq) (
	resp *types.AcquireProjectDeviceResp, err error,
) {
	in := &pb.AcquireProjectDeviceByUDIDReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerProjectDeviceRPC.AcquireProjectDeviceByUDID(
		l.ctx, in, zrpc.WithCallTimeout(common.ConstAcquireProjectDeviceTimeout),
	)
	if err != nil {
		return nil, err
	}

	resp = &types.AcquireProjectDeviceResp{ProjectDevice: &types.ProjectDevice{}}
	if err = utils.Copy(resp.ProjectDevice, out.GetDevice(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(out), err,
		)
	}

	return resp, nil
}
