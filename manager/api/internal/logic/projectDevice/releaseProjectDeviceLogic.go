package projectDevice

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ReleaseProjectDeviceLogic struct {
	*BaseLogic
}

func NewReleaseProjectDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReleaseProjectDeviceLogic {
	return &ReleaseProjectDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ReleaseProjectDeviceLogic) ReleaseProjectDevice(req *types.ReleaseProjectDeviceReq) (
	resp *types.ReleaseProjectDeviceResp, err error,
) {
	in := &pb.ReleaseProjectDeviceReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.ManagerProjectDeviceRPC.ReleaseProjectDevice(
		l.ctx, in, zrpc.WithCallTimeout(common.ConstReleaseProjectDeviceTimeout),
	)
	if err != nil {
		return nil, err
	}

	return &types.ReleaseProjectDeviceResp{}, nil
}
