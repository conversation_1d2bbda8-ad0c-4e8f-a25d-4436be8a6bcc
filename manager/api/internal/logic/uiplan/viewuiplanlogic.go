package uiplan

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewUiPlanLogic struct {
	*BaseLogic
}

func NewViewUiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewUiPlanLogic {
	return &ViewUiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ViewUiPlanLogic) ViewUiPlan(req *types.ViewUiPlanReq) (resp *types.ViewUiPlanResp, err error) {
	in := &pb.ViewUiPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	out, err := l.svcCtx.ManagerUiPlanRPC.ViewUiPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ViewUiPlanResp{UiPlan: &types.UiPlan{}}
	if err = utils.Copy(resp.UiPlan, out.Plan, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v", out, err)
	}

	return resp, nil
}
