package uiplan

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

type RemoveUiPlanLogic struct {
	*BaseLogic
}

func NewRemoveUiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveUiPlanLogic {
	return &RemoveUiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RemoveUiPlanLogic) RemoveUiPlan(req *types.RemoveUiPlanReq) (resp *types.RemoveUiPlanResp, err error) {
	in := &pb.RemoveUiPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	_, err = l.svcCtx.ManagerUiPlanRPC.RemoveUiPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.RemoveUiPlanResp{}, nil
}
