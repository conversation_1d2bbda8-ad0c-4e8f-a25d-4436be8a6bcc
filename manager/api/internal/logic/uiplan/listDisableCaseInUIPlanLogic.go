package uiplan

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ListDisableCaseInUIPlanLogic struct {
	*BaseLogic
}

func NewListDisableCaseInUIPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDisableCaseInUIPlanLogic {
	return &ListDisableCaseInUIPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ListDisableCaseInUIPlanLogic) ListDisableCaseInUIPlan(req *types.ListDisableCaseInUIPlanReq) (
	resp *types.ListDisableCaseInUIPlanResp, err error,
) {
	in := &pb.ListDisableCaseInUIPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%s] to request, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerUiPlanRPC.ListDisableCaseInUIPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.ListDisableCaseInUIPlanResp{
		Items: make([]*types.SearchCaseInUIPlanItem, 0),
	}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%s] to response, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
