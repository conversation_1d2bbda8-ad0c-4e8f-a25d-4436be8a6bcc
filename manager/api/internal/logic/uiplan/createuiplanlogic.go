package uiplan

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

type CreateUiPlanLogic struct {
	*BaseLogic
}

func NewCreateUiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateUiPlanLogic {
	return &CreateUiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateUiPlanLogic) CreateUiPlan(req *types.CreateUiPlanReq) (resp *types.CreateUiPlanResp, err error) {
	in := &pb.CreateUiPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerUiPlanRPC.CreateUiPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateUiPlanResp{PlanId: out.GetPlan().GetPlanId()}, nil
}
