package uiplan

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetCaseTreeOfUIPlanLogic struct {
	*BaseLogic
}

func NewGetCaseTreeOfUIPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCaseTreeOfUIPlanLogic {
	return &GetCaseTreeOfUIPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetCaseTreeOfUIPlanLogic) GetCaseTreeOfUIPlan(req *types.GetCaseTreeOfUIPlanReq) (
	resp *types.GetCaseTreeOfUIPlanResp, err error,
) {
	in := &pb.GetCaseTreeOfUIPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%s] to request, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerUiPlanRPC.GetCaseTreeOfUIPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.GetCaseTreeOfUIPlanResp{}
	if err = utils.Copy(resp, out.GetCaseTree(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%s] to response, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
