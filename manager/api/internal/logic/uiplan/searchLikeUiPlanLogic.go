package uiplan

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchLikeUiPlanLogic struct {
	*BaseLogic
}

func NewSearchLikeUiPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchLikeUiPlanLogic {
	return &SearchLikeUiPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchLikeUiPlanLogic) SearchLikeUiPlan(req *types.SearchUiPlanReq) (resp *types.SearchUiPlanResp, err error) {
	in := &pb.SearchUiPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerUiPlanRPC.SearchLikeUiPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.SearchUiPlanResp{Items: []*types.UiPlan{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
