package uiplan

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyCaseListOfUIPlanLogic struct {
	*BaseLogic
}

func NewModifyCaseListOfUIPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyCaseListOfUIPlanLogic {
	return &ModifyCaseListOfUIPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyCaseListOfUIPlanLogic) ModifyCaseListOfUIPlan(req *types.ModifyCaseListOfUIPlanReq) (
	resp *types.ModifyCaseListOfUIPlanResp, err error,
) {
	if req.OperationType == common.ConstListOperatorTypeAdd {
		in := &pb.AddCaseToUIPlanReq{}
		if err = utils.Copy(in, req, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy data[%s] to request, error: %+v",
				jsonx.MarshalIgnoreError(req), err,
			)
		}

		_, err = l.svcCtx.ManagerUiPlanRPC.AddCaseToUIPlan(l.ctx, in)
	} else if req.OperationType == common.ConstListOperatorTypeRemove {
		in := &pb.RemoveCaseFromUIPlanReq{}
		if err = utils.Copy(in, req, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy data[%s] to request, error: %+v",
				jsonx.MarshalIgnoreError(req), err,
			)
		}

		_, err = l.svcCtx.ManagerUiPlanRPC.RemoveCaseFromUIPlan(l.ctx, in)
	} else {
		err = errors.WithStack(
			errorx.Errorf(
				errorx.DoesNotSupport, "the operator type[%s] doesn't support", req.OperationType,
			),
		)
	}
	if err != nil {
		return nil, err
	}

	return &types.ModifyCaseListOfUIPlanResp{}, nil
}
