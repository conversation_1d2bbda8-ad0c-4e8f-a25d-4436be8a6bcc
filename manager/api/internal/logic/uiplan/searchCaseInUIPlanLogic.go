package uiplan

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchCaseInUIPlanLogic struct {
	*BaseLogic
}

func NewSearchCaseInUIPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchCaseInUIPlanLogic {
	return &SearchCaseInUIPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchCaseInUIPlanLogic) SearchCaseInUIPlan(req *types.SearchCaseInUIPlanReq) (
	resp *types.SearchCaseInUIPlanResp, err error,
) {
	in := &pb.SearchCaseInUIPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%s] to request, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerUiPlanRPC.SearchCaseInUIPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchCaseInUIPlanResp{Items: []*types.SearchCaseInUIPlanItem{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%s] to response, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
