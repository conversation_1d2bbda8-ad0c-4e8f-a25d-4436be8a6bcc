package uiplan

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetCaseTreeOfNotAddedToUIPlanLogic struct {
	*BaseLogic
}

func NewGetCaseTreeOfNotAddedToUIPlanLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *GetCaseTreeOfNotAddedToUIPlanLogic {
	return &GetCaseTreeOfNotAddedToUIPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetCaseTreeOfNotAddedToUIPlanLogic) GetCaseTreeOfNotAddedToUIPlan(req *types.GetCaseTreeOfNotAddedToUIPlanReq) (
	resp *types.GetCaseTreeOfNotAddedToUIPlanResp, err error,
) {
	in := &pb.GetCaseTreeOfNotAddedToUIPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%s] to request, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerUiPlanRPC.GetCaseTreeOfNotAddedToUIPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.GetCaseTreeOfNotAddedToUIPlanResp{}
	if err = utils.Copy(resp, out.GetCaseTree(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%s] to response, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
