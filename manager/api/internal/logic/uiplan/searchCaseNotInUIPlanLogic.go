package uiplan

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchCaseNotInUIPlanLogic struct {
	*BaseLogic
}

func NewSearchCaseNotInUIPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchCaseNotInUIPlanLogic {
	return &SearchCaseNotInUIPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchCaseNotInUIPlanLogic) SearchCaseNotInUIPlan(req *types.SearchCaseNotInUIPlanReq) (
	resp *types.SearchCaseNotInUIPlanResp, err error,
) {
	in := &pb.SearchCaseNotInUIPlanReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%s] to request, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerUiPlanRPC.SearchCaseNotInUIPlan(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchCaseNotInUIPlanResp{Items: []*types.SearchCaseNotInUIPlanItem{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%s] to response, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
