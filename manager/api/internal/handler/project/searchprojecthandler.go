package project

import (
	"net/http"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/rest/httpx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/response"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/logic/project"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

func SearchProjectHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SearchProjectReq
		if err := httpx.Parse(r, &req); err != nil {
			response.MakeHttpResponse(r, w, nil, errors.Wrapf(errorx.Err(errorx.ParseParamError, err.Error()), "failed to parse parameters, error: %+v", err))
			return
		}

		if err := svcCtx.Validator.Validate.StructCtx(r.Context(), req); err != nil {
			response.MakeHttpResponse(r, w, nil, errors.Wrapf(errorx.Err(errorx.ValidateParamError, svcCtx.Validator.Translate(err)), "failed to validate parameters, error: %+v", err))
			return
		}

		// 2023-10-26 去除「搜索项目」接口的权限控制
		//filter := permissioncontrol.NewPermissionFilter(svcCtx.PermissionFunctionRPC.Client(), svcCtx.PermissionRoleRPC.Client())
		//permissioncontrol.PermissionControl(w, r, filter, "probe", pb.DomainType_Platform, "", func(w http.ResponseWriter, r *http.Request) {
		//	l := project.NewSearchProjectLogic(r.Context(), svcCtx)
		//	resp, err := l.SearchProject(&req)
		//	response.MakeHttpResponse(r, w, resp, err)
		//})

		l := project.NewSearchProjectLogic(r.Context(), svcCtx)
		resp, err := l.SearchProject(&req)
		response.MakeHttpResponse(r, w, resp, err)
	}
}
