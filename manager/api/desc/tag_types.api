syntax = "v1"

import "types.api"

type Tag {
    ProjectId   string        `json:"project_id"`
    Type        string        `json:"type"`
    TagId       string        `json:"tag_id"`
    Name        string        `json:"name"`
    Description string        `json:"description"`
    Status      int8          `json:"status"`
    CreatedBy   *FullUserInfo `json:"created_by"`
    UpdatedBy   *FullUserInfo `json:"updated_by"`
    CreatedAt   int64         `json:"created_at"`
    UpdatedAt   int64         `json:"updated_at"`
}

// 创建标签
type (
    CreateTagReq {
        ProjectId   string `json:"project_id" validate:"required" zh:"项目ID"`
        Type        string `json:"type" validate:"required,oneof=COMPONENT_GROUP CASE SUITE PLAN INTERFACE_DOCUMENT UI_AGENT" zh:"标签类型"`
        Name        string `json:"name" validate:"gte=1,lte=64" zh:"项目名称"`
        Description string `json:"description" validate:"lte=255" zh:"项目描述"`
    }
    CreateTagResp {
        TagId string `json:"tag_id"`
    }
)

// 删除标签
type (
    RemoveTagReq {
        ProjectId string   `json:"project_id" validate:"required" zh:"项目ID"`
        TagIds    []string `json:"tag_ids" validate:"gt=0" zh:"标签ID列表"`
    }
    RemoveTagResp {}
)

// 编辑标签
type (
    ModifyTagReq {
        ProjectId   string `json:"project_id" validate:"required" zh:"项目ID"`
        Type        string `json:"type" validate:"required,oneof=COMPONENT_GROUP CASE SUITE PLAN INTERFACE_DOCUMENT UI_AGENT" zh:"标签类型"`
        TagId       string `json:"tag_id" validate:"required" zh:"标签ID"`
        Name        string `json:"name" validate:"gte=1,lte=64" zh:"项目名称"`
        Description string `json:"description" validate:"lte=255" zh:"项目描述"`
        Status      int8   `json:"status,optional" validate:"omitempty,oneof=0 1 2" zh:"标签状态"`
    }
    ModifyTagResp {}
)

// 查询标签
type (
    SearchTagReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Type       string       `json:"type" validate:"required,oneof=COMPONENT_GROUP CASE SUITE PLAN INTERFACE_DOCUMENT UI_AGENT" zh:"标签分类"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }

    SearchTagResp {
        CurrentPage uint64 `json:"current_page"`
        PageSize    uint64 `json:"page_size"`
        TotalCount  uint64 `json:"total_count"`
        TotalPage   uint64 `json:"total_page"`
        Items       []*Tag `json:"items"`
    }
)

// 查看标签
type (
    ViewTagReq {
        ProjectId  string `form:"project_id" validate:"required" zh:"项目ID"`
        TagId      string `form:"tag_id" validate:"required" zh:"标签ID"`
    }
    ViewTagResp {
        *Tag
    }
)
