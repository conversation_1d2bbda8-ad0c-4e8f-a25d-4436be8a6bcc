syntax = "v1"

import "types.api"


type BindDevice {
    UDID  string `json:"udid" copier:"Udid" validate:"gte=1,lte=64" zh:"设备编号"`
    Usage int8   `json:"usage" validate:"oneof=1 2" zh:"设备用途"`
}

type DeviceRelationship {
    UDID  string `json:"udid" copier:"Udid" validate:"gte=1,lte=64" zh:"设备编号"`
    Count int64  `json:"count" validate:"gte=0" zh:"关联的计划数量"`
}

type Device {}

type ProjectDevice {
    Device

    ProjectId string `json:"project_id" zh:"项目ID"`
    Usage     int8   `json:"usage" zh:"设备用途"`
}

type SearchProjectDeviceReferenceItem {
    ProjectId     string        `json:"project_id"`     // 项目ID
    Udid          string        `json:"udid"`           // 设备编号
    ReferenceType string        `json:"reference_type"` // 引用对象类型
    ReferenceId   string        `json:"reference_id"`   // 引用对象ID
    CategoryId    string        `json:"category_id"`    // 引用对象分类ID
    Name          string        `json:"name"`           // 引用对象名称
    Description   string        `json:"description"`    // 引用对象描述
    MaintainedBy  *FullUserInfo `json:"maintained_by"`  // 维护者
    CreatedBy     *FullUserInfo `json:"created_by"`     // 创建者
    UpdatedBy     *FullUserInfo `json:"updated_by"`     // 更新者
    CreatedAt     int64         `json:"created_at"`     // 创建时间
    UpdatedAt     int64         `json:"updated_at"`     // 更新时间
}

// 编辑项目设备列表
type (
    ModifyProjectDeviceReq {
        ProjectId string        `json:"project_id" validate:"required" zh:"项目ID"`
        Devices   []*BindDevice `json:"devices" validate:"gte=0,dive,required" zh:"设备列表"`
    }
    ModifyProjectDeviceResp {
        CreateItems       []*BindDevice         `json:"create_items"`
        UpdateItems       []*BindDevice         `json:"update_items"`
        DeleteItems       []*BindDevice         `json:"delete_items"`
        IgnoreCreateItems []*BindDevice         `json:"ignore_create_items"`
        IgnoreUpdateItems []*DeviceRelationship `json:"ignore_update_items"`
        IgnoreDeleteItems []*DeviceRelationship `json:"ignore_delete_items"`
    }
)

// 搜索项目设备
type (
    SearchProjectDeviceReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Usage      int8         `json:"usage" validate:"oneof=1 2" zh:"用途"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchProjectDeviceResp {
        CurrentPage uint64           `json:"current_page"`
        PageSize    uint64           `json:"page_size"`
        TotalCount  uint64           `json:"total_count"`
        TotalPage   uint64           `json:"total_page"`
        Items       []*ProjectDevice `json:"items"`
    }
)

// 搜索未分配到项目的设备
type (
    SearchUnassignedProjectDeviceReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchUnassignedProjectDeviceResp {
        CurrentPage uint64           `json:"current_page"`
        PageSize    uint64           `json:"page_size"`
        TotalCount  uint64           `json:"total_count"`
        TotalPage   uint64           `json:"total_page"`
        Items       []*ProjectDevice `json:"items"`
    }
)

// 搜索项目设备引用详情
type (
    SearchProjectDeviceReferenceReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        Udid       string       `json:"udid" validate:"required" zh:"设备编号"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchProjectDeviceReferenceResp {
        CurrentPage uint64                              `json:"current_page"`
        PageSize    uint64                              `json:"page_size"`
        TotalCount  uint64                              `json:"total_count"`
        TotalPage   uint64                              `json:"total_page"`
        Items       []*SearchProjectDeviceReferenceItem `json:"items"`
    }
)

// 占用设备
type (
    AcquireProjectDeviceReq {
        ProjectId  string `json:"project_id" validate:"required" zh:"项目ID"`
        Usage      int8   `json:"usage" validate:"oneof=1 2" zh:"用途"`
        UDID       string `json:"udid" validate:"required" copier:"Udid" zh:"设备编号"`
        Expiration uint32 `json:"expiration,omitempty,optional" zh:"最大占用时长（秒）"`
    }
    AcquireProjectDeviceResp {
        *ProjectDevice
    }
)

// 释放设备
type (
    ReleaseProjectDeviceReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        Usage     int8   `json:"usage" validate:"oneof=1 2" zh:"用途"`
        UDID      string `json:"udid" validate:"required" copier:"Udid" zh:"设备编号"`
        Token     string `json:"token" zh:"令牌"`
    }
    ReleaseProjectDeviceResp {}
)
