syntax = "v1"

import "project_device_types.api"

@server (
    prefix: manager/v1
    group: projectDevice
)
service manager {
    @handler modifyProjectDevice
    put /project_device/modify (ModifyProjectDeviceReq) returns (ModifyProjectDeviceResp)

    @handler searchProjectDevice
    post /project_device/search (SearchProjectDeviceReq) returns (SearchProjectDeviceResp)

    @handler searchUnassignedProjectDevice
    post /project_device/remaining_search (SearchUnassignedProjectDeviceReq) returns (SearchUnassignedProjectDeviceResp)

    @handler searchProjectDeviceReference
    post /project_device/reference/search (SearchProjectDeviceReferenceReq) returns (SearchProjectDeviceReferenceResp)
}

@server (
    prefix: manager/v1
    group: projectDevice
    timeout: 10s
)
service manager {
    @handler acquireProjectDevice
    post /project_device/acquire (AcquireProjectDeviceReq) returns (AcquireProjectDeviceResp)

    @handler releaseProjectDevice
    post /project_device/release (ReleaseProjectDeviceReq) returns (ReleaseProjectDeviceResp)
}
