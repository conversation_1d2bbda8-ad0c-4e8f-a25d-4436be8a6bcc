// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/application_config.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.PlatformType(0)
)

// Validate checks the field values on ApplicationConfiguration with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApplicationConfiguration) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplicationConfiguration with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplicationConfigurationMultiError, or nil if none found.
func (m *ApplicationConfiguration) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicationConfiguration) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ConfigId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for PlatformType

	// no validation rules for AppId

	// no validation rules for AppDownloadLink

	for idx, item := range m.GetPrompts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ApplicationConfigurationValidationError{
						field:  fmt.Sprintf("Prompts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ApplicationConfigurationValidationError{
						field:  fmt.Sprintf("Prompts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ApplicationConfigurationValidationError{
					field:  fmt.Sprintf("Prompts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return ApplicationConfigurationMultiError(errors)
	}

	return nil
}

// ApplicationConfigurationMultiError is an error wrapping multiple validation
// errors returned by ApplicationConfiguration.ValidateAll() if the designated
// constraints aren't met.
type ApplicationConfigurationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicationConfigurationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicationConfigurationMultiError) AllErrors() []error { return m }

// ApplicationConfigurationValidationError is the validation error returned by
// ApplicationConfiguration.Validate if the designated constraints aren't met.
type ApplicationConfigurationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicationConfigurationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicationConfigurationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicationConfigurationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicationConfigurationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicationConfigurationValidationError) ErrorName() string {
	return "ApplicationConfigurationValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicationConfigurationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicationConfiguration.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicationConfigurationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicationConfigurationValidationError{}

// Validate checks the field values on
// SearchApplicationConfigurationReferenceItem with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchApplicationConfigurationReferenceItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SearchApplicationConfigurationReferenceItem with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// SearchApplicationConfigurationReferenceItemMultiError, or nil if none found.
func (m *SearchApplicationConfigurationReferenceItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchApplicationConfigurationReferenceItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ConfigId

	// no validation rules for ReferenceType

	// no validation rules for ReferenceId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchApplicationConfigurationReferenceItemMultiError(errors)
	}

	return nil
}

// SearchApplicationConfigurationReferenceItemMultiError is an error wrapping
// multiple validation errors returned by
// SearchApplicationConfigurationReferenceItem.ValidateAll() if the designated
// constraints aren't met.
type SearchApplicationConfigurationReferenceItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchApplicationConfigurationReferenceItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchApplicationConfigurationReferenceItemMultiError) AllErrors() []error { return m }

// SearchApplicationConfigurationReferenceItemValidationError is the validation
// error returned by SearchApplicationConfigurationReferenceItem.Validate if
// the designated constraints aren't met.
type SearchApplicationConfigurationReferenceItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchApplicationConfigurationReferenceItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchApplicationConfigurationReferenceItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchApplicationConfigurationReferenceItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchApplicationConfigurationReferenceItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchApplicationConfigurationReferenceItemValidationError) ErrorName() string {
	return "SearchApplicationConfigurationReferenceItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchApplicationConfigurationReferenceItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchApplicationConfigurationReferenceItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchApplicationConfigurationReferenceItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchApplicationConfigurationReferenceItemValidationError{}
