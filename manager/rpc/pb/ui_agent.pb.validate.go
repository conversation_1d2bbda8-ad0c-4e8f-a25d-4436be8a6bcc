// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/ui_agent.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.PlatformType(0)
)

// Validate checks the field values on UIAgentComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UIAgentComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UIAgentComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UIAgentComponentMultiError, or nil if none found.
func (m *UIAgentComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *UIAgentComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CategoryId

	// no validation rules for ComponentId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for PlatformType

	// no validation rules for ApplicationId

	// no validation rules for Mode

	for idx, item := range m.GetAgentModeSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UIAgentComponentValidationError{
						field:  fmt.Sprintf("AgentModeSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UIAgentComponentValidationError{
						field:  fmt.Sprintf("AgentModeSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UIAgentComponentValidationError{
					field:  fmt.Sprintf("AgentModeSteps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetStepModeSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UIAgentComponentValidationError{
						field:  fmt.Sprintf("StepModeSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UIAgentComponentValidationError{
						field:  fmt.Sprintf("StepModeSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UIAgentComponentValidationError{
					field:  fmt.Sprintf("StepModeSteps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetExpectation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UIAgentComponentValidationError{
					field:  "Expectation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UIAgentComponentValidationError{
					field:  "Expectation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UIAgentComponentValidationError{
				field:  "Expectation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVariables() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UIAgentComponentValidationError{
						field:  fmt.Sprintf("Variables[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UIAgentComponentValidationError{
						field:  fmt.Sprintf("Variables[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UIAgentComponentValidationError{
					field:  fmt.Sprintf("Variables[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ReferenceId

	// no validation rules for LatestExecutedAt

	// no validation rules for LatestResult

	// no validation rules for State

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return UIAgentComponentMultiError(errors)
	}

	return nil
}

// UIAgentComponentMultiError is an error wrapping multiple validation errors
// returned by UIAgentComponent.ValidateAll() if the designated constraints
// aren't met.
type UIAgentComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UIAgentComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UIAgentComponentMultiError) AllErrors() []error { return m }

// UIAgentComponentValidationError is the validation error returned by
// UIAgentComponent.Validate if the designated constraints aren't met.
type UIAgentComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UIAgentComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UIAgentComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UIAgentComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UIAgentComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UIAgentComponentValidationError) ErrorName() string { return "UIAgentComponentValidationError" }

// Error satisfies the builtin error interface
func (e UIAgentComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUIAgentComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UIAgentComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UIAgentComponentValidationError{}

// Validate checks the field values on DeleteUnusedUIAgentImageTaskInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeleteUnusedUIAgentImageTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteUnusedUIAgentImageTaskInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeleteUnusedUIAgentImageTaskInfoMultiError, or nil if none found.
func (m *DeleteUnusedUIAgentImageTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteUnusedUIAgentImageTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_DeleteUnusedUIAgentImageTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := DeleteUnusedUIAgentImageTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteUnusedUIAgentImageTaskInfoMultiError(errors)
	}

	return nil
}

// DeleteUnusedUIAgentImageTaskInfoMultiError is an error wrapping multiple
// validation errors returned by
// DeleteUnusedUIAgentImageTaskInfo.ValidateAll() if the designated
// constraints aren't met.
type DeleteUnusedUIAgentImageTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteUnusedUIAgentImageTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteUnusedUIAgentImageTaskInfoMultiError) AllErrors() []error { return m }

// DeleteUnusedUIAgentImageTaskInfoValidationError is the validation error
// returned by DeleteUnusedUIAgentImageTaskInfo.Validate if the designated
// constraints aren't met.
type DeleteUnusedUIAgentImageTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteUnusedUIAgentImageTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteUnusedUIAgentImageTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteUnusedUIAgentImageTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteUnusedUIAgentImageTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteUnusedUIAgentImageTaskInfoValidationError) ErrorName() string {
	return "DeleteUnusedUIAgentImageTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteUnusedUIAgentImageTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteUnusedUIAgentImageTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteUnusedUIAgentImageTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteUnusedUIAgentImageTaskInfoValidationError{}

var _DeleteUnusedUIAgentImageTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

// Validate checks the field values on UpdateUIAgentComponentResultTaskInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateUIAgentComponentResultTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUIAgentComponentResultTaskInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateUIAgentComponentResultTaskInfoMultiError, or nil if none found.
func (m *UpdateUIAgentComponentResultTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUIAgentComponentResultTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_UpdateUIAgentComponentResultTaskInfo_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := UpdateUIAgentComponentResultTaskInfoValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UpdateUIAgentComponentResultTaskInfo_ComponentId_Pattern.MatchString(m.GetComponentId()) {
		err := UpdateUIAgentComponentResultTaskInfoValidationError{
			field:  "ComponentId",
			reason: "value does not match regex pattern \"(?:^ui_agent_component_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ExecutedAt

	// no validation rules for Result

	if len(errors) > 0 {
		return UpdateUIAgentComponentResultTaskInfoMultiError(errors)
	}

	return nil
}

// UpdateUIAgentComponentResultTaskInfoMultiError is an error wrapping multiple
// validation errors returned by
// UpdateUIAgentComponentResultTaskInfo.ValidateAll() if the designated
// constraints aren't met.
type UpdateUIAgentComponentResultTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUIAgentComponentResultTaskInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUIAgentComponentResultTaskInfoMultiError) AllErrors() []error { return m }

// UpdateUIAgentComponentResultTaskInfoValidationError is the validation error
// returned by UpdateUIAgentComponentResultTaskInfo.Validate if the designated
// constraints aren't met.
type UpdateUIAgentComponentResultTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUIAgentComponentResultTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUIAgentComponentResultTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUIAgentComponentResultTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUIAgentComponentResultTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUIAgentComponentResultTaskInfoValidationError) ErrorName() string {
	return "UpdateUIAgentComponentResultTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUIAgentComponentResultTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUIAgentComponentResultTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUIAgentComponentResultTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUIAgentComponentResultTaskInfoValidationError{}

var _UpdateUIAgentComponentResultTaskInfo_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _UpdateUIAgentComponentResultTaskInfo_ComponentId_Pattern = regexp.MustCompile("(?:^ui_agent_component_id:.+?)")
