// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/prompt_config.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.PromptPurpose(0)
)

// Validate checks the field values on PromptConfiguration with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PromptConfiguration) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PromptConfiguration with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PromptConfigurationMultiError, or nil if none found.
func (m *PromptConfiguration) ValidateAll() error {
	return m.validate(true)
}

func (m *PromptConfiguration) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ConfigId

	// no validation rules for Purpose

	// no validation rules for Category

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Content

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return PromptConfigurationMultiError(errors)
	}

	return nil
}

// PromptConfigurationMultiError is an error wrapping multiple validation
// errors returned by PromptConfiguration.ValidateAll() if the designated
// constraints aren't met.
type PromptConfigurationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PromptConfigurationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PromptConfigurationMultiError) AllErrors() []error { return m }

// PromptConfigurationValidationError is the validation error returned by
// PromptConfiguration.Validate if the designated constraints aren't met.
type PromptConfigurationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PromptConfigurationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PromptConfigurationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PromptConfigurationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PromptConfigurationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PromptConfigurationValidationError) ErrorName() string {
	return "PromptConfigurationValidationError"
}

// Error satisfies the builtin error interface
func (e PromptConfigurationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPromptConfiguration.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PromptConfigurationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PromptConfigurationValidationError{}

// Validate checks the field values on SearchPromptConfigurationReferenceItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SearchPromptConfigurationReferenceItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SearchPromptConfigurationReferenceItem with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// SearchPromptConfigurationReferenceItemMultiError, or nil if none found.
func (m *SearchPromptConfigurationReferenceItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchPromptConfigurationReferenceItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for ConfigId

	// no validation rules for ReferenceType

	// no validation rules for ReferenceId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SearchPromptConfigurationReferenceItemMultiError(errors)
	}

	return nil
}

// SearchPromptConfigurationReferenceItemMultiError is an error wrapping
// multiple validation errors returned by
// SearchPromptConfigurationReferenceItem.ValidateAll() if the designated
// constraints aren't met.
type SearchPromptConfigurationReferenceItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchPromptConfigurationReferenceItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchPromptConfigurationReferenceItemMultiError) AllErrors() []error { return m }

// SearchPromptConfigurationReferenceItemValidationError is the validation
// error returned by SearchPromptConfigurationReferenceItem.Validate if the
// designated constraints aren't met.
type SearchPromptConfigurationReferenceItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchPromptConfigurationReferenceItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchPromptConfigurationReferenceItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchPromptConfigurationReferenceItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchPromptConfigurationReferenceItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchPromptConfigurationReferenceItemValidationError) ErrorName() string {
	return "SearchPromptConfigurationReferenceItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchPromptConfigurationReferenceItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchPromptConfigurationReferenceItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchPromptConfigurationReferenceItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchPromptConfigurationReferenceItemValidationError{}
