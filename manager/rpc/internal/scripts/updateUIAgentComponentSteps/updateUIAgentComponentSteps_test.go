package updateUIAgentComponentSteps

import (
	"context"
	"flag"
	"testing"

	"github.com/zeromicro/go-zero/core/conf"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
)

var (
	configPath = flag.String("config_path", "./rpc/etc/manager.yaml", "config path")
	projectID  = flag.String("project_id", "", "project id")
)

func TestMain(m *testing.M) {
	if !flag.Parsed() {
		flag.Parse()
	}

	m.Run()
}

func TestUpdateUIAgentComponentSteps(t *testing.T) {
	if *projectID == "" {
		t.Fatal("not set the `project_id`")
	}

	var c config.Config
	conf.MustLoad(*configPath, &c)

	l := logic{
		ctx: userinfo.WithContext(
			context.Background(), &userinfo.UserInfo{
				Account:  "T1704",
				Fullname: "韩子健",
				DeptName: "平台研发组",
				Email:    "<EMAIL>",
			},
		),
		svcCtx: svc.NewServiceContext(c),
	}
	if err := l.updateUIAgentComponentSteps(*projectID); err != nil {
		t.Fatal(err)
	}
}

type logic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func (l *logic) updateUIAgentComponentSteps(projectID string) error {
	components, err := l.svcCtx.UIAgentComponentModel.FindNoCacheByQuery(
		l.ctx, l.svcCtx.UIAgentComponentModel.SelectBuilder().Where("`project_id` = ? AND `mode` = ?", projectID, commonpb.UIAgentMode_UIAgentMode_AGENT),
	)
	if err != nil {
		return err
	}

	for _, component := range components {
		if component.Mode != 1 {
			continue
		}

		var steps []*commonpb.UIAgentComponentStep
		if err = protobuf.UnmarshalJSONWithMessagesFromString(
			component.AgentModeSteps, &steps,
		); err != nil {
			return err
		}

		updated := false
		for _, step := range steps {
			if step.GetWaitingTime() == 0 {
				step.WaitingTime = 1.5
				updated = true
			}
		}

		if updated {
			component.AgentModeSteps = protobuf.MarshalJSONWithMessagesToStringIgnoreError(steps)
			if _, err = l.svcCtx.UIAgentComponentModel.Update(l.ctx, nil, component); err != nil {
				return err
			}
		}
	}

	return nil
}
