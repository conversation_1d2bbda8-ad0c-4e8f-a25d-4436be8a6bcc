// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	applicationconfigurationservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/applicationconfigurationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ApplicationConfigurationServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedApplicationConfigurationServiceServer
}

func NewApplicationConfigurationServiceServer(svcCtx *svc.ServiceContext) *ApplicationConfigurationServiceServer {
	return &ApplicationConfigurationServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateApplicationConfiguration 创建应用配置
func (s *ApplicationConfigurationServiceServer) CreateApplicationConfiguration(ctx context.Context, in *pb.CreateApplicationConfigurationReq) (*pb.CreateApplicationConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := applicationconfigurationservicelogic.NewCreateApplicationConfigurationLogic(ctx, s.svcCtx)

	return l.CreateApplicationConfiguration(in)
}

// RemoveApplicationConfiguration 删除应用配置
func (s *ApplicationConfigurationServiceServer) RemoveApplicationConfiguration(ctx context.Context, in *pb.RemoveApplicationConfigurationReq) (*pb.RemoveApplicationConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := applicationconfigurationservicelogic.NewRemoveApplicationConfigurationLogic(ctx, s.svcCtx)

	return l.RemoveApplicationConfiguration(in)
}

// ModifyApplicationConfiguration 编辑应用配置
func (s *ApplicationConfigurationServiceServer) ModifyApplicationConfiguration(ctx context.Context, in *pb.ModifyApplicationConfigurationReq) (*pb.ModifyApplicationConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := applicationconfigurationservicelogic.NewModifyApplicationConfigurationLogic(ctx, s.svcCtx)

	return l.ModifyApplicationConfiguration(in)
}

// SearchApplicationConfiguration 搜索应用配置
func (s *ApplicationConfigurationServiceServer) SearchApplicationConfiguration(ctx context.Context, in *pb.SearchApplicationConfigurationReq) (*pb.SearchApplicationConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := applicationconfigurationservicelogic.NewSearchApplicationConfigurationLogic(ctx, s.svcCtx)

	return l.SearchApplicationConfiguration(in)
}

// ViewApplicationConfiguration 查看应用配置
func (s *ApplicationConfigurationServiceServer) ViewApplicationConfiguration(ctx context.Context, in *pb.ViewApplicationConfigurationReq) (*pb.ViewApplicationConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := applicationconfigurationservicelogic.NewViewApplicationConfigurationLogic(ctx, s.svcCtx)

	return l.ViewApplicationConfiguration(in)
}

// SearchApplicationConfigurationReference 搜索应用配置引用详情
func (s *ApplicationConfigurationServiceServer) SearchApplicationConfigurationReference(ctx context.Context, in *pb.SearchApplicationConfigurationReferenceReq) (*pb.SearchApplicationConfigurationReferenceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := applicationconfigurationservicelogic.NewSearchApplicationConfigurationReferenceLogic(ctx, s.svcCtx)

	return l.SearchApplicationConfigurationReference(in)
}
