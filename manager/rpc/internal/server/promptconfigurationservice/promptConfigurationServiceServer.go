// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	promptconfigurationservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/promptconfigurationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PromptConfigurationServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedPromptConfigurationServiceServer
}

func NewPromptConfigurationServiceServer(svcCtx *svc.ServiceContext) *PromptConfigurationServiceServer {
	return &PromptConfigurationServiceServer{
		svcCtx: svcCtx,
	}
}

// CreatePromptConfiguration 创建Prompt配置
func (s *PromptConfigurationServiceServer) CreatePromptConfiguration(ctx context.Context, in *pb.CreatePromptConfigurationReq) (*pb.CreatePromptConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := promptconfigurationservicelogic.NewCreatePromptConfigurationLogic(ctx, s.svcCtx)

	return l.CreatePromptConfiguration(in)
}

// RemovePromptConfiguration 删除Prompt配置
func (s *PromptConfigurationServiceServer) RemovePromptConfiguration(ctx context.Context, in *pb.RemovePromptConfigurationReq) (*pb.RemovePromptConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := promptconfigurationservicelogic.NewRemovePromptConfigurationLogic(ctx, s.svcCtx)

	return l.RemovePromptConfiguration(in)
}

// ModifyPromptConfiguration 编辑Prompt配置
func (s *PromptConfigurationServiceServer) ModifyPromptConfiguration(ctx context.Context, in *pb.ModifyPromptConfigurationReq) (*pb.ModifyPromptConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := promptconfigurationservicelogic.NewModifyPromptConfigurationLogic(ctx, s.svcCtx)

	return l.ModifyPromptConfiguration(in)
}

// SearchPromptConfiguration 搜索Prompt配置
func (s *PromptConfigurationServiceServer) SearchPromptConfiguration(ctx context.Context, in *pb.SearchPromptConfigurationReq) (*pb.SearchPromptConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := promptconfigurationservicelogic.NewSearchPromptConfigurationLogic(ctx, s.svcCtx)

	return l.SearchPromptConfiguration(in)
}

// ViewPromptConfiguration 查看Prompt配置
func (s *PromptConfigurationServiceServer) ViewPromptConfiguration(ctx context.Context, in *pb.ViewPromptConfigurationReq) (*pb.ViewPromptConfigurationResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := promptconfigurationservicelogic.NewViewPromptConfigurationLogic(ctx, s.svcCtx)

	return l.ViewPromptConfiguration(in)
}

func (s *PromptConfigurationServiceServer) SearchPromptConfigurationReference(ctx context.Context, in *pb.SearchPromptConfigurationReferenceReq) (*pb.SearchPromptConfigurationReferenceResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := promptconfigurationservicelogic.NewSearchPromptConfigurationReferenceLogic(ctx, s.svcCtx)

	return l.SearchPromptConfigurationReference(in)
}
