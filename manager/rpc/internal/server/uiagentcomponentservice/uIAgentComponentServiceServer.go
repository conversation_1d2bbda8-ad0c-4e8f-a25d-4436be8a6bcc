// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: manager.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	uiagentcomponentservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/uiagentcomponentservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UIAgentComponentServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedUIAgentComponentServiceServer
}

func NewUIAgentComponentServiceServer(svcCtx *svc.ServiceContext) *UIAgentComponentServiceServer {
	return &UIAgentComponentServiceServer{
		svcCtx: svcCtx,
	}
}

// CreateUIAgentComponent 创建`UI Agent`组件
func (s *UIAgentComponentServiceServer) CreateUIAgentComponent(ctx context.Context, in *pb.CreateUIAgentComponentReq) (*pb.CreateUIAgentComponentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiagentcomponentservicelogic.NewCreateUIAgentComponentLogic(ctx, s.svcCtx)

	return l.CreateUIAgentComponent(in)
}

// RemoveUIAgentComponent 删除`UI Agent`组件
func (s *UIAgentComponentServiceServer) RemoveUIAgentComponent(ctx context.Context, in *pb.RemoveUIAgentComponentReq) (*pb.RemoveUIAgentComponentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiagentcomponentservicelogic.NewRemoveUIAgentComponentLogic(ctx, s.svcCtx)

	return l.RemoveUIAgentComponent(in)
}

// ModifyUIAgentComponent 编辑`UI Agent`组件
func (s *UIAgentComponentServiceServer) ModifyUIAgentComponent(ctx context.Context, in *pb.ModifyUIAgentComponentReq) (*pb.ModifyUIAgentComponentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiagentcomponentservicelogic.NewModifyUIAgentComponentLogic(ctx, s.svcCtx)

	return l.ModifyUIAgentComponent(in)
}

// SearchUIAgentComponent 搜索`UI Agent`组件
func (s *UIAgentComponentServiceServer) SearchUIAgentComponent(ctx context.Context, in *pb.SearchUIAgentComponentReq) (*pb.SearchUIAgentComponentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiagentcomponentservicelogic.NewSearchUIAgentComponentLogic(ctx, s.svcCtx)

	return l.SearchUIAgentComponent(in)
}

// ViewUIAgentComponent 查看`UI Agent`组件
func (s *UIAgentComponentServiceServer) ViewUIAgentComponent(ctx context.Context, in *pb.ViewUIAgentComponentReq) (*pb.ViewUIAgentComponentResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiagentcomponentservicelogic.NewViewUIAgentComponentLogic(ctx, s.svcCtx)

	return l.ViewUIAgentComponent(in)
}

// UpdateUIAgentComponentResult 更新`UI Agent`组件的执行结果
func (s *UIAgentComponentServiceServer) UpdateUIAgentComponentResult(ctx context.Context, in *pb.UpdateUIAgentComponentResultReq) (*pb.UpdateUIAgentComponentResultResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiagentcomponentservicelogic.NewUpdateUIAgentComponentResultLogic(ctx, s.svcCtx)

	return l.UpdateUIAgentComponentResult(in)
}
