package internal

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/threading"

	consumerv1 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworker/consumer"
	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/tasks"
)

func InitOperation(svcCtx *svc.ServiceContext, needToLaunch bool) error {
	// register tasks
	if err := registerTasksToWorkerConsumerV1(svcCtx); err != nil {
		return errors.Wrap(err, "failed to register tasks")
	}
	if err := registerTasksToWorkerConsumerV2(svcCtx); err != nil {
		return errors.Wrap(err, "failed to register tasks")
	}
	if err := registerTasksToManagerConsumer(svcCtx); err != nil {
		return errors.Wrap(err, "failed to register tasks")
	}
	if err := registerTasksToLarkProxyConsumer(svcCtx); err != nil {
		return errors.Wrap(err, "failed to register tasks")
	}
	if err := registerScheduledTasks(svcCtx); err != nil {
		return errors.Wrapf(err, "failed to register scheduled tasks")
	}

	if needToLaunch {
		// start consumer
		launchWorkerConsumerV1(svcCtx.WorkerConsumerV1)
		launchWorkerConsumerV2(svcCtx.WorkerConsumerV2)
		launchManagerConsumer(svcCtx.ManagerConsumer)
		launchLarkProxyConsumer(svcCtx.LarkProxyConsumer)
	}

	return nil
}

func registerTasksToWorkerConsumerV1(svcCtx *svc.ServiceContext) error {
	return svcCtx.WorkerConsumerV1.RegisterTasks(
		map[string]any{
			constants.MQTaskTypeManagerHandleParsePythonProjectResult: tasks.HandleParsePythonProjectResultTaskMiddle(svcCtx),
		},
	)
}

func registerTasksToWorkerConsumerV2(svcCtx *svc.ServiceContext) error {
	return svcCtx.WorkerConsumerV2.RegisterHandlers(
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeManagerHandleParsePythonProjectResult,
			tasks.NewProcessorParsePythonProjectResult(svcCtx),
		),
	)
}

func registerTasksToManagerConsumer(svcCtx *svc.ServiceContext) error {
	return svcCtx.ManagerConsumer.RegisterHandlers(
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeManagerHandleCaseFailStatResult,
			tasks.NewCaseFailStatResultTaskProcessor(svcCtx),
		),
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeManagerHandleUpdatePerfPlanByCase,
			tasks.NewUpdatePerfPlanByCaseTaskProcessor(svcCtx),
		),
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeManagerHandleUpdateInterfaceDefinition,
			tasks.NewUpdateInterfaceDefinitionTaskProcessor(svcCtx),
		),
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeManagerHandleUpdateInterfaceCoverage,
			tasks.NewUpdateInterfaceCoverageTaskProcessor(svcCtx),
		),
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeManagerHandleDeleteDisabledDevice,
			tasks.NewDeleteDisabledDeviceTaskProcessor(svcCtx),
		),
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeManagerHandleDeleteUnusedUIAgentImage,
			tasks.NewDeleteUnusedUIAgentImageTaskProcessor(svcCtx),
		),
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeManagerHandlerUpdateUIAgentComponentResult,
			tasks.NewUpdateUIAgentComponentResultTaskProcessor(svcCtx),
		),
	)
}

func registerTasksToLarkProxyConsumer(svcCtx *svc.ServiceContext) error {
	return svcCtx.LarkProxyConsumer.RegisterHandlers(
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeLarkProxyChatDisbanded,
			tasks.NewProcessorOfLarkChatDisbandedTask(svcCtx),
		),
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeLarkProxyChatUpdated,
			tasks.NewProcessorOfLarkChatUpdatedTask(svcCtx),
		),
		consumerv2.NewTaskHandlerOjb(
			constants.MQTaskTypeLarkProxyChatBotDeleted,
			tasks.NewProcessorOfLarkChatBotDeletedTask(svcCtx),
		),
	)
}

func registerScheduledTasks(svcCtx *svc.ServiceContext) error {
	if err := svcCtx.Scheduler.RegisterTasks(
		map[string]func(){
			svcCtx.Config.FailedCaseCleaner.CronExpression: func() {
				if err := newUpdateFailedCaseRecordHandler(svcCtx).Update(); err != nil {
					logx.Errorf("failed to clear and update the failed case records, error: %+v", err)
				}
			},
			svcCtx.Config.UpdateInterfaceDefinition.CronExpression: func() {
				if err := newUpdateInterfaceDefinitionHandler(svcCtx).Update(); err != nil {
					logx.Errorf("failed to update the interface definition, error: %+v", err)
				}
			},
			svcCtx.Config.UpdateInterfaceCoverage.CronExpression: func() {
				if err := newUpdateInterfaceCoverageHandler(svcCtx).Update(); err != nil {
					logx.Errorf("failed to update the interface coverage, error: %+v", err)
				}
			},
			svcCtx.Config.UpdateInterfaceMetricsReference.CronExpression: func() {
				if err := newUpdateInterfaceMetricsReferenceHandler(svcCtx).Update(); err != nil {
					logx.Errorf("failed to update the interface metrics reference, error: %+v", err)
				}
			},
			svcCtx.Config.UpdateAdvancedNotification.CronExpression: func() {
				if err := newUpdateAdvancedNotificationHandler(svcCtx).Update(); err != nil {
					logx.Errorf("failed to update the advanced notification, error: %+v", err)
				}
			},
			svcCtx.Config.DeleteDisabledDevice.CronExpression: func() {
				if err := newDeletedDisabledDeviceHandler(svcCtx).Delete(); err != nil {
					logx.Errorf("failed to delete the disabled device, error: %+v", err)
				}
			},
			svcCtx.Config.DeleteUnusedUIAgentImage.CronExpression: func() {
				if err := newDeleteUnusedUIAgentImageHandler(svcCtx).Delete(); err != nil {
					logx.Errorf("failed to delete the unused ui agent image, error: %+v", err)
				}
			},
		},
	); err != nil {
		return err
	}

	svcCtx.Scheduler.Start()
	proc.AddShutdownListener(svcCtx.Scheduler.Stop)

	return nil
}

func launchWorkerConsumerV1(consumer *consumerv1.Consumer) {
	threading.GoSafe(
		func() {
			logx.Info("starting the worker consumer v1")
			consumer.Start()
		},
	)

	proc.AddShutdownListener(
		func() {
			logx.Info("stopping the worker consumer v1")
			consumer.Stop()
		},
	)
}

func launchWorkerConsumerV2(consumer *consumerv2.Consumer) {
	threading.GoSafe(
		func() {
			logx.Info("starting the worker consumer v2")
			consumer.Start()
		},
	)

	proc.AddShutdownListener(
		func() {
			logx.Info("stopping the worker consumer v2")
			consumer.Stop()
		},
	)
}

func launchManagerConsumer(consumer *consumerv2.Consumer) {
	threading.GoSafe(
		func() {
			logx.Info("starting the manager consumer")
			consumer.Start()
		},
	)

	proc.AddShutdownListener(
		func() {
			logx.Info("stopping the manager consumer")
			consumer.Stop()
		},
	)
}

func launchLarkProxyConsumer(consumer *consumerv2.Consumer) {
	threading.GoSafe(
		func() {
			logx.Info("starting the larkproxy consumer")
			consumer.Start()
		},
	)

	proc.AddShutdownListener(
		func() {
			logx.Info("stopping the larkproxy consumer")
			consumer.Stop()
		},
	)
}
