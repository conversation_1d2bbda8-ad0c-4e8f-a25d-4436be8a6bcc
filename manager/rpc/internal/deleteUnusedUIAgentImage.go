package internal

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type DeleteUnusedUIAgentImageHandler struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func newDeleteUnusedUIAgentImageHandler(svcCtx *svc.ServiceContext) *DeleteUnusedUIAgentImageHandler {
	ctx := context.Background()
	return &DeleteUnusedUIAgentImageHandler{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (h *DeleteUnusedUIAgentImageHandler) Delete() error {
	var cancel context.CancelFunc
	h.ctx, cancel = context.WithTimeout(h.ctx, common.ConstExpireOfDeleteUnusedUIAgentImageTask)
	defer cancel()

	key := common.ConstLockDeleteUnusedUIAgentImage
	fn := func() error {
		projects, err := h.svcCtx.ProjectModel.FindAll(h.ctx)
		if err != nil {
			return err
		}

		tp := constants.MQTaskTypeManagerHandleDeleteUnusedUIAgentImage
		for _, project := range projects {
			payload := protobuf.MarshalJSONIgnoreError(
				&pb.DeleteUnusedUIAgentImageTaskInfo{
					ProjectId: project.ProjectId,
				},
			)
			if _, err := h.svcCtx.ManagerProducer.Send(
				h.ctx,
				base.NewTask(tp, payload, base.WithRetentionOptions(12*time.Hour)),
				base.QueuePriorityDefault,
			); err != nil {
				h.Errorf("failed to send task to mq, type: %s, payload: %s, error: %+v", tp, payload, err)
			} else {
				h.Debugf("send task to mq successfully, type: %s, payload: %s", tp, payload)
			}
		}

		return nil
	}

	if err := caller.LockWithOptionDo(
		h.svcCtx.Redis, key, fn, redislock.WithExpire(common.ConstExpireOfDeleteUnusedUIAgentImageTask),
	); err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			h.Errorf("failed to delete unused ui agent image, key: %s, error: %+v", key, err)
			return err
		}

		h.Infof("another service is executing the task of delete unused ui agent image, key: %s", key)
	} else {
		h.Infof("finish to delete unused ui agent image, key: %s", key)
	}

	return nil
}
