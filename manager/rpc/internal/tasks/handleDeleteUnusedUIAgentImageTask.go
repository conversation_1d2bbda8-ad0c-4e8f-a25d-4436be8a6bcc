package tasks

import (
	"context"
	"os"
	"path/filepath"
	"runtime/debug"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type DeleteUnusedUIAgentImageTaskProcessor struct {
	svcCtx *svc.ServiceContext
}

func NewDeleteUnusedUIAgentImageTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &DeleteUnusedUIAgentImageTaskProcessor{
		svcCtx: svcCtx,
	}
}

func (p *DeleteUnusedUIAgentImageTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) (
	result []byte, err error,
) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	ctx, cancel := context.WithTimeout(ctx, common.ConstExpireOfDeleteUnusedUIAgentImageTask)
	defer cancel()

	logger.Infof("the payload of delete unused ui agent image task: %s", task.Payload)

	var info pb.DeleteUnusedUIAgentImageTaskInfo
	if err = protobuf.UnmarshalJSON(task.Payload, &info); err != nil {
		logger.Errorf(
			"failed to unmarshal the payload of delete unused ui agent image, payload: %s, error: %+v",
			task.Payload, err,
		)
		return []byte(constants.FAILURE), nil
	}

	if err = NewDeleteUnusedUIAgentImageTaskLogic(ctx, p.svcCtx).Delete(&info); err != nil {
		logger.Errorf("failed to delete unused ui agent image, payload: %s, error: %+v", task.Payload, err)
		return []byte(constants.FAILURE), nil
	}

	return []byte(constants.SUCCESS), nil
}

type DeleteUnusedUIAgentImageTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteUnusedUIAgentImageTaskLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *DeleteUnusedUIAgentImageTaskLogic {
	return &DeleteUnusedUIAgentImageTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteUnusedUIAgentImageTaskLogic) Delete(info *pb.DeleteUnusedUIAgentImageTaskInfo) error {
	if info == nil {
		return nil
	}

	projectID := info.GetProjectId()
	records, err := l.svcCtx.UIAgentImageModel.FindAllUnreferenced(l.ctx, projectID)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find all unreferenced ui agent image, project_id: %s, error: %+v",
			projectID, err,
		)
	}

	return mr.MapReduceVoid[*model.UiAgentImage, any](
		func(source chan<- *model.UiAgentImage) {
			for _, record := range records {
				if record == nil {
					continue
				}

				source <- record
			}
		},
		func(item *model.UiAgentImage, writer mr.Writer[any], cancel func(error)) {
			if item == nil {
				return
			}

			out, err := l.svcCtx.ReporterRPC.GetCountOfUIAgentComponentRecordsByImage(
				l.ctx, &reporterpb.GetCountOfUIAgentComponentRecordsByImageReq{
					ProjectId: item.ProjectId,
					ImageId:   item.ImageId,
				},
			)
			if err != nil {
				l.Error(err)
				return
			} else if out.GetCount() > 0 {
				l.Infof(
					"some records are still using the unreferenced image, project_id: %s, image_id: %s, count: %d",
					item.ProjectId, item.ImageId, out.GetCount(),
				)
				return
			}

			_, err = l.svcCtx.UIAgentImageModel.RemoveByImageID(l.ctx, nil, item.ProjectId, item.ImageId)
			if err != nil {
				l.Errorf(
					"failed to remove unreferenced ui agent image, project_id: %s, image_id: %s, error: %+v",
					item.ProjectId, item.ImageId, err,
				)
			}

			filePath := filepath.Join(
				l.svcCtx.Config.PVCPath,
				constants.ConstStoragePathOfFiles,
				constants.ConstStoragePathOfUIAgentTest,
				item.ProjectId, item.ImageId,
			)
			if e := os.Remove(filePath); e != nil {
				l.Errorf("failed to remove ui agent image file, path: %s, error: %+v", filePath, e)
			}
		},
		func(pipe <-chan any, cancel func(error)) {
		},
		mr.WithContext(l.ctx),
	)
}
