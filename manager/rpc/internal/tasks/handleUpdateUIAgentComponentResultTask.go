package tasks

import (
	"context"
	"runtime/debug"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	uiagentcomponentservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/uiagentcomponentservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UpdateUIAgentComponentResultTaskProcessor struct {
	svcCtx *svc.ServiceContext
}

func NewUpdateUIAgentComponentResultTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &UpdateUIAgentComponentResultTaskProcessor{
		svcCtx: svcCtx,
	}
}

func (p *UpdateUIAgentComponentResultTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) ([]byte, error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor panic: %+v, stack: %s", r, debug.Stack())
		}
	}()

	ctx, cancel := context.WithTimeout(ctx, common.ConstExpireOfUpdateUIAgentComponentResultTask)
	defer cancel()

	logger.Infof("the payload of update ui agent component result task: %s", task.Payload)

	var info pb.UpdateUIAgentComponentResultTaskInfo
	if err := protobuf.UnmarshalJSON(task.Payload, &info); err != nil {
		logger.Errorf(
			"failed to unmarshal the payload of update ui agent component result, payload: %s, error: %+v",
			task.Payload, err,
		)
		return []byte(constants.FAILURE), nil
	}

	if _, err := uiagentcomponentservicelogic.NewUpdateUIAgentComponentResultLogic(ctx, p.svcCtx).UpdateUIAgentComponentResult(
		&pb.UpdateUIAgentComponentResultReq{
			ProjectId:   info.GetProjectId(),
			ComponentId: info.GetComponentId(),
			ExecutedAt:  info.GetExecutedAt(),
			Result:      info.GetResult(),
		},
	); err != nil {
		logger.Errorf(
			"failed to update ui agent component result, payload: %s, error: %+v",
			task.Payload, err,
		)
		return []byte(constants.FAILURE), nil
	}

	return []byte(constants.SUCCESS), nil
}
