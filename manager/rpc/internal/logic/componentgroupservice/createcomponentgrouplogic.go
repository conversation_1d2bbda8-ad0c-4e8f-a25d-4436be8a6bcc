package componentgroupservicelogic

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	categoryservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/categoryservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateComponentGroupLogic struct {
	*BaseLogic
}

func NewCreateComponentGroupLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateComponentGroupLogic {
	return &CreateComponentGroupLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateComponentGroup 创建组件组
func (l *CreateComponentGroupLogic) CreateComponentGroup(in *pb.CreateComponentGroupReq) (resp *pb.CreateComponentGroupResp, err error) {
	var c *model.Category

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeComponentGroup, in.GetCategoryId()); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("the type of parent category[%s] does not support creation of sub category", c.CategoryType)))
	}

	// validate the component_group_type in req
	if err = l.checkComponentGroupTypeByCategoryId(in.GetProjectId(), in.GetCategoryId(), in.GetComponentGroupType()); err != nil {
		return nil, err
	}

	// validate the circular reference exists
	if err = logic.CheckCircularReference(l.ctx, l.svcCtx.ComponentGroupModel, in.GetProjectId(), in.GetRelations()); err != nil {
		return nil, err
	}

	componentGroup, err := l.create(in)
	if err != nil {
		return nil, err
	}

	resp = &pb.CreateComponentGroupResp{ComponentGroup: &pb.ComponentGroup{}}
	if err = commonutils.Copy(resp.ComponentGroup, componentGroup, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy component group[%+v] to response, error: %+v", componentGroup, err)
	}

	return resp, nil
}

func (l *CreateComponentGroupLogic) checkComponentGroupTypeByCategoryId(projectId, categoryId, componentGroupType string) error {
	var name string
	switch componentGroupType {
	case common.ConstComponentGroupTypeSingle, common.ConstComponentGroupTypeGroup:
		name = common.ConstCategorySubRootBusinessComponent
	case common.ConstComponentGroupTypeSetup, common.ConstComponentGroupTypeTeardown:
		name = common.ConstCategorySubRootSetupTeardownComponent
	default:
		return errors.WithStack(errorx.Err(errorx.DoesNotSupport, fmt.Sprintf("the component group type[%s] doesn't support", componentGroupType)))
	}

	categories, err := l.svcCtx.CategoryModel.FindAncestralCategories(l.ctx, model.GetCategoryTreeCondition{
		ProjectId:  projectId,
		Type:       common.ConstCategoryTreeTypeComponentGroup,
		CategoryId: categoryId,
	})
	if err != nil {
		return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find ancestral categories of the category_id[%s], error: %+v", categoryId, err)
	}

	for _, c := range categories {
		if c.Name == name {
			return nil
		}
	}

	return errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("the type of component group[%s] must be created under the category tree[%s]", componentGroupType, name)))
}

// in order to reduce cyclomatic complexity of CreateComponentGroupLogic.CreateComponentGroup
func (l *CreateComponentGroupLogic) create(req *pb.CreateComponentGroupReq) (*model.ComponentGroup, error) {
	componentGroupId, err := l.generateComponentGroupId(req.GetProjectId())
	if err != nil {
		return nil, err
	}
	version := utils.GenVersion()

	// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
	tags := jsonx.MarshalToStringIgnoreError(req.GetTags())

	maintainedBy := req.GetMaintainedBy()
	if maintainedBy == "" {
		// if the maintainer is not set, the creator is set as the maintainer
		maintainedBy = l.currentUser.Account
	}

	componentGroup := &model.ComponentGroup{
		ProjectId:          req.GetProjectId(),
		CategoryId:         req.GetCategoryId(),
		ComponentGroupId:   componentGroupId,
		ComponentGroupType: req.GetComponentGroupType(),
		Name:               req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Priority: req.GetPriority(),
		Tags: sql.NullString{
			String: tags,
			Valid:  tags != "",
		},
		Imports:            jsonx.MarshalToStringIgnoreError(req.GetImports()),
		Exports:            jsonx.MarshalToStringIgnoreError(req.GetExports()),
		AccountConfig:      protobuf.MarshalJSONToStringIgnoreError(req.GetAccountConfig()),
		Version:            version,
		Structure:          protobuf.MarshalJSONWithMessagesToStringIgnoreError(req.GetRelations()),
		ReferenceStructure: protobuf.MarshalJSONToStringIgnoreError(req.GetReferenceRelations()),
		Latest:             int64(constants.IsLatestVersion),
		MaintainedBy: sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		},
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
	}

	// create component group in a transaction
	if err = l.svcCtx.ComponentGroupModel.Trans(l.ctx, func(context context.Context, session sqlx.Session) error {
		if _, err := l.svcCtx.ComponentGroupModel.InsertTX(context, session, componentGroup); err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v", l.svcCtx.ComponentGroupModel.Table(), componentGroup, err)
		}

		// create a category in the component group category tree
		if _, err := l.createCategoryLogic.CreateCategoryForInternal(context, session, categoryservicelogic.CreateCategoryInternalReq{
			CreateCategoryReq: &pb.CreateCategoryReq{
				ProjectId:   req.GetProjectId(),
				Type:        common.ConstCategoryTreeTypeComponentGroup,
				Name:        req.GetName(),
				Description: req.GetDescription(),
				ParentId:    req.GetCategoryId(),
			},
			CategoryType:   common.ConstCategoryTypeFile,
			RootType:       common.GetRootTypeByNodeType(componentGroup.ComponentGroupType),
			NodeType:       componentGroup.ComponentGroupType,
			NodeId:         componentGroup.ComponentGroupId,
			Builtin:        false,
			IsInternalCall: true,
		}); err != nil {
			return err
		}

		// create component group reference relationship
		if err := l.createReferenceRelationship(context, session, createReferenceRelationshipInternalReq{
			ProjectId:        componentGroup.ProjectId,
			ComponentGroupId: componentGroup.ComponentGroupId,
			Version:          componentGroup.Version,
			Relations:        req.GetRelations(),
		}); err != nil {
			return err
		}

		// create the elements and components of the component group
		if err := l.createElementAndComponent(context, session, createElementAndComponentInternalReq{
			ProjectId:        componentGroup.ProjectId,
			ComponentGroupId: componentGroup.ComponentGroupId,
			Version:          componentGroup.Version,
			Nodes:            req.GetNodes(),
			Edges:            req.GetEdges(),
			Combos:           req.GetCombos(),
		}); err != nil {
			return err
		}

		// create the new tag and tag reference of component group
		if err := l.createTagLogic.CreateTagAndReferenceForInternal(context, session, types.CreateOrUpdateTagReference{
			ProjectId:        componentGroup.ProjectId,
			ReferenceType:    common.ConstReferenceTypeComponentGroup,
			ReferenceId:      componentGroup.ComponentGroupId,
			ReferenceVersion: componentGroup.Version,
			Tags:             req.GetTags(),
		}); err != nil {
			return err
		}

		return nil
	}); err != nil {
		return nil, err
	}

	return componentGroup, nil
}
