package tagservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var noVersionReferenceType = []string{
	common.ConstReferenceTypeApiSuite,
	common.ConstReferenceTypeApiPlan,
	common.ConstReferenceTypeInterfaceDocument,
	common.ConstReferenceTypePerfCase,
	common.ConstReferenceTypePerfPlan,
	common.ConstReferenceTypeStabilityPlan,
	common.ConstReferenceTypeUIAgentComponent,
	common.ConstReferenceTypeUIAgentCase,
	common.ConstReferenceTypeUIAgentPlan,
}

type CreateTagLogic struct {
	*BaseLogic
}

func NewCreateTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateTagLogic {
	return &CreateTagLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateTag 创建标签
func (l *CreateTagLogic) CreateTag(in *pb.CreateTagReq) (resp *pb.CreateTagResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	if _, err = l.svcCtx.TagModel.FindOneByProjectIdTypeName(
		l.ctx, in.GetProjectId(), in.GetType(), in.GetName(),
	); !errors.Is(err, model.ErrNotFound) {
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find tag with project_id[%s], type[%s] and name[%s], error: %+v", in.GetProjectId(),
				in.GetType(), in.GetName(), err,
			)
		}
		return nil, errorx.Errorf(
			errorx.AlreadyExists,
			"tag with project_id[%s], type[%s] and name[%s] already exists",
			in.GetProjectId(), in.GetType(), in.GetName(),
		)
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockTagProjectIdTypeNamePrefix, in.GetProjectId(), in.GetType(), in.GetName(),
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	tag, err := l.create(in)
	if err != nil {
		return nil, err
	}

	resp = &pb.CreateTagResp{Tag: &pb.Tag{}}
	if err = utils.Copy(resp.Tag, tag, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy tag[%+v] to response, error: %+v", tag,
			err,
		)
	}

	return resp, nil
}

func (l *CreateTagLogic) create(req *pb.CreateTagReq) (*model.Tag, error) {
	tagId, err := l.generateTagId(req.GetProjectId())
	if err != nil {
		return nil, err
	}

	tag := &model.Tag{
		ProjectId: req.GetProjectId(),
		Type:      req.GetType(),
		TagId:     tagId,
		Name:      req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Status:    int64(constants.EnableStatus),
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
	}

	if _, err = l.svcCtx.TagModel.InsertTX(l.ctx, nil, tag); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v",
			l.svcCtx.TagModel.Table(), tag, err,
		)
	}

	return tag, nil
}

func (l *CreateTagLogic) CreateTagAndReferenceForInternal(
	ctx context.Context, session sqlx.Session, tr types.CreateOrUpdateTagReference,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	tagType := common.GetTagTypeByReferenceType(tr.ReferenceType)
	if tagType == "" {
		return errorx.Errorf(errorx.DoesNotSupport, "the tag reference type[%s] doesn't support", tr.ReferenceType)
	}

	tags := stringx.Distinct(tr.Tags)

	if stringx.Contains(noVersionReferenceType, tr.ReferenceType) {
		// 没有版本概念的，先删除原有的引用关系
		if len(tr.Tags) == 0 {
			// 删除全部引用关系
			if _, err := l.svcCtx.TagReferenceModel.RemoveByReferenceId(
				ctx, session, tr.ProjectId, tr.ReferenceType, tr.ReferenceId, "",
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove tag reference with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					tr.ProjectId, tr.ReferenceType, tr.ReferenceId, err,
				)
			}
		} else {
			// 删除不在本次中的标签的引用关系
			rs, err := l.svcCtx.TagReferenceModel.FindReferenceByReference(
				ctx, tr.ProjectId, tr.ReferenceType, tr.ReferenceId, "",
			)
			if err != nil && !errors.Is(err, model.ErrNotFound) {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find tag reference with project_id[%s], reference_type[%s] and reference_id[%s], error: %+v",
					tr.ProjectId, tr.ReferenceType, tr.ReferenceId, err,
				)
			}

			for _, r := range rs {
				tag, err := l.svcCtx.TagModel.FindOneByProjectIdTagId(ctx, tr.ProjectId, r.TagId)
				// 查询错误 或者 原标签不在本次之中，则删除引用关系
				if err != nil || !stringx.Contains(tr.Tags, tag.Name) {
					r.Deleted = int64(constants.HasDeleted)
					r.UpdatedBy = l.currentUser.Account
					r.UpdatedAt = time.Now()
					if _, err = l.svcCtx.TagReferenceModel.Update(ctx, session, r); err != nil {
						return errors.Wrapf(
							errorx.Err(errorx.DBError, err.Error()),
							"failed to remove tag reference with project_id[%s], reference_type[%s], reference_id[%s] and tag[%s], error: %+v",
							tr.ProjectId, tr.ReferenceType, tr.ReferenceId, tag.Name, err,
						)
					}
				} else {
					// 原标签已在本次之中，则无需创建引用关系
					tags = stringx.Remove(tags, tag.Name)
				}
			}
		}
	}

	for _, tag := range tags {
		var tagId string
		if t, err := l.svcCtx.TagModel.FindOneByProjectIdTypeName(ctx, tr.ProjectId, tagType, tag); err != nil {
			if !errors.Is(err, model.ErrNotFound) {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find tag with project_id[%s], type[%s] and name[%s], error: %+v",
					tr.ProjectId, tagType, tag, err,
				)
			}

			// 标签不存在则创建
			resp, err := l.CreateTag(
				&pb.CreateTagReq{
					ProjectId: tr.ProjectId,
					Type:      tagType,
					Name:      tag,
				},
			)
			if err != nil {
				return err
			}

			tagId = resp.Tag.TagId
		} else {
			tagId = t.TagId
		}

		// create tag reference relationship
		data := &model.TagReferenceRelationship{
			ProjectId:     tr.ProjectId,
			ReferenceType: tr.ReferenceType,
			ReferenceId:   tr.ReferenceId,
			ReferenceVersion: sql.NullString{
				String: tr.ReferenceVersion,
				Valid:  tr.ReferenceVersion != "",
			},
			TagId:     tagId,
			CreatedBy: l.currentUser.Account,
			UpdatedBy: l.currentUser.Account,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		if _, err := l.svcCtx.TagReferenceModel.Insert(ctx, session, data); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to insert table[%s] with values[%+v], error: %+v",
				l.svcCtx.TagReferenceModel.Table(), data, err,
			)
		}
	}

	return nil
}
