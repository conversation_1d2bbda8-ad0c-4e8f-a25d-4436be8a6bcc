package tagservicelogic

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyTagLogic struct {
	*BaseLogic
}

func NewModifyTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyTagLogic {
	return &ModifyTagLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyTag 编辑标签
func (l *ModifyTagLogic) ModifyTag(in *pb.ModifyTagReq) (resp *pb.ModifyTagResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the tag_id in req
	origin, err := model.CheckTagByTagId(l.ctx, l.svcCtx.TagModel, in.GetProjectId(), in.GetTagId())
	if err != nil {
		return nil, err
	}

	if in.GetType() != origin.Type {
		return nil, errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior, fmt.Sprintf(
					"cannot change the type of tag[%s] from [%s] to [%s]", origin.Name, origin.Type, in.GetType(),
				),
			),
		)
	}

	if in.GetName() != origin.Name {
		// validate the name in req
		if _, err = l.svcCtx.TagModel.FindOneByProjectIdTypeName(
			l.ctx, in.GetProjectId(), in.GetType(), in.GetName(),
		); !errors.Is(err, model.ErrNotFound) {
			if err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find tag with project_id[%s], type[%s] and name[%s], error: %+v", in.GetProjectId(),
					in.GetType(), in.GetName(), err,
				)
			}
			return nil, errorx.Err(
				errorx.AlreadyExists, fmt.Sprintf(
					"tag with project_id[%s], type[%s] and name[%s] already exists", in.GetProjectId(),
					in.GetType(), in.GetName(),
				),
			)
		}
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockTagProjectIdTagIdPrefix, in.GetProjectId(), in.GetTagId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	key1 := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockTagProjectIdTypeNamePrefix, in.GetProjectId(), in.GetType(), in.GetName(),
	)
	lock1, err := redislock.NewRedisLockAndAcquire(
		l.svcCtx.Redis, key1, redislock.WithExpire(common.ConstLockExpireTime),
	)
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock1.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	tag, err := l.modify(in, origin)
	if err != nil {
		return nil, err
	}

	resp = &pb.ModifyTagResp{Tag: &pb.Tag{}}
	if err = utils.Copy(resp.Tag, tag, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy tag[%+v] to response, error: %+v", tag,
			err,
		)
	}

	return resp, nil
}

func (l *ModifyTagLogic) modify(req *pb.ModifyTagReq, origin *model.Tag) (*model.Tag, error) {
	status := req.Status
	if status == int64(constants.NullStatus) {
		status = origin.Status
	}

	tag := &model.Tag{
		Id:        origin.Id,
		ProjectId: origin.ProjectId,
		Type:      origin.Type,
		TagId:     origin.TagId,
		Name:      req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Status:    status,
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
	}

	if err := l.svcCtx.TagModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.TagModel.UpdateTX(context, session, tag); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()), "failed to update table[%s] with values[%+v], error: %+v",
					l.svcCtx.TagModel.Table(), tag, err,
				)
			}

			if req.Name != origin.Name {
				rrs, err := l.svcCtx.TagReferenceModel.FindReferenceByTagId(l.ctx, tag.ProjectId, tag.TagId)
				if err != nil && !errors.Is(err, model.ErrNotFound) {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to find the tag reference with project_id[%s] and tag_id[%s], error: %+v",
						tag.ProjectId, tag.TagId, err,
					)
				}

				if err = l.updateReference(origin.Name, req.GetName(), rrs); err != nil {
					return err
				}
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return tag, nil
}

type updateTagsFunc func(fromTag, toTag string, ref *model.TagReferenceRelationship) error

func (l *ModifyTagLogic) updateReference(fromTag, toTag string, rrs []*model.TagReferenceRelationship) (err error) {
	workers := len(rrs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach(
		func(source chan<- any) {
			for _, rr := range rrs {
				source <- rr
			}
		}, func(item any) {
			ref, ok := item.(*model.TagReferenceRelationship)
			if !ok {
				err = multierror.Append(
					err, errorx.Errorf(
						errorx.InternalError,
						"the tag reference[%v (%T)] is not a *model.TagReferenceRelationship",
						item, item,
					),
				)
			} else {
				var fn updateTagsFunc

				switch ref.ReferenceType {
				case common.ConstReferenceTypeComponentGroup:
					fn = l.updateComponentGroupTags
				case common.ConstReferenceTypeApiCase:
					fn = l.updateApiCaseTags
				case common.ConstReferenceTypeApiSuite:
					fn = l.updateApiSuiteTags
				case common.ConstReferenceTypeApiPlan:
					fn = l.updateApiPlanTags
				case common.ConstReferenceTypeInterfaceDocument:
					fn = l.updateInterfaceDocumentTags
				case common.ConstReferenceTypeInterfaceCase:
					fn = l.updateInterfaceCaseTags
				case common.ConstReferenceTypePerfCase:
					fn = l.updatePerfCaseTags
				case common.ConstReferenceTypePerfPlan:
					fn = l.updatePerfPlanTags
				case common.ConstReferenceTypeStabilityPlan:
					fn = l.updateStabilityPlanTags
				case common.ConstReferenceTypeUIAgentComponent:
					fn = l.updateUIAgentComponentTags
				default:
					err = multierror.Append(
						err, errorx.Errorf(
							errorx.DoesNotSupport,
							"the type of tag reference[%s] doesn't support",
							ref.ReferenceType,
						),
					)
				}

				if fn != nil {
					if e := fn(fromTag, toTag, ref); e != nil {
						err = multierror.Append(err, e)
					}
				}
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return err
}

func (l *ModifyTagLogic) updateComponentGroupTags(
	fromTag, toTag string, ref *model.TagReferenceRelationship,
) (err error) {
	origin, err := model.CheckComponentGroupByComponentGroupId(
		l.ctx, l.svcCtx.ComponentGroupModel, ref.ProjectId, ref.ReferenceId, ref.ReferenceVersion.String,
	)
	if err != nil {
		return err
	} else if !origin.Tags.Valid || origin.Tags.String == "" {
		return errorx.Err(
			errorx.InternalError, fmt.Sprintf(
				"there are not tags in the component group with project_id[%s], component_group_id[%s] and version[%s]",
				ref.ProjectId, ref.ReferenceId, ref.ReferenceVersion.String,
			),
		)
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockComponentGroupProjectIdComponentGroupIdPrefix, ref.ProjectId, ref.ReferenceId,
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	var tags []string
	if err = jsonx.UnmarshalFromString(origin.Tags.String, &tags); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "failed to unmarshal the tags[%s], error: %+v",
			origin.Tags.String, err,
		)
	}

	updateTags(fromTag, toTag, tags)
	origin.Tags = sql.NullString{
		String: jsonx.MarshalToStringIgnoreError(tags),
		Valid:  true,
	}

	if _, err = l.svcCtx.ComponentGroupModel.UpdateTX(l.ctx, nil, origin); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update the tags[%s => %s] of component group[%s], error: %+v", fromTag, toTag, origin.Name, err,
		)
	}

	return nil
}

func (l *ModifyTagLogic) updateApiCaseTags(fromTag, toTag string, ref *model.TagReferenceRelationship) (err error) {
	origin, err := model.CheckApiCaseByCaseId(
		l.ctx, l.svcCtx.ApiCaseModel, ref.ProjectId, ref.ReferenceId, ref.ReferenceVersion.String,
	)
	if err != nil {
		return err
	} else if !origin.Tags.Valid || origin.Tags.String == "" {
		return errorx.Errorf(
			errorx.InternalError,
			"there are not tags in the api case, project_id: %s, case_id: %s, version: %s",
			ref.ProjectId, ref.ReferenceId, ref.ReferenceVersion.String,
		)
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiCaseProjectIdCaseIdPrefix, ref.ProjectId, ref.ReferenceId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	var tags []string
	if err = jsonx.UnmarshalFromString(origin.Tags.String, &tags); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the tags[%s], error: %+v",
			origin.Tags.String, err,
		)
	}

	updateTags(fromTag, toTag, tags)
	origin.Tags = sql.NullString{
		String: jsonx.MarshalToStringIgnoreError(tags),
		Valid:  true,
	}

	if _, err = l.svcCtx.ApiCaseModel.UpdateTX(l.ctx, nil, origin); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update the tags[%s => %s] of api case[%s], error: %+v",
			fromTag, toTag, origin.Name, err,
		)
	}

	return nil
}

func (l *ModifyTagLogic) updateApiSuiteTags(fromTag, toTag string, ref *model.TagReferenceRelationship) (err error) {
	origin, err := model.CheckApiSuiteBySuiteId(l.ctx, l.svcCtx.ApiSuiteModel, ref.ProjectId, ref.ReferenceId)
	if err != nil {
		return err
	} else if !origin.Tags.Valid || origin.Tags.String == "" {
		return errorx.Errorf(
			errorx.InternalError,
			"there are not tags in the api suite, project_id: %s, suite_id: %s",
			ref.ProjectId, ref.ReferenceId,
		)
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiSuiteProjectIdSuiteIdPrefix, ref.ProjectId, ref.ReferenceId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	var tags []string
	if err = jsonx.UnmarshalFromString(origin.Tags.String, &tags); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the tags[%s], error: %+v",
			origin.Tags.String, err,
		)
	}

	updateTags(fromTag, toTag, tags)
	origin.Tags = sql.NullString{
		String: jsonx.MarshalToStringIgnoreError(tags),
		Valid:  true,
	}

	if _, err = l.svcCtx.ApiSuiteModel.UpdateTX(l.ctx, nil, origin); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update the tags[%s => %s] of api suite[%s], error: %+v",
			fromTag, toTag, origin.Name, err,
		)
	}

	return nil
}

func (l *ModifyTagLogic) updateApiPlanTags(fromTag, toTag string, ref *model.TagReferenceRelationship) (err error) {
	origin, err := model.CheckApiPlanByPlanId(l.ctx, l.svcCtx.ApiPlanModel, ref.ProjectId, ref.ReferenceId)
	if err != nil {
		return err
	} else if !origin.Tags.Valid || origin.Tags.String == "" {
		return errorx.Errorf(
			errorx.InternalError,
			"there are not tags in the api plan, project_id: %s, plan_id: %s",
			ref.ProjectId, ref.ReferenceId,
		)
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiPlanProjectIdPlanIdPrefix, ref.ProjectId, ref.ReferenceId)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	var tags []string
	if err = jsonx.UnmarshalFromString(origin.Tags.String, &tags); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the tags[%s], error: %+v",
			origin.Tags.String, err,
		)
	}

	updateTags(fromTag, toTag, tags)
	origin.Tags = sql.NullString{
		String: jsonx.MarshalToStringIgnoreError(tags),
		Valid:  true,
	}

	if _, err = l.svcCtx.ApiPlanModel.UpdateTX(l.ctx, nil, origin); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update the tags[%s => %s] of api plan[%s], error: %+v",
			fromTag, toTag, origin.Name, err,
		)
	}

	return nil
}

func (l *ModifyTagLogic) updateInterfaceDocumentTags(
	fromTag, toTag string, ref *model.TagReferenceRelationship,
) (err error) {
	origin, err := model.CheckInterfaceDocumentByDocumentId(
		l.ctx, l.svcCtx.InterfaceDocumentModel, ref.ProjectId, ref.ReferenceId,
	)
	if err != nil {
		return err
	} else if !origin.Tags.Valid || origin.Tags.String == "" {
		return errorx.Errorf(
			errorx.InternalError,
			"there are not tags in the interface document, project_id: %s, document_id: %s",
			ref.ProjectId, ref.ReferenceId,
		)
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockInterfaceDocumentProjectIdDocumentIdPrefix, ref.ProjectId, ref.ReferenceId,
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	var tags []string
	if err = jsonx.UnmarshalFromString(origin.Tags.String, &tags); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the tags[%s], error: %+v",
			origin.Tags.String, err,
		)
	}

	updateTags(fromTag, toTag, tags)
	origin.Tags = sql.NullString{
		String: jsonx.MarshalToStringIgnoreError(tags),
		Valid:  true,
	}

	if _, err = l.svcCtx.InterfaceDocumentModel.UpdateTX(l.ctx, nil, origin); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update the tags[%s => %s] of interface document[%s], error: %+v",
			fromTag, toTag, origin.Name, err,
		)
	}

	return nil
}

func (l *ModifyTagLogic) updateInterfaceCaseTags(
	fromTag, toTag string, ref *model.TagReferenceRelationship,
) (err error) {
	origin, err := model.CheckInterfaceCaseByCaseId(
		l.ctx, l.svcCtx.InterfaceCaseModel, ref.ProjectId, "", ref.ReferenceId, ref.ReferenceVersion.String,
	)
	if err != nil {
		return err
	} else if !origin.Tags.Valid || origin.Tags.String == "" {
		return errorx.Errorf(
			errorx.InternalError,
			"there are not tags in the interface case, project_id: %s, case_id: %s, version: %s",
			ref.ProjectId, ref.ReferenceId, ref.ReferenceVersion.String,
		)
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockInterfaceCaseProjectIdDocumentIdCaseIdPrefix, ref.ProjectId, origin.DocumentId,
		ref.ReferenceId,
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	var tags []string
	if err = jsonx.UnmarshalFromString(origin.Tags.String, &tags); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the tags[%s], error: %+v",
			origin.Tags.String, err,
		)
	}

	updateTags(fromTag, toTag, tags)
	origin.Tags = sql.NullString{
		String: jsonx.MarshalToStringIgnoreError(tags),
		Valid:  true,
	}

	if _, err = l.svcCtx.InterfaceCaseModel.UpdateTX(l.ctx, nil, origin); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to update the tags[%s => %s] of interface case[%s], error: %+v",
			fromTag, toTag, origin.Name, err,
		)
	}

	return nil
}

func (l *ModifyTagLogic) updatePerfCaseTags(fromTag, toTag string, ref *model.TagReferenceRelationship) (err error) {
	origin, err := model.CheckPerfCaseV2ByCaseID(l.ctx, l.svcCtx.PerfCaseV2Model, ref.ProjectId, ref.ReferenceId)
	if err != nil {
		return err
	} else if !origin.Tags.Valid || origin.Tags.String == "" {
		return errorx.Errorf(
			errorx.InternalError,
			"there are not tags in the perf case, project_id: %s, case_id: %s",
			ref.ProjectId, ref.ReferenceId,
		)
	}

	key := fmt.Sprintf("%s:%s:%s", common.ConstLockPerfCaseProjectIDCaseIDPrefix, ref.ProjectId, ref.ReferenceId)
	fn := func() error {
		var tags []string
		if err = jsonx.UnmarshalFromString(origin.Tags.String, &tags); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.SerializationError, err.Error()),
				"failed to unmarshal the tags[%s], error: %+v",
				origin.Tags.String, err,
			)
		}

		updateTags(fromTag, toTag, tags)
		origin.Tags = sql.NullString{
			String: jsonx.MarshalToStringIgnoreError(tags),
			Valid:  true,
		}

		if _, err = l.svcCtx.PerfCaseV2Model.Update(l.ctx, nil, origin); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to update the tags[%s => %s] of perf case[%s], error: %+v",
				fromTag, toTag, origin.Name, err,
			)
		}

		return nil
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}

func (l *ModifyTagLogic) updatePerfPlanTags(fromTag, toTag string, ref *model.TagReferenceRelationship) (err error) {
	origin, err := model.CheckPerfPlanV2ByPlanID(l.ctx, l.svcCtx.PerfPlanV2Model, ref.ProjectId, ref.ReferenceId)
	if err != nil {
		return err
	} else if !origin.Tags.Valid || origin.Tags.String == "" {
		return errorx.Errorf(
			errorx.InternalError,
			"there are not tags in the perf plan, project_id: %s, plan_id: %s",
			ref.ProjectId, ref.ReferenceId,
		)
	}

	key := fmt.Sprintf("%s:%s:%s", common.ConstLockPerfPlanProjectIDPlanIDPrefix, ref.ProjectId, ref.ReferenceId)
	fn := func() error {
		var tags []string
		if err = jsonx.UnmarshalFromString(origin.Tags.String, &tags); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.SerializationError, err.Error()),
				"failed to unmarshal the tags[%s], error: %+v",
				origin.Tags.String, err,
			)
		}

		updateTags(fromTag, toTag, tags)
		origin.Tags = sql.NullString{
			String: jsonx.MarshalToStringIgnoreError(tags),
			Valid:  true,
		}

		if _, err = l.svcCtx.PerfPlanV2Model.Update(l.ctx, nil, origin); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to update the tags[%s => %s] of perf plan[%s], error: %+v",
				fromTag, toTag, origin.Name, err,
			)
		}

		return nil
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}

func (l *ModifyTagLogic) updateStabilityPlanTags(
	fromTag, toTag string, ref *model.TagReferenceRelationship,
) (err error) {
	origin, err := model.CheckStabilityPlanByPlanId(l.ctx, l.svcCtx.StabilityPlanModel, ref.ProjectId, ref.ReferenceId)
	if err != nil {
		return err
	} else if !origin.Tags.Valid || origin.Tags.String == "" {
		return errorx.Errorf(
			errorx.InternalError,
			"there are not tags in the stability plan, project_id: %s, plan_id: %s",
			ref.ProjectId, ref.ReferenceId,
		)
	}

	key := fmt.Sprintf("%s:%s:%s", common.ConstLockStabilityPlanProjectIDPlanIDPrefix, ref.ProjectId, ref.ReferenceId)
	fn := func() error {
		var tags []string
		if err = jsonx.UnmarshalFromString(origin.Tags.String, &tags); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.SerializationError, err.Error()),
				"failed to unmarshal the tags[%s], error: %+v",
				origin.Tags.String, err,
			)
		}

		updateTags(fromTag, toTag, tags)
		origin.Tags = sql.NullString{
			String: jsonx.MarshalToStringIgnoreError(tags),
			Valid:  true,
		}

		if _, err = l.svcCtx.StabilityPlanModel.Update(l.ctx, nil, origin); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to update the tags[%s => %s] of stability plan[%s], error: %+v",
				fromTag, toTag, origin.Name, err,
			)
		}

		return nil
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}

func (l *ModifyTagLogic) updateUIAgentComponentTags(
	fromTag, toTag string, ref *model.TagReferenceRelationship,
) (err error) {
	origin, err := model.CheckUIAgentComponentByComponentID(
		l.ctx, l.svcCtx.UIAgentComponentModel, ref.ProjectId, ref.ReferenceId,
	)
	if err != nil {
		return err
	} else if !origin.Tags.Valid || origin.Tags.String == "" {
		return errorx.Errorf(
			errorx.InternalError,
			"there are not tags in the ui agent component, project_id: %s, component_id: %s",
			ref.ProjectId, ref.ReferenceId,
		)
	}

	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockUIAgentComponentProjectIDComponentIDPrefix, ref.ProjectId, ref.ReferenceId,
	)
	fn := func() error {
		var tags []string
		if err = jsonx.UnmarshalFromString(origin.Tags.String, &tags); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.SerializationError, err.Error()),
				"failed to unmarshal the tags[%s], error: %+v",
				origin.Tags.String, err,
			)
		}

		updateTags(fromTag, toTag, tags)
		origin.Tags = sql.NullString{
			String: jsonx.MarshalToStringIgnoreError(tags),
			Valid:  true,
		}

		if _, err = l.svcCtx.UIAgentComponentModel.Update(l.ctx, nil, origin); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to update the tags[%s => %s] of ui agent component[%s], error: %+v",
				fromTag, toTag, origin.Name, err,
			)
		}

		return nil
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}

func updateTags(fromTag, toTag string, tags []string) {
	for i := range tags {
		if tags[i] == fromTag {
			tags[i] = toTag
		}
	}
}
