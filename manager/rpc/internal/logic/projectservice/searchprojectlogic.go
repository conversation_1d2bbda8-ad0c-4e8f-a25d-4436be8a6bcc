package projectservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	permcommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/permission/common"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/permission/permissioncontrol"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchProjectLogic struct {
	*BaseLogic

	perm *permissioncontrol.PermissionBusinessData
}

func NewSearchProjectLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchProjectLogic {
	return &SearchProjectLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),

		perm: permissioncontrol.FromContext(ctx),
	}
}

// SearchProject 搜索项目
func (l *SearchProjectLogic) SearchProject(in *pb.SearchProjectReq) (resp *pb.SearchProjectResp, err error) {
	resp = &pb.SearchProjectResp{}

	skip, projectIDs := l.PermFilter()
	selectBuilder, countBuilder := l.generateSearchSqlBuilder(in, skip, projectIDs...)

	count, err := l.svcCtx.ProjectModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to count project, error: %+v", err)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	projects, err := l.svcCtx.ProjectModel.FindNoCacheByQuery(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find project, error: %+v", err)
	}

	resp.Items = make([]*pb.Project, 0, len(projects))
	for _, project := range projects {
		item := &pb.Project{}
		if err = utils.Copy(item, project, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy project[%+v] to response, error: %+v", project, err,
			)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}

// PermFilter 权限过滤
// skip: 是否跳过权限检查
// projectIDs: 当skip=false, 只有该项目列表才允许返回
func (l *SearchProjectLogic) PermFilter() (skip bool, projectIDs []string) {
	if l.perm == nil {
		return true, nil
	}

	// 如果没有配置权限，则获取全部
	if len(l.perm.View) == 0 {
		return true, nil
	}

	// 权限过滤
	projectIDs = make([]string, 0, 10)
	for domainKey, allow := range l.perm.View {
		if allow {
			d, err := permcommon.ParseDomainKey(domainKey)
			if err != nil {
				continue
			}

			if d.GetDomainType() == constants.DomainTypeOfProject {
				projectIDs = append(projectIDs, d.GetDomainId())
			}
		}
	}

	return false, projectIDs
}
