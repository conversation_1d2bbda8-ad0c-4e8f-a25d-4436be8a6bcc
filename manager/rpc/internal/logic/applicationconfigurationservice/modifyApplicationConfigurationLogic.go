package applicationconfigurationservicelogic

import (
	"context"
	"database/sql"
	"sort"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic/hashmap"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pkgpuller"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyApplicationConfigurationLogic struct {
	*BaseLogic
}

func NewModifyApplicationConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyApplicationConfigurationLogic {
	return &ModifyApplicationConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyApplicationConfiguration 编辑应用配置
func (l *ModifyApplicationConfigurationLogic) ModifyApplicationConfiguration(in *pb.ModifyApplicationConfigurationReq) (
	out *pb.ModifyApplicationConfigurationResp, err error,
) {
	var (
		projectID = in.GetProjectId()
		configID  = in.GetConfigId()
		name      = in.GetName()
	)

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	// validate the config_id in req
	origin, err := model.CheckApplicationConfigurationByConfigID(
		l.ctx, l.svcCtx.ApplicationConfigModel, projectID, configID,
	)
	if err != nil {
		return nil, err
	}

	fromPlatformType := commonpb.PlatformType(origin.PlatformType)
	toPlatformType := in.GetPlatformType()
	if toPlatformType != fromPlatformType {
		rrs, err := l.svcCtx.ApplicationConfigReferenceModel.FindByConfigID(l.ctx, projectID, configID)
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find application configuration reference, project_id: %s, config_id: %s, name: %s, error: %+v",
				projectID, configID, name, err,
			)
		} else if len(rrs) > 0 {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot to modify the platform type of application configuration which has been referenced by %d items[s], project_id: %s, config_id: %s, name: %s, platform_type: %s -> %s",
				len(rrs), projectID, configID, name,
				protobuf.GetEnumStringOf(fromPlatformType), protobuf.GetEnumStringOf(toPlatformType),
			)
		}
	}

	// validate the app_download_link in req if the empty is valid
	if len(in.GetAppDownloadLink()) == 0 {
		_, err = pkgpuller.NewAppPkgPuller(l.ctx, pkgpuller.AppPkgNameType(in.GetAppId()), "")
		if err != nil {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the app_download_link can't be empty, error: %+v", err,
			)
		}
	}

	// validate the prompts in req
	promptMap, promptSet, err := l.checkAndGetPrompts(in.GetProjectId(), in.GetPrompts()...)
	if err != nil {
		return nil, err
	}

	var config *model.ApplicationConfiguration

	key := l.generateLockKey(in.GetProjectId(), in.GetConfigId())
	fn := func() error {
		config, err = l.modify(in, origin, promptSet)
		return err
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	out = &pb.ModifyApplicationConfigurationResp{
		Configuration: &pb.ApplicationConfiguration{
			Prompts: make([]*pb.PromptConfiguration, 0, promptSet.Size()),
		},
	}
	if err = utils.Copy(out.Configuration, config, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy application configuration to response, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(config), err,
		)
	}

	// load prompt configurations
	if err = l.loadPromptConfigurations(out.GetConfiguration(), promptMap, promptSet); err != nil {
		return nil, err
	}

	return out, nil
}

func (l *ModifyApplicationConfigurationLogic) modify(
	in *pb.ModifyApplicationConfigurationReq, origin *model.ApplicationConfiguration, prompts *set.Set[string],
) (*model.ApplicationConfiguration, error) {
	now := time.Now()
	config := &model.ApplicationConfiguration{
		Id:        origin.Id,
		ProjectId: origin.ProjectId,
		ConfigId:  origin.ConfigId,
		Name:      in.GetName(),
		Description: sql.NullString{
			String: in.GetDescription(),
			Valid:  in.GetDescription() != "",
		},
		PlatformType:    int64(in.GetPlatformType()),
		AppId:           in.GetAppId(),
		AppDownloadLink: in.GetAppDownloadLink(),
		CreatedBy:       origin.CreatedBy,
		UpdatedBy:       l.currentUser.Account,
		CreatedAt:       origin.CreatedAt,
		UpdatedAt:       now,
	}

	fromMap, fromSet, err := l.getPromptReferences(config.ProjectId, config.ConfigId)
	if err != nil {
		return nil, err
	}
	removes := fromSet.Difference(prompts)
	increases := prompts.Difference(fromSet)

	references := make([]*model.PromptConfigurationReferenceRelationship, 0, increases.Size())
	increases.Each(
		func(key string) {
			references = append(
				references, &model.PromptConfigurationReferenceRelationship{
					ProjectId:     config.ProjectId,
					ReferenceType: common.ConstReferenceTypeApplication,
					ReferenceId:   config.ConfigId, // application configuration id
					ConfigId:      key,             // prompt configuration id
					CreatedBy:     l.currentUser.Account,
					UpdatedBy:     l.currentUser.Account,
					CreatedAt:     now,
					UpdatedAt:     now,
				},
			)
		},
	)

	// modify application configuration in a transaction
	if err = l.svcCtx.ApplicationConfigModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err = l.svcCtx.ApplicationConfigModel.Update(context, session, config); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.ApplicationConfigModel.Table(), jsonx.MarshalIgnoreError(config), err,
				)
			}

			for _, key := range removes.Keys() {
				val, ok := fromMap.Get(key)
				if !ok || val.Id == 0 {
					continue
				}

				if err := l.svcCtx.PromptConfigReferenceModel.LogicDelete(context, session, val.Id); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to delete item from table, table: %s, item: %s, error: %+v",
						l.svcCtx.PromptConfigReferenceModel.Table(), jsonx.MarshalIgnoreError(val), err,
					)
				}
			}

			if _, err = l.svcCtx.PromptConfigReferenceModel.BatchInsert(context, session, references); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to batch insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PromptConfigReferenceModel.Table(), jsonx.MarshalIgnoreError(references), err,
				)
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return config, nil
}

func (l *ModifyApplicationConfigurationLogic) loadPromptConfigurations(
	config *pb.ApplicationConfiguration, promptMap *hashmap.Map[string, *model.PromptConfiguration],
	promptSet *set.Set[string],
) error {
	for _, configID := range promptSet.Keys() {
		prompt, ok := promptMap.Get(configID)
		if !ok {
			continue
		}

		item := &pb.PromptConfiguration{}
		if err := utils.Copy(item, prompt, l.converters...); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy prompt configuration to response, config: %s, error: %+v",
				jsonx.MarshalIgnoreError(prompt), err,
			)
		}

		config.Prompts = append(config.Prompts, item)
	}
	sort.SliceStable(
		config.GetPrompts(), func(i, j int) bool {
			return config.GetPrompts()[i].GetCategory() < config.GetPrompts()[j].GetCategory()
		},
	)

	return nil
}
