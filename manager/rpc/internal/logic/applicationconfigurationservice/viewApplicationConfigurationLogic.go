package applicationconfigurationservicelogic

import (
	"context"
	"sort"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewApplicationConfigurationLogic struct {
	*BaseLogic
}

func NewViewApplicationConfigurationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewApplicationConfigurationLogic {
	return &ViewApplicationConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewApplicationConfiguration 查看应用配置
func (l *ViewApplicationConfigurationLogic) ViewApplicationConfiguration(in *pb.ViewApplicationConfigurationReq) (out *pb.ViewApplicationConfigurationResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the config_id in req
	config, err := model.CheckApplicationConfigurationByConfigID(
		l.ctx, l.svcCtx.ApplicationConfigModel, in.GetProjectId(), in.GetConfigId(),
	)
	if err != nil {
		return nil, err
	}

	out = &pb.ViewApplicationConfigurationResp{Configuration: &pb.ApplicationConfiguration{}}
	if err = utils.Copy(out.Configuration, config, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy application configuration to response, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(config), err,
		)
	}

	// load prompt configurations
	if err = l.loadPromptConfigurations(out.Configuration); err != nil {
		return nil, err
	}

	return out, nil
}

func (l *ViewApplicationConfigurationLogic) loadPromptConfigurations(config *pb.ApplicationConfiguration) error {
	// find prompt configuration references
	_, refSet, err := l.getPromptReferences(config.ProjectId, config.ConfigId)
	if err != nil {
		return err
	}

	// find prompt configurations
	promptMap, promptSet, err := l.getPrompts(config.ProjectId, refSet.Keys()...)
	if err != nil {
		return err
	}

	config.Prompts = make([]*pb.PromptConfiguration, 0, promptSet.Size())
	for _, configID := range promptSet.Keys() {
		prompt, ok := promptMap.Get(configID)
		if !ok {
			continue
		}

		item := &pb.PromptConfiguration{}
		if err = utils.Copy(item, prompt, l.converters...); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy prompt configuration to response, config: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(prompt), err,
			)
		}

		config.Prompts = append(config.Prompts, item)
	}
	sort.SliceStable(
		config.GetPrompts(), func(i, j int) bool {
			return config.GetPrompts()[i].GetCategory() < config.GetPrompts()[j].GetCategory()
		},
	)

	return nil
}
