package applicationconfigurationservicelogic

import (
	"context"
	"sort"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchApplicationConfigurationLogic struct {
	*BaseLogic
}

func NewSearchApplicationConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchApplicationConfigurationLogic {
	return &SearchApplicationConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchApplicationConfiguration 搜索应用配置
func (l *SearchApplicationConfigurationLogic) SearchApplicationConfiguration(in *pb.SearchApplicationConfigurationReq) (
	out *pb.SearchApplicationConfigurationResp, err error,
) {
	out = &pb.SearchApplicationConfigurationResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	req := model.SearchApplicationConfigurationReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
	}
	count, err := l.svcCtx.ApplicationConfigModel.FindCountBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count application configurations, project_id: %s, error: %+v",
			in.GetProjectId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	configs, err := l.svcCtx.ApplicationConfigModel.FindAllBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find application configurations, project_id: %s, error: %+v",
			in.GetProjectId(), err,
		)
	}

	out.Items = make([]*pb.ApplicationConfiguration, 0, len(configs))
	for _, config := range configs {
		item := &pb.ApplicationConfiguration{}
		if err = utils.Copy(item, config, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy application configuration to response, config: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(config), err,
			)
		}

		// load prompt configurations
		if err = l.loadPromptConfigurations(item); err != nil {
			return nil, err
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}

func (l *SearchApplicationConfigurationLogic) loadPromptConfigurations(config *pb.ApplicationConfiguration) error {
	// find prompt configuration references
	_, refSet, err := l.getPromptReferences(config.ProjectId, config.ConfigId)
	if err != nil {
		return err
	}

	// find prompt configurations
	promptMap, promptSet, err := l.getPrompts(config.ProjectId, refSet.Keys()...)
	if err != nil {
		return err
	}

	config.Prompts = make([]*pb.PromptConfiguration, 0, promptSet.Size())
	for _, configID := range promptSet.Keys() {
		prompt, ok := promptMap.Get(configID)
		if !ok {
			continue
		}

		item := &pb.PromptConfiguration{}
		if err = utils.Copy(item, prompt, l.converters...); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy prompt configuration to response, config: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(prompt), err,
			)
		}

		config.Prompts = append(config.Prompts, item)
	}
	sort.SliceStable(
		config.GetPrompts(), func(i, j int) bool {
			return config.GetPrompts()[i].GetCategory() < config.GetPrompts()[j].GetCategory()
		},
	)

	return nil
}
