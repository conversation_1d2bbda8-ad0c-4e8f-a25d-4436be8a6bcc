package applicationconfigurationservicelogic

import (
	"context"
	"database/sql"
	"sort"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic/hashmap"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pkgpuller"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateApplicationConfigurationLogic struct {
	*BaseLogic
}

func NewCreateApplicationConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateApplicationConfigurationLogic {
	return &CreateApplicationConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateApplicationConfiguration 创建应用配置
func (l *CreateApplicationConfigurationLogic) CreateApplicationConfiguration(in *pb.CreateApplicationConfigurationReq) (
	out *pb.CreateApplicationConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the app_download_link in req if the empty is valid
	if len(in.GetAppDownloadLink()) == 0 {
		_, err = pkgpuller.NewAppPkgPuller(l.ctx, pkgpuller.AppPkgNameType(in.GetAppId()), "")
		if err != nil {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the app_download_link can't be empty, error: %+v", err,
			)
		}
	}

	// validate the prompts in req
	promptMap, promptSet, err := l.checkAndGetPrompts(in.GetProjectId(), in.GetPrompts()...)
	if err != nil {
		return nil, err
	}

	config, err := l.create(in, promptMap)
	if err != nil {
		return nil, err
	}

	out = &pb.CreateApplicationConfigurationResp{
		Configuration: &pb.ApplicationConfiguration{
			Prompts: make([]*pb.PromptConfiguration, 0, promptSet.Size()),
		},
	}
	if err = utils.Copy(out.Configuration, config, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy application configuration to response, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(config), err,
		)
	}

	// load prompt configurations
	if err = l.loadPromptConfigurations(out.GetConfiguration(), promptMap, promptSet); err != nil {
		return nil, err
	}

	return out, nil
}

func (l *CreateApplicationConfigurationLogic) create(
	in *pb.CreateApplicationConfigurationReq, prompts *hashmap.Map[string, *model.PromptConfiguration],
) (
	*model.ApplicationConfiguration, error,
) {
	configID, err := l.generateConfigID(in.GetProjectId())
	if err != nil {
		return nil, err
	}

	now := time.Now()
	config := &model.ApplicationConfiguration{
		ProjectId: in.GetProjectId(),
		ConfigId:  configID,
		Name:      in.GetName(),
		Description: sql.NullString{
			String: in.GetDescription(),
			Valid:  in.GetDescription() != "",
		},
		PlatformType:    int64(in.GetPlatformType()),
		AppId:           in.GetAppId(),
		AppDownloadLink: in.GetAppDownloadLink(),
		CreatedBy:       l.currentUser.Account,
		UpdatedBy:       l.currentUser.Account,
		CreatedAt:       now,
		UpdatedAt:       now,
	}

	references := make([]*model.PromptConfigurationReferenceRelationship, 0, prompts.Size())
	prompts.Each(
		func(key string, val *model.PromptConfiguration) {
			references = append(
				references, &model.PromptConfigurationReferenceRelationship{
					ProjectId:     config.ProjectId,
					ReferenceType: common.ConstReferenceTypeApplication,
					ReferenceId:   config.ConfigId, // application configuration id
					ConfigId:      val.ConfigId,    // prompt configuration id
					CreatedBy:     l.currentUser.Account,
					UpdatedBy:     l.currentUser.Account,
					CreatedAt:     now,
					UpdatedAt:     now,
				},
			)
		},
	)

	// create application configuration in a transaction
	if err = l.svcCtx.ApplicationConfigModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err = l.svcCtx.ApplicationConfigModel.Insert(context, session, config); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.ApplicationConfigModel.Table(), jsonx.MarshalIgnoreError(config), err,
				)
			}

			if _, err = l.svcCtx.PromptConfigReferenceModel.BatchInsert(context, session, references); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to batch insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PromptConfigReferenceModel.Table(), jsonx.MarshalIgnoreError(references), err,
				)
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return config, nil
}

func (l *CreateApplicationConfigurationLogic) loadPromptConfigurations(
	config *pb.ApplicationConfiguration, promptMap *hashmap.Map[string, *model.PromptConfiguration],
	promptSet *set.Set[string],
) error {
	for _, configID := range promptSet.Keys() {
		prompt, ok := promptMap.Get(configID)
		if !ok {
			continue
		}

		item := &pb.PromptConfiguration{}
		if err := utils.Copy(item, prompt, l.converters...); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy prompt configuration to response, config: %s, error: %+v",
				jsonx.MarshalIgnoreError(prompt), err,
			)
		}

		config.Prompts = append(config.Prompts, item)
	}
	sort.SliceStable(
		config.GetPrompts(), func(i, j int) bool {
			return config.GetPrompts()[i].GetCategory() < config.GetPrompts()[j].GetCategory()
		},
	)

	return nil
}
