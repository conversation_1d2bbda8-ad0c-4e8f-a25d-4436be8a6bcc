package applicationconfigurationservicelogic

import (
	"context"
	"fmt"
	"sync"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	cacheMutex  sync.RWMutex
	promptCache map[string]*model.PromptConfiguration
	converters  []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		promptCache: make(map[string]*model.PromptConfiguration, constants.ConstDefaultMakeMapSize),
		converters:  []qetutils.TypeConverter{},
	}
}

func (l *BaseLogic) generateConfigID(projectID string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenApplicationConfigID), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.ApplicationConfigModel.FindOneByProjectIdConfigId(l.ctx, projectID, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	configID := g.Next()
	if configID == "" {
		return "", errorx.Err(
			errorx.GenerateUniqueIdFailure, "failed to generate application config id, please try it later",
		)
	}

	return configID, nil
}

// generateLockKey 生成分布式锁的key
func (l *BaseLogic) generateLockKey(projectID, configID string) string {
	return fmt.Sprintf("%s:%s:%s", common.ConstLockApplicationConfigProjectIDConfigIDPrefix, projectID, configID)
}

func (l *BaseLogic) checkAndGetPrompts(
	projectID string, promptConfigIDs ...string,
) (*hashmap.Map[string, *model.PromptConfiguration], *set.Set[string], error) {
	m, s, err := l.getPrompts(projectID, promptConfigIDs...)
	if err != nil {
		return nil, nil, err
	}

	cache := make(map[int64]lang.PlaceholderType, m.Size())
	for _, configID := range s.Keys() {
		prompt, ok := m.Get(configID)
		if !ok {
			return nil, nil, errorx.Errorf(
				errorx.NotExists,
				"the prompt configuration doesn't exist, project_id: %s, config_id: %s",
				projectID, configID,
			)
		}

		if prompt.Purpose != int64(commonpb.PromptPurpose_PromptPurpose_UI_AGENT) {
			return nil, nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the prompt configuration is not for ui agent, project_id: %s, config_id: %s, name: %s, purpose: %d",
				prompt.ProjectId, prompt.ConfigId, prompt.Name, prompt.Purpose,
			)
		}

		// validate the prompt configuration is not for the same category
		if _, ok = cache[prompt.Category]; ok {
			return nil, nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot select multiple prompt configurations for the same category, project_id: %s, category: %d",
				prompt.ProjectId, prompt.Category,
			)
		}

		cache[prompt.Category] = lang.Placeholder
	}

	return m, s, nil
}

func (l *BaseLogic) getPrompts(
	projectID string, promptConfigIDs ...string,
) (*hashmap.Map[string, *model.PromptConfiguration], *set.Set[string], error) {
	m := hashmap.New[string, *model.PromptConfiguration](
		uint64(len(promptConfigIDs)), generic.Equals[string], generic.HashString,
	)
	s := set.NewHashset[string](uint64(len(promptConfigIDs)), generic.Equals[string], generic.HashString)
	if err := mr.MapReduceVoid[string, *model.PromptConfiguration](
		func(source chan<- string) {
			cache := make(map[string]lang.PlaceholderType, len(promptConfigIDs))
			for _, promptConfigID := range promptConfigIDs {
				if _, ok := cache[promptConfigID]; ok {
					continue
				}

				cache[promptConfigID] = lang.Placeholder
				source <- promptConfigID
			}
		},
		func(item string, writer mr.Writer[*model.PromptConfiguration], cancel func(error)) {
			l.cacheMutex.RLock()
			config, ok := l.promptCache[item]
			l.cacheMutex.RUnlock()
			if !ok {
				var err error
				config, err = model.CheckPromptConfigurationByConfigID(
					l.ctx, l.svcCtx.PromptConfigModel, projectID, item,
				)
				if err != nil {
					l.Error(err)
					return
				}

				// NOTE: if the `*model.PromptConfiguration` object is modified outside,
				// it will affect the data in the cache
				l.cacheMutex.Lock()
				l.promptCache[item] = config
				l.cacheMutex.Unlock()
			}

			writer.Write(config)
		},
		func(pipe <-chan *model.PromptConfiguration, cancel func(error)) {
			for item := range pipe {
				if item == nil {
					continue
				}

				m.Put(item.ConfigId, item)
				s.Put(item.ConfigId)
			}
		},
		mr.WithContext(l.ctx),
	); err != nil {
		return nil, nil, err
	}

	return m, &s, nil
}

func (l *BaseLogic) getPromptReferences(projectID, appConfigID string) (
	*hashmap.Map[string, *model.PromptConfigurationReferenceRelationship], *set.Set[string], error,
) {
	references, err := l.svcCtx.PromptConfigReferenceModel.FindByReference(
		l.ctx, projectID, common.ConstReferenceTypeApplication, appConfigID,
	)
	if err != nil {
		return nil, nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find prompt configuration references, project_id: %s, reference_type: %s, reference_id: %s, error: %+v",
			projectID, common.ConstReferenceTypeApplication, appConfigID, err,
		)
	}

	m := hashmap.New[string, *model.PromptConfigurationReferenceRelationship](
		uint64(len(references)), generic.Equals[string], generic.HashString,
	)
	s := set.NewHashset[string](uint64(len(references)), generic.Equals[string], generic.HashString)
	for _, reference := range references {
		if reference.ConfigId == "" {
			continue
		}

		m.Put(reference.ConfigId, reference)
		s.Put(reference.ConfigId)
	}

	return m, &s, nil
}
