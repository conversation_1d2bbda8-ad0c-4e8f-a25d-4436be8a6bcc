package promptconfigurationservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchPromptConfigurationLogic struct {
	*BaseLogic
}

func NewSearchPromptConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchPromptConfigurationLogic {
	return &SearchPromptConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchPromptConfiguration 搜索Prompt配置
func (l *SearchPromptConfigurationLogic) SearchPromptConfiguration(in *pb.SearchPromptConfigurationReq) (
	out *pb.SearchPromptConfigurationResp, err error,
) {
	out = &pb.SearchPromptConfigurationResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	req := model.SearchPromptConfigurationReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
		Purpose: int64(in.GetPurpose()),
	}
	count, err := l.svcCtx.PromptConfigModel.FindCountBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count prompt configurations, project_id: %s, purpose: %s, error: %+v",
			in.GetProjectId(), protobuf.GetEnumStringOf(in.GetPurpose()), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	configs, err := l.svcCtx.PromptConfigModel.FindAllBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find prompt configurations, project_id: %s, purpose: %s, error: %+v",
			in.GetProjectId(), protobuf.GetEnumStringOf(in.GetPurpose()), err,
		)
	}

	out.Items = make([]*pb.PromptConfiguration, 0, len(configs))
	for _, config := range configs {
		item := &pb.PromptConfiguration{}
		if err = utils.Copy(item, config, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy prompt configuration to response, config: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(config), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
