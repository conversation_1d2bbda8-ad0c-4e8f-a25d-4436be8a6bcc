package promptconfigurationservicelogic

import (
	"context"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemovePromptConfigurationLogic struct {
	*BaseLogic
}

func NewRemovePromptConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *RemovePromptConfigurationLogic {
	return &RemovePromptConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemovePromptConfiguration 删除Prompt配置
func (l *RemovePromptConfigurationLogic) RemovePromptConfiguration(in *pb.RemovePromptConfigurationReq) (
	out *pb.RemovePromptConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	configIDs := in.GetConfigIds()
	workers := len(configIDs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach[string](
		func(source chan<- string) {
			for _, configID := range configIDs {
				source <- configID
			}
		}, func(item string) {
			if e := l.remove(in.GetProjectId(), item); e != nil {
				err = multierror.Append(err, e)
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemovePromptConfigurationResp{}, err
}

func (l *RemovePromptConfigurationLogic) remove(projectID, configID string) error {
	// validate the config_id in req
	config, err := model.CheckPromptConfigurationByConfigID(l.ctx, l.svcCtx.PromptConfigModel, projectID, configID)
	if err != nil {
		if re, ok := errorx.RootError(err); ok && re.Code() == errorx.NotExists {
			return nil
		}

		return err
	}

	// check if prompt configuration is referenced by other resources
	rrs, err := l.svcCtx.PromptConfigReferenceModel.FindByConfigID(l.ctx, projectID, configID)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find prompt configuration reference, project_id: %s, config_id: %s, name: %s, error: %+v",
			projectID, configID, config.Name, err,
		)
	} else if len(rrs) > 0 {
		return errorx.Errorf(
			errorx.ProhibitedBehavior,
			"cannot to delete the prompt configuration which has been referenced by %d items[s], project_id: %s, config_id: %s, name: %s",
			len(rrs), projectID, configID, config.Name,
		)
	}

	key := l.generateLockKey(projectID, configID)
	fn := func() error {
		return l.svcCtx.PromptConfigModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				// Table: prompt_configuration_reference_relationship
				if _, err := l.svcCtx.PromptConfigReferenceModel.RemoveByConfigID(
					context, session, projectID, configID,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove prompt configuration reference relationship, project_id: %s, config_id: %s, error: %+v",
						projectID, configID, err,
					)
				}

				// Table: prompt_configuration
				if _, err := l.svcCtx.PromptConfigModel.RemoveByConfigID(
					context, session, projectID, configID,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove prompt configuration, project_id: %s, config_id: %s, error: %+v",
						projectID, configID, err,
					)
				}

				return nil
			},
		)
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}
