package promptconfigurationservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchPromptConfigurationReferenceLogic struct {
	*BaseLogic
}

func NewSearchPromptConfigurationReferenceLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchPromptConfigurationReferenceLogic {
	return &SearchPromptConfigurationReferenceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchPromptConfigurationReferenceLogic) SearchPromptConfigurationReference(in *pb.SearchPromptConfigurationReferenceReq) (
	out *pb.SearchPromptConfigurationReferenceResp, err error,
) {
	out = &pb.SearchPromptConfigurationReferenceResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the config_id in req
	if _, err = model.CheckPromptConfigurationByConfigID(
		l.ctx, l.svcCtx.PromptConfigModel, in.GetProjectId(), in.GetConfigId(),
	); err != nil {
		return nil, err
	}

	req := model.SearchPromptConfigurationReferenceReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  in.GetProjectId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
		ConfigID: in.GetConfigId(),
	}
	count, err := l.svcCtx.PromptConfigModel.FindCountReferenceBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count prompt configuration references, project_id: %s, config_id: %s, error: %+v",
			in.GetProjectId(), in.GetConfigId(), err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	records, err := l.svcCtx.PromptConfigModel.FindReferenceBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find reference data of prompt configuration, project_id: %s, config_id: %s, error: %+v",
			in.GetProjectId(), in.GetConfigId(), err,
		)
	}

	out.Items = make([]*pb.SearchPromptConfigurationReferenceItem, 0, len(records))
	for _, record := range records {
		item := &pb.SearchPromptConfigurationReferenceItem{}
		if err = utils.Copy(item, record, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy reference data of prompt configuration to response, data: %s, error: %+v",
				jsonx.MarshalIgnoreError(record), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
