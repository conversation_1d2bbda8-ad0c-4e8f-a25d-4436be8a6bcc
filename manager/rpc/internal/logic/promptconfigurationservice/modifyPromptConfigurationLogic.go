package promptconfigurationservicelogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyPromptConfigurationLogic struct {
	*BaseLogic
}

func NewModifyPromptConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyPromptConfigurationLogic {
	return &ModifyPromptConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyPromptConfiguration 编辑Prompt配置
func (l *ModifyPromptConfigurationLogic) ModifyPromptConfiguration(in *pb.ModifyPromptConfigurationReq) (
	out *pb.ModifyPromptConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the config_id in req
	origin, err := model.CheckPromptConfigurationByConfigID(
		l.ctx, l.svcCtx.PromptConfigModel, in.GetProjectId(), in.GetConfigId(),
	)
	if err != nil {
		return nil, err
	}

	var config *model.PromptConfiguration

	key := l.generateLockKey(in.GetProjectId(), in.GetConfigId())
	fn := func() error {
		config, err = l.modify(in, origin)
		return err
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	out = &pb.ModifyPromptConfigurationResp{Configuration: &pb.PromptConfiguration{}}
	if err = utils.Copy(out.Configuration, config, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy prompt configuration to response, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(config), err,
		)
	}

	return out, nil
}

func (l *ModifyPromptConfigurationLogic) modify(
	in *pb.ModifyPromptConfigurationReq, origin *model.PromptConfiguration,
) (*model.PromptConfiguration, error) {
	config := &model.PromptConfiguration{
		Id:        origin.Id,
		ProjectId: origin.ProjectId,
		ConfigId:  origin.ConfigId,
		Purpose:   origin.Purpose,
		Category:  origin.Category,
		Name:      in.GetName(),
		Description: sql.NullString{
			String: in.GetDescription(),
			Valid:  in.GetDescription() != "",
		},
		Content:   in.GetContent(),
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
		UpdatedAt: time.Now(),
	}

	// modify prompt configuration in a transaction
	if err := l.svcCtx.PromptConfigModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.PromptConfigModel.Update(context, session, config); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PromptConfigModel.Table(), jsonx.MarshalIgnoreError(config), err,
				)
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return config, nil
}
