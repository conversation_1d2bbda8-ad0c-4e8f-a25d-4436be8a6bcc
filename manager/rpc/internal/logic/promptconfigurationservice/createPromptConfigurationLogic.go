package promptconfigurationservicelogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreatePromptConfigurationLogic struct {
	*BaseLogic
}

func NewCreatePromptConfigurationLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreatePromptConfigurationLogic {
	return &CreatePromptConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreatePromptConfiguration 创建Prompt配置
func (l *CreatePromptConfigurationLogic) CreatePromptConfiguration(in *pb.CreatePromptConfigurationReq) (
	out *pb.CreatePromptConfigurationResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	config, err := l.create(in)
	if err != nil {
		return nil, err
	}

	out = &pb.CreatePromptConfigurationResp{Configuration: &pb.PromptConfiguration{}}
	if err = utils.Copy(out.Configuration, config, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy prompt configuration to response, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(config), err,
		)
	}

	return out, nil
}

func (l *CreatePromptConfigurationLogic) create(in *pb.CreatePromptConfigurationReq) (
	*model.PromptConfiguration, error,
) {
	configID, err := l.generateConfigID(in.GetProjectId())
	if err != nil {
		return nil, err
	}

	now := time.Now()
	config := &model.PromptConfiguration{
		ProjectId: in.GetProjectId(),
		ConfigId:  configID,
		Purpose:   int64(in.GetPurpose()),
		Category:  int64(in.GetCategory()),
		Name:      in.GetName(),
		Description: sql.NullString{
			String: in.GetDescription(),
			Valid:  in.GetDescription() != "",
		},
		Content:   in.GetContent(),
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: now,
		UpdatedAt: now,
	}

	// create prompt configuration in a transaction
	if err = l.svcCtx.PromptConfigModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err = l.svcCtx.PromptConfigModel.Insert(context, session, config); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PromptConfigModel.Table(), jsonx.MarshalIgnoreError(config), err,
				)
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return config, nil
}
