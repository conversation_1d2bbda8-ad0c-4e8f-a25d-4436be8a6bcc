package slanotifierservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	pb1 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchSlaNotifierLogic struct {
	BaseLogic
}

func NewSearchSlaNotifierLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchSlaNotifierLogic {
	return &SearchSlaNotifierLogic{
		BaseLogic: *newBaseLogic(ctx, svcCtx),
	}
}

// SearchSlaNotifier 获取SLA通知人员
func (l *SearchSlaNotifierLogic) SearchSlaNotifier(in *pb.SearchSlaNotifierReq) (out *pb.SearchSlaNotifierResp, err error) {
	projectId := in.GetProjectId()

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectId); err != nil {
		return nil, err
	}

	out = &pb.SearchSlaNotifierResp{}
	count, err := l.svcCtx.SlaReportNotifierModel.FindCount(l.ctx,
		l.svcCtx.SlaReportNotifierModel.SelectCountBuilder().Where("`project_id` = ?", projectId))
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count SLA report notifier, project_id: %s, error: %+v",
			projectId, err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	notifiers, err := l.svcCtx.SlaReportNotifierModel.FindNoCacheByQuery(l.ctx,
		l.svcCtx.SlaReportNotifierModel.SelectBuilder().Where("`project_id` = ?", projectId))
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find SLA report notifier, project_id: %s, error: %+v",
			projectId, err,
		)
	}

	out.Items = make([]*pb1.UserInfo, 0, len(notifiers))
	for _, notifier := range notifiers {
		item := &pb1.UserInfo{}
		if err = utils.Copy(item, notifier, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy SLA report notifier to response, SLA report notifier: %s, error: %+v",
				jsonx.MarshalIgnoreError(notifier), err,
			)
		}
		if notifier.LarkUserId.Valid {
			item.LarkId = notifier.LarkUserId.String
		}
		out.Items = append(out.Items, item)
	}

	return out, nil
}
