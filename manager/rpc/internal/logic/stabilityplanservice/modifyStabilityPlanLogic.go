package stabilityplanservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/gorhill/cronexpr"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pkgpuller"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyStabilityPlanLogic struct {
	*BaseLogic
}

func NewModifyStabilityPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyStabilityPlanLogic {
	return &ModifyStabilityPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyStabilityPlan 编辑稳定性测试计划
func (l *ModifyStabilityPlanLogic) ModifyStabilityPlan(in *pb.ModifyStabilityPlanReq) (
	out *pb.ModifyStabilityPlanResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	var c *model.Category
	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeStabilityPlan, in.GetCategoryId(),
	); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the type of parent category[%s] does not support creation of sub category",
			c.CategoryType,
		)
	}

	// validate the plan_id in req
	origin, err := model.CheckStabilityPlanByPlanId(
		l.ctx, l.svcCtx.StabilityPlanModel, in.GetProjectId(), in.GetPlanId(),
	)
	if err != nil {
		return nil, err
	}

	// validate the account_config_id in req
	if in.GetAccountConfigId() != "" {
		if _, err = model.CheckAccountConfigByConfigId(
			l.ctx, l.svcCtx.AccountConfigModel, in.GetProjectId(), in.GetAccountConfigId(),
		); err != nil {
			return nil, err
		}
	}

	// validate the app_download_link in req if the empty is valid
	if len(in.GetAppDownloadLink()) == 0 {
		_, err = pkgpuller.NewAppPkgPuller(l.ctx, pkgpuller.AppPkgNameType(in.GetPackageName()), "")
		if err != nil {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the app_download_link can't be empty, error: %+v", err,
			)
		}
	}

	var stabilityPlan *model.StabilityPlan
	key := fmt.Sprintf(
		"%s:%s:%s", common.ConstLockStabilityPlanProjectIDPlanIDPrefix, in.GetProjectId(), in.GetPlanId(),
	)
	fn := func() error {
		stabilityPlan, err = l.modify(in, origin)
		return err
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	out = &pb.ModifyStabilityPlanResp{Plan: &pb.StabilityPlan{}}
	if err = utils.Copy(out.Plan, stabilityPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf plan to response, stability plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(stabilityPlan), err,
		)
	}

	return out, nil
}

func (l *ModifyStabilityPlanLogic) modify(
	req *pb.ModifyStabilityPlanReq, origin *model.StabilityPlan,
) (*model.StabilityPlan, error) {
	var (
		projectID       = req.GetProjectId()
		planID          = req.GetPlanId()
		description     = req.GetDescription()
		cronExpression  = req.GetCronExpression()
		accountConfigID = req.GetAccountConfigId()
		maintainedBy    = req.GetMaintainedBy()

		tags, activities sql.NullString
	)

	if len(req.GetTags()) > 0 {
		// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		tags.String = jsonx.MarshalToStringIgnoreError(req.GetTags())
		tags.Valid = true
	}

	if len(req.GetActivities()) > 0 {
		// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		activities.String = jsonx.MarshalToStringIgnoreError(req.GetActivities())
		activities.Valid = true
	}

	if maintainedBy != "" {
		if user, err := l.getUserInfoByAccount(maintainedBy); err != nil {
			return nil, err
		} else if !user.GetEnabled() {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot assign a disabled user as a maintainer of stability plan, project_id: %s, plan_id: %s, maintained_by: %s",
				projectID, planID, maintainedBy,
			)
		}
	} else {
		maintainedBy = origin.MaintainedBy.String
	}

	// validate the cron_expression in req if the plan type is SCHEDULE
	if req.GetType() == commonpb.TriggerMode_SCHEDULE {
		_, err := cronexpr.Parse(req.GetCronExpression())
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CronExpressionParseError, err.Error()),
				"failed to parse the cron expression, cron: %s, error: %+v",
				req.GetCronExpression(), err,
			)
		}
	}

	now := time.Now()
	stabilityPlan := &model.StabilityPlan{
		Id:         origin.Id,
		ProjectId:  origin.ProjectId,
		CategoryId: req.GetCategoryId(),
		PlanId:     origin.PlanId,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: description,
			Valid:  description != "",
		},
		State:        int64(req.GetState()),
		Type:         protobuf.GetEnumStringOf(req.GetType()),
		PriorityType: int64(req.GetPriorityType()),
		CronExpression: sql.NullString{
			String: cronExpression,
			Valid:  cronExpression != "",
		},
		Tags: tags,
		AccountConfigId: sql.NullString{
			String: accountConfigID,
			Valid:  accountConfigID != "",
		},
		DeviceType:   int64(req.GetDeviceType()),
		PlatformType: int64(req.GetPlatformType()),
		Devices: sql.NullString{
			String: protobuf.MarshalJSONToStringIgnoreError(req.GetDevices()),
			Valid:  true,
		},
		PackageName:     req.GetPackageName(),
		AppDownloadLink: req.GetAppDownloadLink(),
		Duration:        int64(req.GetDuration()),
		Activities:      activities,
		CustomScript: sql.NullString{
			String: protobuf.MarshalJSONToStringIgnoreError(req.GetCustomScript()),
			Valid:  true,
		},
		MaintainedBy: sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		},
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
		UpdatedAt: now,
	}

	if err := l.svcCtx.StabilityPlanModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// update the stability plan
			if _, err := l.svcCtx.StabilityPlanModel.Update(context, session, stabilityPlan); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.StabilityPlanModel.Table(), jsonx.MarshalIgnoreError(stabilityPlan), err,
				)
			}

			// update the project and devices reference of stability plan
			if err := l.updateProjectDeviceRelationship(context, session, stabilityPlan, req.GetDevices()); err != nil {
				return err
			}

			// update the new tag and tag reference of stability plan
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:     stabilityPlan.ProjectId,
					ReferenceType: common.ConstReferenceTypeStabilityPlan,
					ReferenceId:   stabilityPlan.PlanId,
					Tags:          req.GetTags(),
				},
			); err != nil {
				return err
			}

			// update notify item of stability plan
			if err := l.updateNotifyItems(context, session, stabilityPlan, req.GetLarkChats()); err != nil {
				return err
			}

			return l.updateScheduleTask(req, origin)
		},
	); err != nil {
		return nil, err
	}

	return stabilityPlan, nil
}
