package perfplanv2servicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/emirpasic/gods/maps/hashmap"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	beatcommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/common"
	beatpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/beat/rpc/pb"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/calculate"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/beat"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	notifyservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/notifyservice"
	tagservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/tagservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

var (
	workingTimeLeft, _  = time.Parse("15:04", constants.ConstWorkingTimeRangeOfLeft)
	workingTimeRight, _ = time.Parse("15:04", constants.ConstWorkingTimeRangeOfRight)
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	createTagLogic    *tagservicelogic.CreateTagLogic
	createNotifyLogic *notifyservicelogic.CreatePlanNotifyLogic

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		createTagLogic:    tagservicelogic.NewCreateTagLogic(ctx, svcCtx),
		createNotifyLogic: notifyservicelogic.NewCreatePlanNotifyLogic(ctx, svcCtx),

		converters: []qetutils.TypeConverter{
			commonpb.StringToTriggerMode(),
			commonpb.StringToProtocol(),
			commonpb.StringToTargetEnvironment(),
			commonpb.StringToMetricType(),
			logic.Int64ToCommonState(),
			logic.SqlNullStringToTags(),
			logic.StringToRateLimits(),
			logic.StringToPerfCaseSteps(),
			logic.SqlNullStringToPerfCaseSteps(),
			logic.StringToStatsOfSteps(),
			logic.SqlNullStringToExcludePaths(),
			logic.SqlNullStringToExcludeFiles(),
		},
	}
}

func (l *BaseLogic) validateProtobufConfigs(projectID string, configIDs []string) error {
	cache := make(map[string]lang.PlaceholderType, len(configIDs))
	for _, configID := range configIDs {
		if _, ok := cache[configID]; ok {
			return errorx.Errorf(
				errorx.ProhibitedBehavior, "cannot select the same protobuf configuration, config_id: %s", configID,
			)
		}

		if _, err := model.CheckProtobufConfigByConfigID(
			l.ctx, l.svcCtx.ProtobufConfigModel, projectID, configID,
		); err != nil {
			return err
		}

		cache[configID] = lang.Placeholder
	}

	return nil
}

func (l *BaseLogic) validateStopRules(projectID string, ruleIDs []string) error {
	cache := make(map[string]string, len(ruleIDs))
	for _, ruleID := range ruleIDs {
		rule, err := model.CheckPerfStopRuleByRuleID(l.ctx, l.svcCtx.PerfStopRuleModel, projectID, ruleID)
		if err != nil {
			return err
		}

		if v, ok := cache[rule.MetricType]; ok {
			return errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the metric type of perf stop rule is duplicated, metric_type: %s, rules: [%s, %s]",
				rule.MetricType, v, rule.Name,
			)
		}
		cache[rule.MetricType] = rule.Name
	}

	return nil
}

func (l *BaseLogic) generatePlanID(projectID string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenPerfPlanID), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.PerfPlanV2Model.FindOneByProjectIdPlanId(l.ctx, projectID, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	planID := g.Next()
	if planID == "" {
		return "", errorx.Err(
			errorx.GenerateUniqueIdFailure, "failed to generate perf plan id, please try it later",
		)
	}

	return planID, nil
}

func (l *BaseLogic) getUserInfoByAccount(account string) (*userpb.UserInfo, error) {
	resp, err := l.svcCtx.UserRPC.ViewUser(
		l.ctx, &userpb.GetUserReq{
			Account: account,
		},
	)
	if err != nil {
		return nil, err
	}

	return resp.GetUserInfo(), nil
}

type perfPlanCaseItem struct {
	perfPlanCase *pb.PerfPlanCaseV2Item
	durations    calculate.ExecutionDurations
}

func (l *BaseLogic) handlePerfPlanCases(
	projectID string, protocol commonpb.Protocol, authRateLimits []*commonpb.RateLimitV2,
	cases []*pb.PerfPlanCaseV2Item,
) (items []*perfPlanCaseItem, maxDuration time.Duration, err error) {
	items = make([]*perfPlanCaseItem, 0, len(cases))
	err = mr.MapReduceVoid[*pb.PerfPlanCaseV2Item, *perfPlanCaseItem](
		func(source chan<- *pb.PerfPlanCaseV2Item) {
			for _, item := range cases {
				source <- item
			}
		}, func(item *pb.PerfPlanCaseV2Item, writer mr.Writer[*perfPlanCaseItem], cancel func(error)) {
			if item == nil {
				return
			}

			var (
				perfCase *pb.PerfCaseV2
				perfData *model.PerfData
				err1     error
			)
			defer func() {
				if err1 != nil {
					cancel(err1)
				}
			}()

			perfCase, err1 = l.getPerfCaseByCaseID(projectID, item.GetCaseId(), authRateLimits)
			if err1 != nil {
				return
			}

			// Get the perf_case rate_limits configured in perf_plan
			globalRateLimits := item.GetRateLimits()
			if len(globalRateLimits) == 0 {
				err1 = errorx.Errorf(
					errorx.ProhibitedBehavior,
					"the rate_limits must be configured in perf_plan, case_name: %s",
					perfCase.Name,
				)
				return
			}

			if protocol != perfCase.GetProtocol() {
				err1 = errorx.Errorf(
					errorx.ProhibitedBehavior,
					"the protocol of perf plan and perf case is not match, case_name: %s, protocol: %s -> %s",
					perfCase.Name, protobuf.GetEnumStringOf(protocol), perfCase.Protocol,
				)
				return
			}

			if item.GetPerfDataId() != "" {
				// get the number of virtual users of perf data
				perfData, err1 = model.CheckPerfDataByDataID(
					l.ctx, l.svcCtx.PerfDataModel, projectID, item.GetPerfDataId(),
				)
				if err1 != nil {
					return
				}

				item.CustomVu = false
				item.NumberOfVu = uint32(perfData.NumberOfVu)
			} else if !item.GetCustomVu() {
				// calculate the number of virtual users by serial and parallel steps
				item.NumberOfVu = uint32(
					calculate.CalculateNumberOfVirtualUsers(
						perfCase.GetSerialSteps(), perfCase.GetParallelSteps(), globalRateLimits,
					),
				)
			} else if item.GetNumberOfVu() <= 0 {
				err1 = errorx.Errorf(
					errorx.ProhibitedBehavior,
					"when selecting a custom number of virtual users， the number of virtual users cannot be zero, case_name: %s, custom_vu: %t, number_of_vu: %d",
					perfCase.Name, item.GetCustomVu(), item.GetNumberOfVu(),
				)
				return
			}

			lg1 := &commonpb.LoadGenerator{}
			if item.GetCustomLg() {
				if item.GetNumberOfLg() <= 0 {
					err1 = errorx.Errorf(
						errorx.ProhibitedBehavior,
						"when selecting a custom number of load generators， the number of load generators cannot be zero, case_name: %s, custom_lg: %t, number_of_lg: %d",
						perfCase.Name, item.GetCustomLg(), item.GetNumberOfLg(),
					)
					return
				} else if item.GetNumberOfVu() < item.GetNumberOfLg() {
					err1 = errorx.Errorf(
						errorx.ProhibitedBehavior,
						"the number of virtual users cannot be less than the number of load generators, case_name: %s, number_of_vu: %d, number_of_lg: %d",
						perfCase.Name, item.GetNumberOfVu(), item.GetNumberOfLg(),
					)
					return
				} else if item.GetNumberOfVu()/item.GetNumberOfLg() > calculate.ConstMaxVUPerLG {
					err1 = errorx.Errorf(
						errorx.ProhibitedBehavior,
						"the number of virtual users per load generator cannot be greater than %d, case_name: %s, number_of_vu: %d, number_of_lg: %d",
						calculate.ConstMaxVUPerLG, perfCase.Name, item.GetNumberOfVu(), item.GetNumberOfLg(),
					)
					return
				}

				lg1 = &commonpb.LoadGenerator{
					NumberOfLg:       item.GetNumberOfLg(),
					RequestsOfCpu:    item.GetRequestsOfCpu(),
					RequestsOfMemory: item.GetRequestsOfMemory(),
					LimitsOfCpu:      item.GetLimitsOfCpu(),
					LimitsOfMemory:   item.GetLimitsOfMemory(),
				}
			}

			// calculate the load generator resource
			lg2 := calculate.CalculateLoadGeneratorResource(item.NumberOfVu, lg1)
			item.NumberOfLg = lg2.GetNumberOfLg()
			item.RequestsOfCpu = lg2.GetRequestsOfCpu()
			item.RequestsOfMemory = lg2.GetRequestsOfMemory()
			item.LimitsOfCpu = lg2.GetLimitsOfCpu()
			item.LimitsOfMemory = lg2.GetLimitsOfMemory()

			options := make([]calculate.Option, 0, 2)
			switch perfCase.GetProtocol() {
			case commonpb.Protocol_PROTOCOL_TT:
				if len(authRateLimits) > 0 {
					options = append(options, calculate.WithAuthRateLimits(authRateLimits))
				}
			case commonpb.Protocol_PROTOCOL_HTTP,
				commonpb.Protocol_PROTOCOL_GRPC,
				commonpb.Protocol_PROTOCOL_TT_AUTH:
				options = append(options, calculate.WithoutAuth())
			}
			options = append(options, calculate.WithGlobalRateLimits(globalRateLimits))

			durations := calculate.CalculateTotalCaseDuration(
				&commonpb.PerfCaseContentV2{
					SetupSteps:    perfCase.GetSetupSteps(),
					SerialSteps:   perfCase.GetSerialSteps(),
					ParallelSteps: perfCase.GetParallelSteps(),
					TeardownSteps: perfCase.GetTeardownSteps(),
				}, item.GetNumberOfVu(), options...,
			)

			writer.Write(
				&perfPlanCaseItem{
					perfPlanCase: item,
					durations:    durations,
				},
			)
		}, func(pipe <-chan *perfPlanCaseItem, cancel func(error)) {
			for item := range pipe {
				if item.durations.TotalDuration > maxDuration {
					maxDuration = item.durations.TotalDuration
				}
				items = append(items, item)
			}
		},
	)

	return items, maxDuration, err
}

func (l *BaseLogic) getPerfCaseByCaseID(
	projectID, caseID string, authRateLimits []*commonpb.RateLimitV2,
) (*pb.PerfCaseV2, error) {
	item, err := model.CheckPerfCaseV2ByCaseID(
		l.ctx, l.svcCtx.PerfCaseV2Model, projectID, caseID,
	)
	if err != nil {
		return nil, err
	}

	perfCase := &pb.PerfCaseV2{}
	if err = qetutils.Copy(perfCase, item, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf case, perf case: %s, error: %+v",
			jsonx.MarshalIgnoreError(item), err,
		)
	}

	if perfCase.GetProtocol() == commonpb.Protocol_PROTOCOL_TT_AUTH {
		// when the protocol is `TT_AUTH`, set the perf case steps to the built-in default steps
		perfCase.SetupSteps = []*commonpb.PerfCaseStepV2{}
		perfCase.SerialSteps = []*commonpb.PerfCaseStepV2{
			common.GetTTAuthPerfCaseStep(),
		}
		perfCase.ParallelSteps = []*commonpb.PerfCaseStepV2{}
		perfCase.TeardownSteps = []*commonpb.PerfCaseStepV2{}

		if len(perfCase.GetRateLimits()) == 0 {
			// when the protocol is `TT_AUTH`, if the rate limits are not set, set the rate limits to the perf plan auth rate limits
			perfCase.RateLimits = authRateLimits
		}
	}

	return perfCase, nil
}

type stats struct {
	numberOfCases, numberOfSteps int64
	statsOfSteps                 *hashmap.Map
}

func (l *BaseLogic) getStatsByCases(projectID string, cases []*pb.PerfPlanCaseV2Item) (*stats, error) {
	s := &stats{
		statsOfSteps: hashmap.New(),
	}

	if err := mr.MapReduceVoid[string, *model.PerfCaseStepV2](
		func(source chan<- string) {
			for _, item := range cases {
				source <- item.CaseId
				s.numberOfCases += 1
			}
		}, func(item string, writer mr.Writer[*model.PerfCaseStepV2], cancel func(error)) {
			var err_ error
			defer func() {
				if err_ != nil {
					cancel(err_)
				}
			}()

			steps, err_ := l.svcCtx.PerfCaseStepV2Model.FindByCaseID(l.ctx, projectID, item)
			if err_ != nil {
				err_ = errors.Wrapf(
					errorx.Err(errorx.DBError, err_.Error()),
					"failed to find perf case step by case id, project_id: %s, case_id : %s error: %+v",
					projectID, item, err_,
				)
				return
			}

			for _, step := range steps {
				writer.Write(step)
			}
		}, func(pipe <-chan *model.PerfCaseStepV2, cancel func(error)) {
			for item := range pipe {
				if item == nil {
					continue
				}

				if item.Type != string(common.ConstPerfCaseStepTypeSerial) &&
					item.Type != string(common.ConstPerfCaseStepTypeParallel) {
					continue
				}

				s.numberOfSteps += 1
				v, ok1 := s.statsOfSteps.Get(item.TargetRps)
				if !ok1 {
					s.statsOfSteps.Put(
						item.TargetRps, &pb.StatsOfStep{
							TargetRps:     item.TargetRps,
							NumberOfSteps: 1,
						},
					)
				} else if step, ok2 := v.(*pb.StatsOfStep); !ok2 {
					s.statsOfSteps.Put(
						item.TargetRps, &pb.StatsOfStep{
							TargetRps:     item.TargetRps,
							NumberOfSteps: 1,
						},
					)
				} else {
					step.NumberOfSteps += 1
				}
			}
		},
	); err != nil {
		return nil, err
	}

	return s, nil
}

func (l *BaseLogic) updateProtobufConfigRelationship(
	ctx context.Context, session sqlx.Session, perfPlan *model.PerfPlanV2, configs []string,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	rs, err := l.svcCtx.ProtobufConfigReferenceModel.FindByReference(
		ctx, perfPlan.ProjectId, common.ConstReferenceTypePerfPlan, perfPlan.PlanId,
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find protobuf configuration relationship, project_id: %s, plan_id: %s, error: %+v",
			perfPlan.ProjectId, perfPlan.PlanId, err,
		)
	}

	if len(configs) == 0 && len(rs) == 0 {
		return nil
	}

	cache := make(map[string]*model.ProtobufConfigurationReferenceRelationship, len(rs))
	fromSet := set.NewHashset[string](constants.ConstDefaultMakeSliceSize, generic.Equals[string], generic.HashString)
	toSet := set.NewHashset[string](
		constants.ConstDefaultMakeSliceSize, generic.Equals[string], generic.HashString, configs...,
	)
	for _, r := range rs {
		if r == nil {
			continue
		}

		cache[r.ConfigId] = r
		fromSet.Put(r.ConfigId)
	}

	// remove the perf plan and protobuf configuration relationship
	for _, configID := range fromSet.Keys() {
		if toSet.Has(configID) {
			continue
		}

		item, ok := cache[configID]
		if !ok {
			continue
		}

		if err = l.svcCtx.ProtobufConfigReferenceModel.Delete(ctx, session, item.Id); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to delete item from table, table: %s, item: %s, error: %+v",
				l.svcCtx.ProtobufConfigReferenceModel.Table(), jsonx.MarshalIgnoreError(item), err,
			)
		}
	}

	// create the perf plan and protobuf configuration relationship
	for _, configID := range toSet.Keys() {
		if fromSet.Has(configID) {
			continue
		}

		item := &model.ProtobufConfigurationReferenceRelationship{
			ProjectId:     perfPlan.ProjectId,
			ReferenceType: common.ConstReferenceTypePerfPlan,
			ReferenceId:   perfPlan.PlanId,
			ConfigId:      configID,
			CreatedBy:     perfPlan.CreatedBy,
			UpdatedBy:     perfPlan.UpdatedBy,
			CreatedAt:     perfPlan.CreatedAt,
			UpdatedAt:     perfPlan.UpdatedAt,
		}
		if _, err := l.svcCtx.ProtobufConfigReferenceModel.Insert(ctx, session, item); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to insert values to table, table: %s, values: %s, error: %+v",
				l.svcCtx.ProtobufConfigReferenceModel.Table(), jsonx.MarshalIgnoreError(item), err,
			)
		}
	}

	return nil
}

func (l *BaseLogic) updatePerfStopRuleRelationship(
	ctx context.Context, session sqlx.Session, perfPlan *model.PerfPlanV2, rules []string,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	rs, err := l.svcCtx.PerfPlanRuleModel.FindByPlanID(ctx, perfPlan.ProjectId, perfPlan.PlanId)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf plan rule relationship, project_id: %s, plan_id: %s, error: %+v",
			perfPlan.ProjectId, perfPlan.PlanId, err,
		)
	}

	if len(rules) == 0 && len(rs) == 0 {
		return nil
	}

	cache := make(map[string]*model.PerfPlanRuleRelationship, len(rs))
	fromSet := set.NewHashset[string](constants.ConstDefaultMakeSliceSize, generic.Equals[string], generic.HashString)
	toSet := set.NewHashset[string](
		constants.ConstDefaultMakeSliceSize, generic.Equals[string], generic.HashString, rules...,
	)
	for _, r := range rs {
		if r == nil {
			continue
		}

		cache[r.RuleId] = r
		fromSet.Put(r.RuleId)
	}

	// remove the perf plan and perf stop rule relationship
	for _, ruleID := range fromSet.Keys() {
		if toSet.Has(ruleID) {
			continue
		}

		item, ok := cache[ruleID]
		if !ok {
			continue
		}

		if err = l.svcCtx.PerfPlanRuleModel.Delete(ctx, session, item.Id); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to delete item from table, table: %s, item: %s, error: %+v",
				l.svcCtx.PerfPlanRuleModel.Table(), jsonx.MarshalIgnoreError(item), err,
			)
		}
	}

	// create the perf plan and perf stop rule relationship
	for _, ruleID := range toSet.Keys() {
		if fromSet.Has(ruleID) {
			continue
		}

		item := &model.PerfPlanRuleRelationship{
			ProjectId: perfPlan.ProjectId,
			PlanId:    perfPlan.PlanId,
			RuleId:    ruleID,
			CreatedBy: perfPlan.CreatedBy,
			UpdatedBy: perfPlan.UpdatedBy,
			CreatedAt: perfPlan.CreatedAt,
			UpdatedAt: perfPlan.UpdatedAt,
		}
		if _, err := l.svcCtx.PerfPlanRuleModel.Insert(ctx, session, item); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to insert values to table, table: %s, values: %s, error: %+v",
				l.svcCtx.PerfPlanRuleModel.Table(), jsonx.MarshalIgnoreError(item), err,
			)
		}
	}

	return nil
}

func (l *BaseLogic) updatePerfPlanCaseRelationship(
	ctx context.Context, session sqlx.Session, perfPlan *model.PerfPlanV2, cases []*perfPlanCaseItem,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	// remove the perf plan and perf case relationship
	if _, err := l.svcCtx.PerfPlanCaseModel.RemoveByPlanID(
		ctx, session, perfPlan.ProjectId, perfPlan.PlanId,
	); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to remove perf plan case relationship, project_id: %s, plan_id: %s, error: %+v",
			perfPlan.ProjectId, perfPlan.PlanId, err,
		)
	}

	// create the perf plan and perf case relationship
	items := make([]*model.PerfPlanCaseRelationship, 0, len(cases))
	for _, case_ := range cases {
		items = append(
			items, &model.PerfPlanCaseRelationship{
				ProjectId: perfPlan.ProjectId,
				PlanId:    perfPlan.PlanId,
				CaseId:    case_.perfPlanCase.GetCaseId(),
				RateLimits: sql.NullString{
					String: protobuf.MarshalJSONWithMessagesToStringIgnoreError(case_.perfPlanCase.GetRateLimits()),
					Valid:  true,
				},
				TargetRps:         utils.GetMaxRPSFromRateLimits(case_.perfPlanCase.GetRateLimits()),
				PerfDataId:        case_.perfPlanCase.GetPerfDataId(),
				CustomVu:          cast.ToInt64(case_.perfPlanCase.GetCustomVu()),
				NumberOfVu:        int64(case_.perfPlanCase.GetNumberOfVu()),
				CustomLg:          cast.ToInt64(case_.perfPlanCase.GetCustomLg()),
				NumberOfLg:        int64(case_.perfPlanCase.GetNumberOfLg()),
				RequestsOfCpu:     case_.perfPlanCase.GetRequestsOfCpu(),
				RequestsOfMemory:  case_.perfPlanCase.GetRequestsOfMemory(),
				LimitsOfCpu:       case_.perfPlanCase.GetLimitsOfCpu(),
				LimitsOfMemory:    case_.perfPlanCase.GetLimitsOfMemory(),
				EstimatedDuration: int64(case_.durations.TotalDuration.Seconds()),
				State:             int64(pb.CommonState_CS_ENABLE),
				CreatedBy:         perfPlan.CreatedBy,
				UpdatedBy:         perfPlan.UpdatedBy,
				CreatedAt:         perfPlan.CreatedAt,
				UpdatedAt:         perfPlan.UpdatedAt,
			},
		)
	}

	if _, err := l.svcCtx.PerfPlanCaseModel.BatchInsert(ctx, session, items); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to batch insert values to table, table: %s, values: %s, error: %+v",
			l.svcCtx.PerfPlanCaseModel.Table(), jsonx.MarshalIgnoreError(items), err,
		)
	}

	return nil
}

func (l *BaseLogic) updateNotifyItems(
	ctx context.Context, session sqlx.Session, perfPlan *model.PerfPlanV2, chats []*commonpb.LarkChat,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	notifyItems := make([]*pb.CreateNotifyItem, 0, len(chats))
	for _, chat := range chats {
		notifyItems = append(
			notifyItems, &pb.CreateNotifyItem{
				ReceiverName: chat.GetName(),
				Receiver:     chat.GetChatId(),
			},
		)
	}

	return l.createNotifyLogic.CreatePlanNotifyForInternal(
		ctx, session, types.CreateOrUpdateNotifyReference{
			ProjectID:   perfPlan.ProjectId,
			PlanID:      perfPlan.PlanId,
			NotifyMode:  pb.NotifyMode_ALWAYS_NOTIFY,
			NotifyType:  pb.NotifyType_LARK_CHAT,
			NotifyItems: notifyItems,
		},
	)
}

func (l *BaseLogic) createScheduleTask(req *pb.CreatePerfPlanV2Req, perfPlan *model.PerfPlanV2) error {
	if req.GetType() == commonpb.TriggerMode_SCHEDULE {
		if _, err := l.svcCtx.BeatRPC.CreateOrModifyPeriodicTask(
			l.ctx, &beatpb.CreateOrModifyPeriodicTaskReq{
				Name:  l.generatePeriodicTaskName(perfPlan.ProjectId, perfPlan.PlanId),
				Type:  constants.MQTaskTypeDispatcherPeriodicPlanTask,
				Queue: constants.MQNamePeriodicPlanTask,
				Spec:  perfPlan.CronExpression.String,
				Payload: protobuf.MarshalJSONToStringIgnoreError(
					&commonpb.PeriodicPlanTaskInfo{
						ProjectId:      perfPlan.ProjectId,
						PlanId:         perfPlan.PlanId,
						CronExpression: perfPlan.CronExpression.String,
						PlanType:       commonpb.PlanType_PERF,
					},
				),
				Version: beatcommon.V2,
			},
		); err != nil {
			l.Errorf(
				"failed to create schedule task of perf plan, project_id: %s, plan_id: %s, name: %s, error: %+v",
				perfPlan.ProjectId, perfPlan.PlanId, perfPlan.Name, err,
			)
			return err
		}
	}

	return nil
}

func (l *BaseLogic) updateScheduleTask(req *pb.ModifyPerfPlanV2Req, origin *model.PerfPlanV2) error {
	var (
		beatOperator beat.ScheduleTaskOperator

		projectID = req.GetProjectId()
		planID    = req.GetPlanId()
		planName  = req.GetName()
		toState   = req.GetState()
		toType    = req.GetType()
		toCron    = req.GetCronExpression()

		fromState = origin.State
		fromType  = origin.Type
		fromCron  = origin.CronExpression.String
	)

	// enable to disable
	if fromState == int64(qetconstants.EnableStatus) && toState == pb.CommonState_CS_DISABLE {
		// stop schedule task
		if fromType == commonpb.TriggerMode_SCHEDULE.String() {
			beatOperator = beat.ConstBeatScheduleTaskOperatorRemove
		}
	}

	// disable to enable
	if fromState == int64(qetconstants.DisableStatus) && toState == pb.CommonState_CS_ENABLE {
		// start schedule task
		if toType == commonpb.TriggerMode_SCHEDULE {
			beatOperator = beat.ConstBeatScheduleTaskOperatorCreate
		}
	}

	// enable to enable
	if fromState == int64(qetconstants.EnableStatus) && toState == pb.CommonState_CS_ENABLE {
		// schedule to other type
		if fromType == commonpb.TriggerMode_SCHEDULE.String() && toType != commonpb.TriggerMode_SCHEDULE {
			beatOperator = beat.ConstBeatScheduleTaskOperatorRemove
		}

		// other type to schedule
		if fromType != commonpb.TriggerMode_SCHEDULE.String() && toType == commonpb.TriggerMode_SCHEDULE {
			beatOperator = beat.ConstBeatScheduleTaskOperatorCreate
		}

		// schedule to schedule, and cron expression has been changed
		if fromType == commonpb.TriggerMode_SCHEDULE.String() && toType == commonpb.TriggerMode_SCHEDULE && fromCron != toCron {
			beatOperator = beat.ConstBeatScheduleTaskOperatorCreate
		}
	}

	switch beatOperator {
	case beat.ConstBeatScheduleTaskOperatorCreate:
		if _, err := l.svcCtx.BeatRPC.CreateOrModifyPeriodicTask(
			l.ctx, &beatpb.CreateOrModifyPeriodicTaskReq{
				Name:  l.generatePeriodicTaskName(projectID, planID),
				Type:  constants.MQTaskTypeDispatcherPeriodicPlanTask,
				Queue: constants.MQNamePeriodicPlanTask,
				Spec:  toCron,
				Payload: protobuf.MarshalJSONToStringIgnoreError(
					&commonpb.PeriodicPlanTaskInfo{
						ProjectId:      projectID,
						PlanId:         planID,
						CronExpression: toCron,
						PlanType:       commonpb.PlanType_PERF,
					},
				),
				Version: beatcommon.V2,
			},
		); err != nil {
			l.Errorf(
				"failed to %s schedule task of perf plan, project_id: %s, plan_id: %s, name: %s, error: %+v",
				strings.ToLower(beatOperator), projectID, planID, planName, err,
			)
			return err
		}
	case beat.ConstBeatScheduleTaskOperatorRemove:
		if _, err := l.svcCtx.BeatRPC.RemovePeriodicTask(
			l.ctx, &beatpb.RemovePeriodicTaskReq{
				Name: l.generatePeriodicTaskName(projectID, planID),
			},
		); err != nil {
			l.Errorf(
				"failed to %s schedule task of perf plan, project_id: %s, plan_id: %s, name: %s, error: %+v",
				strings.ToLower(beatOperator), projectID, planID, planName, err,
			)
			return err
		}
	}

	return nil
}

func (l *BaseLogic) generatePeriodicTaskName(projectID, planID string) string {
	return fmt.Sprintf("%s:projectId:planId:%s:%s", constants.MQTaskTypeDispatcherPeriodicPlanTask, projectID, planID)
}

func (l *BaseLogic) setReferenceQPS(
	ctx context.Context, projectId, protocol string, items []*pb.SearchCaseInPerfPlanV2Item,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	if len(items) == 0 {
		return nil
	}

	switch protocol {
	case protobuf.GetEnumStringOf(commonpb.Protocol_PROTOCOL_TT),
		protobuf.GetEnumStringOf(commonpb.Protocol_PROTOCOL_TT_AUTH):
		return mr.MapReduceVoid[*commonpb.PerfCaseStepV2, any](
			func(source chan<- *commonpb.PerfCaseStepV2) {
				for _, item := range items {
					for _, steps := range [][]*commonpb.PerfCaseStepV2{
						item.GetSerialSteps(),
						item.GetParallelSteps(),
					} {
						for _, step := range steps {
							source <- step
						}
					}
				}
			}, func(step *commonpb.PerfCaseStepV2, writer mr.Writer[any], cancel func(error)) {
				if step == nil {
					return
				}

				m, err := model.CheckInterfaceMetricsReferenceByMethod(
					ctx, l.svcCtx.InterfaceMetricsReferenceModel,
					projectId, string(common.ConstInterfaceMetricsReferenceProtocolTT), step.GetMethod(),
				)
				if err != nil {
					l.Logger.Error(err)
					return
				}

				if m.ReferenceQps.Valid {
					step.ReferenceQps = &[]int64{m.ReferenceQps.Int64}[0]
				}
			}, func(pipe <-chan any, cancel func(error)) {
			},
		)
	}

	return nil
}
