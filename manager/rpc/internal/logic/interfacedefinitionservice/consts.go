package interfacedefinitionservicelogic

const (
	constSlash  = "/"
	constDotGit = ".git"

	defaultLocalImportDir       = "local_import"
	defaultAllTeam              = "全部团队"
	defaultCoverageDataKeepDays = 90

	ConstLimiterRate = 100

	ConstMediaTypeApplicationJsonName              = "application/json"
	ConstMediaTypeApplicationJsonHeaderContentType = "Content-Type"

	ConstMapKeyProjectIdDocumentId                  = "key:projectId:documentId"
	ConstMapKeyProjectIdDocumentName                = "key:projectId:name"
	ConstMapKeyProjectIdDocumentServiceDocumentName = "key:projectId:service:name"
	ConstMapKeyProjectIdSchemaId                    = "key:projectId:schemaId"
	ConstMapKeyProjectIdSchemaFullName              = "key:projectId:fullName"
	ConstMapKeyProjectIdReferenceTypeReferenceId    = "key:projectId:referenceType:referenceId"
)

const (
	ConstTDSCommonCallPath = "/common/call"

	ConstTDSCommonCallFieldClient    = "client"
	ConstTDSCommonCallFieldApiName   = "api_name"
	ConstTDSCommonCallFieldFixDict   = "fix_dict"
	ConstTDSCommonCallFieldProtoJson = "proto_json"
	ConstTDSCommonCallFieldProtoFile = "proto_file"
	ConstTDSCommonCallFieldProtoMsg  = "proto_msg"
	ConstTDSCommonCallFieldReqMsg    = "req_msg"
	ConstTDSCommonCallFieldStubName  = "stub_name"
	ConstTDSCommonCallFieldMetadata  = "metadata"
	ConstTDSCommonCallFieldHeaders   = "headers"
	ConstTDSCommonCallFieldMethod    = "method"

	ConstTDSCommonCallFieldCid          = "cid"
	ConstTDSCommonCallFieldAccount      = "account"
	ConstTDSCommonCallFieldUid          = "uid"
	ConstTDSCommonCallFieldUri          = "uri"
	ConstTDSCommonCallFieldDrvType      = "drv_type"
	ConstTDSCommonCallFieldProdType     = "prod_type"
	ConstTDSCommonCallFieldPlatformType = "platform_type"

	ConstTDSCommonCallFieldCmd      = "cmd"
	ConstTDSCommonCallFieldProtocol = "protocol"
	ConstTDSCommonCallFieldPacket   = "packet"

	ConstTDSCommonCallFieldCode        = "code"
	ConstTDSCommonCallFieldMessage     = "message"
	ConstTDSCommonCallFieldData        = "data"
	ConstTDSCommonCallFieldElapsedTime = "elapsed_time"
	ConstTDSCommonCallFieldRet         = "ret"
	ConstTDSCommonCallFieldTraceId     = "trace_id"
	ConstTDSCommonCallFieldApmId       = "apm_id"
)

const (
	ConstTTMetaClientCommonCallPath = "/common/call"

	ConstTTMetaCommonCallFieldClient     = "client_info"
	ConstTTMetaCommonCallFieldApiName    = "method"
	ConstTTMetaCommonCallFieldServerName = "server_name"
	ConstTTMetaCommonCallFieldProtoJson  = "proto_json"
	ConstTTMetaCommonCallFieldUid        = "uid"
	ConstTTMetaCommonCallFieldCode       = "code"
	ConstTTMetaCommonCallFieldMessage    = "msg"
	ConstTTMetaCommonCallFieldData       = "data"
)

const (
	ConstApiProxyCommonApiCallPath = "/router/v1/common/api/call"

	ConstApiProxyCommonApiCallFieldMethod          = "method"
	ConstApiProxyCommonApiCallFieldCid             = "cid"
	ConstApiProxyCommonApiCallFieldHeaders         = "headers"
	ConstApiProxyCommonApiCallFieldBody            = "body"
	ConstApiProxyCommonApiCallFieldUrl             = "url"
	ConstApiProxyCommonApiCallFieldCode            = "code"
	ConstApiProxyCommonApiCallFieldMessage         = "message"
	ConstApiProxyCommonApiCallFieldData            = "data"
	ConstApiProxyCommonApiCallFieldCustomFields    = "custom_fields"
	ConstApiProxyCommonApiCallFieldCallResp        = "call_resp"
	ConstApiProxyCommonApiCallFieldCallRespHeaders = "headers"
	ConstApiProxyCommonApiCallFieldCallRespBody    = "body"
	ConstApiProxyCommonApiCallFieldCallRespStatus  = "status"
)
