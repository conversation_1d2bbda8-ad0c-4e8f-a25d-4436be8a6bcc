package apiexecutionservicelogic

import (
	"context"
	"fmt"
	"net/url"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	zeroutils "github.com/zeromicro/go-zero/core/utils"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/calculate"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/cmdb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	devicehubcommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	larkproxypb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	relationpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

var perfCaseStepTypeOrders = map[string]int{
	string(common.ConstPerfCaseStepTypeSetup):    1,
	string(common.ConstPerfCaseStepTypeSerial):   2,
	string(common.ConstPerfCaseStepTypeParallel): 3,
	string(common.ConstPerfCaseStepTypeTeardown): 4,
}

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []qetutils.TypeConverter{
			commonpb.StringToTriggerMode(),
			commonpb.StringToPurposeType(),
			commonpb.StringToDeviceType(),
			commonpb.StringToPlatformType(),
			commonpb.StringToFailRetry(),
			commonpb.StringToTestLanguage(),
			commonpb.StringToTestFramework(),
			commonpb.StringToProtocol(),
			commonpb.StringToTargetEnvironment(),
			commonpb.StringToMetricType(),

			logic.StringToResourceState(),
			logic.StringToImports(),
			logic.StringToExports(),
			logic.SqlNullStringToTestArgs(),
			logic.SqlNullStringToDeviceIDs(),
			logic.StringToVariables(),
			logic.StringToPerfKeepalive(),
			logic.StringToRateLimits(),
			logic.StringToPerfCaseSteps(),
			logic.SqlNullStringToPerfCaseSteps(),
			logic.SqlNullStringToStabilityCustomDevices(),      // devices
			logic.SqlNullStringToStabilityCustomScript(),       // custom_script
			logic.StringToUIAgentComponentSteps(),              // steps
			logic.SqlNullStringToUIAgentComponentExpectation(), // expectation
		},
	}
}

// getComponentGroupExecutionData 获取组件组的执行数据
func (l *BaseLogic) getComponentGroupExecutionData(projectId, componentGroupId, version string) (
	*pb.ApiExecutionData, error,
) {
	componentGroup, err := model.CheckComponentGroupByComponentGroupId(
		l.ctx, l.svcCtx.ComponentGroupModel, projectId, componentGroupId, version,
	)
	if err != nil {
		return nil, err
	}

	var relations []*types.Relation
	if err = jsonx.UnmarshalFromString(componentGroup.Structure, &relations); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal component group structure[%s], error: %+v",
			componentGroup.Structure, err,
		)
	}

	groupData := &pb.ApiExecutionData_Group{Group: &pb.ComponentGroupComponent{}}
	if err = qetutils.Copy(groupData.Group, componentGroup, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy component group[%+v] to api execution data, error: %+v",
			componentGroup, err,
		)
	}

	apiExecutionData := &pb.ApiExecutionData{
		Id:       componentGroup.ComponentGroupId,
		Type:     pb.ApiExecutionDataType_API_COMPONENT_GROUP,
		Data:     groupData,
		Children: make([]*pb.ApiExecutionData_ChildData, 1),
	}
	if err = l.generateApiExecutionData(
		componentData{
			ProjectId:        projectId,
			ParentId:         componentGroup.ComponentGroupId,
			ParentType:       constants.ComponentGroup,
			ParentVersion:    componentGroup.Version,
			ApiExecutionData: apiExecutionData,
		}, [][]*types.Relation{relations},
	); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(codes.GenerateApiExecutionDataFailure, err.Error()),
			"failed to generate api execution data with project_id[%s] and component_group_id[%s], error: %+v",
			projectId, componentGroupId, err,
		)
	}

	return apiExecutionData, nil
}

// getApiCaseExecutionData 获取API用例的执行数据
func (l *BaseLogic) getApiCaseExecutionData(projectId, caseId, version string) (*pb.ApiExecutionData, error) {
	// validate the case_id
	apiCase, err := model.CheckApiCaseByCaseId(l.ctx, l.svcCtx.ApiCaseModel, projectId, caseId, version)
	if err != nil {
		return nil, err
	}

	// validate the state of api case
	// if apiCase.State != int64(pb.CommonState_CS_ENABLE) {
	//	return nil, errors.Wrapf(errorx.ErrGrpcPermissionDenied, "cannot to execute the api case[%s] whose the state is disable", apiCase.Name)
	// }

	var relations []*types.Relation
	if err = jsonx.UnmarshalFromString(apiCase.Structure, &relations); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal api case structure[%s], error: %+v",
			apiCase.Structure, err,
		)
	}

	caseData := &pb.ApiExecutionData_Case{Case: &pb.CaseComponent{}}
	if err = qetutils.Copy(caseData.Case, apiCase, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy api case[%+v] to api execution data, error: %+v",
			apiCase, err,
		)
	}

	apiExecutionData := &pb.ApiExecutionData{
		Id:       apiCase.CaseId,
		Type:     pb.ApiExecutionDataType_API_CASE,
		Data:     caseData,
		Children: make([]*pb.ApiExecutionData_ChildData, 1),
	}
	if err = l.generateApiExecutionData(
		componentData{
			ProjectId:        projectId,
			ParentId:         apiCase.CaseId,
			ParentType:       constants.ApiCase,
			ParentVersion:    apiCase.Version,
			ApiExecutionData: apiExecutionData,
		}, [][]*types.Relation{relations},
	); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(codes.GenerateApiExecutionDataFailure, err.Error()),
			"failed to generate api execution data with project_id[%s] and case_id[%s], error: %+v",
			projectId, caseId, err,
		)
	}

	return apiExecutionData, nil
}

// getInterfaceCaseExecutionData 获取接口用例执行数据
func (l *BaseLogic) getInterfaceCaseExecutionData(projectId, caseId, version string) (*pb.ApiExecutionData, error) {
	// validate the case_id
	interfaceCase, err := model.CheckInterfaceCaseByCaseId(
		l.ctx, l.svcCtx.InterfaceCaseModel, projectId, "", caseId, version,
	)
	if err != nil {
		return nil, err
	}

	// validate the state of interface case
	// if interfaceCase.State != int64(pb.CommonState_CS_ENABLE) {
	//	return nil, errors.Wrapf(errorx.ErrGrpcPermissionDenied, "cannot to execute the interface case[%s] whose the state is disable", interfaceCase.Name)
	// }

	var relations []*types.Relation
	if err = jsonx.UnmarshalFromString(interfaceCase.Structure, &relations); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal interface case structure[%s], error: %+v",
			interfaceCase.Structure, err,
		)
	}

	interfaceCaseData := &pb.ApiExecutionData_InterfaceCase{InterfaceCase: &pb.InterfaceCaseComponent{}}
	if err = qetutils.Copy(interfaceCaseData.InterfaceCase, interfaceCase, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy interface case[%+v] to api execution data, error: %+v",
			interfaceCase, err,
		)
	}

	apiExecutionData := &pb.ApiExecutionData{
		Id:       interfaceCase.CaseId,
		Type:     pb.ApiExecutionDataType_INTERFACE_CASE,
		Data:     interfaceCaseData,
		Children: make([]*pb.ApiExecutionData_ChildData, 1),
	}

	if err = l.generateApiExecutionData(
		componentData{
			ProjectId:        projectId,
			ParentId:         interfaceCase.CaseId,
			ParentType:       constants.InterfaceCase,
			ParentVersion:    interfaceCase.Version,
			ApiExecutionData: apiExecutionData,
		}, [][]*types.Relation{relations},
	); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(codes.GenerateApiExecutionDataFailure, err.Error()),
			"failed to generate api execution data with project_id[%s] and case_id[%s], error: %+v",
			projectId, caseId, err,
		)
	}

	return apiExecutionData, nil
}

// getApiSuiteExecutionStructure 获取API集合的执行结构
func (l *BaseLogic) getApiSuiteExecutionStructure(projectId, suiteId string) (*pb.ApiExecutionData, error) {
	// validate the suite_id
	apiSuite, err := model.CheckApiSuiteBySuiteId(l.ctx, l.svcCtx.ApiSuiteModel, projectId, suiteId)
	if err != nil {
		return nil, err
	}

	suiteData := &pb.ApiExecutionData_Suite{Suite: &pb.SuiteComponent{}}
	if err = qetutils.Copy(suiteData.Suite, apiSuite, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy api suite[%+v] to api execution data, error: %+v",
			apiSuite, err,
		)
	}

	// apiCases, err := l.svcCtx.ApiCaseModel.FindLatestBySuiteId(l.ctx, projectId, suiteId)
	// if err != nil {
	// 	return nil, errors.Wrapf(
	// 		errorx.Err(errorx.DBError, err.Error()),
	// 		"failed to find api cases in api suite with project_id[%s] and suite_id[%s], error: %+v",
	// 		projectId, suiteId, err,
	// 	)
	// }

	selectBuilder, _ := l.svcCtx.ApiSuiteModel.GenerateSearchCaseInApiSuiteSqlBuilder(
		model.SearchCaseInApiSuiteReq{
			ProjectId: projectId,
			SuiteId:   suiteId,
		},
	)
	cases, err := l.svcCtx.ApiSuiteModel.FindCasesInApiSuite(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find cases in api suite with project_id[%s] and suite_id[%s], error: %+v",
			projectId, suiteId, err,
		)
	}

	apiExecutionStructure := &pb.ApiExecutionData{
		Id:       apiSuite.SuiteId,
		Type:     pb.ApiExecutionDataType_API_SUITE,
		Data:     suiteData,
		Children: []*pb.ApiExecutionData_ChildData{},
	}

	if err = l.generateApiExecutionStructure(
		componentData{
			ProjectId:        projectId,
			ParentId:         suiteId,
			ParentType:       common.ConstReferenceTypeApiSuite,
			ApiExecutionData: apiExecutionStructure,
		}, cases,
	); err != nil {
		return nil, err
	}

	return apiExecutionStructure, nil
}

// getInterfaceDocumentExecutionStructure 获取接口集合（接口文档）的执行结构
func (l *BaseLogic) getInterfaceDocumentExecutionStructure(projectId, documentId string) (*pb.ApiExecutionData, error) {
	interfaceDocument, err := model.CheckInterfaceDocumentByDocumentId(
		l.ctx, l.svcCtx.InterfaceDocumentModel, projectId, documentId,
	)
	if err != nil {
		return nil, err
	}

	suiteData := &pb.ApiExecutionData_InterfaceDocument{InterfaceDocument: &pb.InterfaceDocumentComponent{}}
	if err = qetutils.Copy(suiteData.InterfaceDocument, interfaceDocument, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy interface document[%+v] to api execution data, error: %+v",
			interfaceDocument, err,
		)
	}

	interfaceCases, err := l.svcCtx.InterfaceCaseModel.FindLatestByDocumentId(l.ctx, projectId, documentId)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find interface cases with project_id[%s] and document_id[%s], error: %+v",
			projectId, documentId, err,
		)
	}

	apiExecutionStructure := &pb.ApiExecutionData{
		Id:       interfaceDocument.DocumentId,
		Type:     pb.ApiExecutionDataType_INTERFACE_DOCUMENT,
		Data:     suiteData,
		Children: []*pb.ApiExecutionData_ChildData{},
	}

	if err = l.generateApiExecutionStructure(
		componentData{
			ProjectId:        projectId,
			ParentId:         documentId,
			ParentType:       common.ConstReferenceTypeInterfaceDocument,
			ApiExecutionData: apiExecutionStructure,
		}, interfaceCases,
	); err != nil {
		return nil, err
	}

	return apiExecutionStructure, nil
}

func (l *BaseLogic) getSuiteCaseChildren(
	planData *pb.ApiExecutionData_Plan, caseIds []string,
) ([]*pb.ApiExecutionData_ChildData, error) {
	var caseChildren []*pb.ApiExecutionData_ChildData

	if len(caseIds) == 0 {
		return caseChildren, nil
	}

	sb := l.svcCtx.ApiCaseModel.SelectBuilder().
		Where("`project_id` = ? AND `latest` = ? ", planData.Plan.ProjectId, qetconstants.IsLatestVersion).
		Where(squirrel.Eq{"case_id": caseIds})

	cases, err := l.svcCtx.ApiCaseModel.FindNoCacheByQuery(l.ctx, sb)
	if err != nil {
		return nil, err
	}

	var caseChildren_ []*pb.ApiExecutionData_ChildData
	for _, apiCase := range cases {
		caseItem := &pb.ApiExecutionData{
			Id:   apiCase.CaseId,
			Type: pb.ApiExecutionDataType_API_CASE,
			Data: &pb.ApiExecutionData_Case{
				Case: &pb.CaseComponent{
					ProjectId:      apiCase.ProjectId,
					CaseId:         apiCase.CaseId,
					Name:           apiCase.Name,
					Description:    apiCase.Description.String,
					State:          logic.ConvertStringToResourceState(apiCase.State),
					ReferenceState: pb.CommonState_CS_ENABLE,
					Version:        apiCase.Version,
					MaintainedBy:   apiCase.MaintainedBy.String,
				},
			},
		}
		caseChild := &pb.ApiExecutionData_ChildData{
			Child: []*pb.ApiExecutionData{caseItem},
		}
		caseChildren_ = append(caseChildren_, caseChild)
	}
	caseChildren = append(caseChildren, caseChildren_...)

	return caseChildren, nil
}

func (l *BaseLogic) getInterfaceCaseChildren(
	planData *pb.ApiExecutionData_Plan, documentId string, caseIds []string,
) ([]*pb.ApiExecutionData_ChildData, error) {
	var caseChildren []*pb.ApiExecutionData_ChildData

	sb := l.svcCtx.InterfaceCaseModel.SelectBuilder().
		Where(
			"`project_id` = ? AND `document_id` = ? AND `latest` = ? ",
			planData.Plan.ProjectId, documentId, qetconstants.IsLatestVersion,
		).
		Where(squirrel.Eq{"case_id": caseIds})

	interfaceCases, err := l.svcCtx.InterfaceCaseModel.FindNoCacheByQuery(l.ctx, sb)
	if err != nil {
		return nil, err
	}

	var caseChildren_ []*pb.ApiExecutionData_ChildData
	for _, interfaceCase := range interfaceCases {
		caseItem := &pb.ApiExecutionData{
			Id:   interfaceCase.CaseId,
			Type: pb.ApiExecutionDataType_INTERFACE_CASE,
			Data: &pb.ApiExecutionData_InterfaceCase{
				InterfaceCase: &pb.InterfaceCaseComponent{
					ProjectId:      interfaceCase.ProjectId,
					DocumentId:     documentId,
					CaseId:         interfaceCase.CaseId,
					Name:           interfaceCase.Name,
					Description:    interfaceCase.Description.String,
					State:          logic.ConvertStringToResourceState(interfaceCase.State),
					ReferenceState: pb.CommonState_CS_ENABLE,
					Version:        interfaceCase.Version,
					MaintainedBy:   interfaceCase.MaintainedBy.String,
				},
			},
		}
		caseChild := &pb.ApiExecutionData_ChildData{
			Child: []*pb.ApiExecutionData{caseItem},
		}
		caseChildren_ = append(caseChildren_, caseChild)
	}
	caseChildren = append(caseChildren, caseChildren_...)

	return caseChildren, nil
}

func (l *BaseLogic) getInterfaceExecutionData(
	planData *pb.ApiExecutionData_Plan, documentId string, caseIds []string,
) (*pb.ApiExecutionData, error) {
	documentCaseChildren := make([]*pb.ApiExecutionData_ChildData, 0)

	i := 0
	step := 20
	for {
		if i >= len(caseIds) {
			break
		}
		n := i + step
		if n > len(caseIds) {
			n = len(caseIds)
		}

		partCaseIds := caseIds[i:n]
		documentCaseChildren_, err := l.getInterfaceCaseChildren(planData, documentId, partCaseIds)
		if err != nil {
			return nil, err
		}
		documentCaseChildren = append(documentCaseChildren, documentCaseChildren_...)
		i += step
	}

	interfaceDocument := &pb.ApiExecutionData{
		Id:   documentId,
		Type: pb.ApiExecutionDataType_INTERFACE_DOCUMENT,
		Data: &pb.ApiExecutionData_InterfaceDocument{
			InterfaceDocument: &pb.InterfaceDocumentComponent{
				ProjectId:         planData.Plan.ProjectId,
				DocumentId:        documentId,
				State:             pb.CommonState_CS_ENABLE,
				ReferenceState:    pb.CommonState_CS_ENABLE,
				CaseExecutionMode: pb.ExecutionMode_EM_PARALLEL,
			},
		},
		Children: documentCaseChildren,
	}

	return interfaceDocument, nil
}

// Deprecated: use `getPrecisionTestPlanApiExecutionStructureV2` instead.
// getPrecisionTestPlanApiExecutionStructure 获取精准测试计划的API执行结构
func (l *BaseLogic) getPrecisionTestPlanApiExecutionStructure(
	planData *pb.ApiExecutionData_Plan, services []string,
) (*pb.ApiExecutionData, error) {
	if len(services) == 0 {
		return nil, errors.New("本次为执行精准测试，传递的服务列表不能为空")
	}

	if planData.Plan.Type != commonpb.TriggerMode_INTERFACE {
		return nil, errors.New("本次为执行精准测试，必须使用接口触发")
	}

	projectId := planData.Plan.GetProjectId()
	planId := planData.Plan.GetPlanId()
	generalConfigId := planData.Plan.GetGeneralConfig().GetConfigId()

	// 获取升级的每个服务关联的测试用例
	resp, err := l.svcCtx.RelationRPC.GetServiceBindTestCase(
		l.ctx, &relationpb.GetServiceBindTestCaseRequest{
			ProjectId:       projectId,
			GeneralConfigId: generalConfigId,
			ServiceNames:    services,
		},
	)
	if err != nil {
		return nil, err
	}

	serviceMap := make(map[string]map[string]bool)
	for _, r := range resp.GetRelations() {
		cases, ok := serviceMap[r.GetServiceName()]
		if !ok {
			cases = make(map[string]bool)
		}
		sp := strings.Split(r.GetCaseId(), "::")
		if len(sp) > 0 {
			// example:
			// case_id:WDzOeqoz3Q4WwD8iR5wAr::
			// interface_case_id:3mhNfwWEW1HmwApz6biGH::interface_document_id:4-ky4ijYLRT8LIlAq63qe
			caseId := sp[0]
			cases[caseId] = true
		}
		serviceMap[r.GetServiceName()] = cases
	}

	// 获取该计划所有可执行的用例
	caseMap, err := l.getNormalPlanAllApiCase(planData)
	if err != nil {
		return nil, err
	}

	serviceCases := make([]*pb.ApiExecutionData_ServiceCasesData, 0, len(serviceMap))
	serviceList := make([]*pb.ApiExecutionData, 0, len(serviceMap))
	for serviceName, _cases := range serviceMap {
		serviceCase := &pb.ApiExecutionData_ServiceCasesData{
			Service: serviceName,
			Cases:   make([]string, 0, len(_cases)),
		}
		child := make([]*model.SearchCaseInApiPlanItem, 0, len(_cases))
		for caseName := range _cases {
			// 求交集
			if c, ok := caseMap[caseName]; ok {
				target := &model.SearchCaseInApiPlanItem{}
				err1 := qetutils.Copy(target, c, l.converters...)
				if err1 != nil {
					continue
				}

				serviceCase.Cases = append(serviceCase.Cases, caseName)
				child = append(child, target)
			}
		}

		id := utils.GenServiceId()
		service := &pb.ApiExecutionData{
			Id:   id,
			Type: pb.ApiExecutionDataType_API_SERVICE,
			Data: &pb.ApiExecutionData_Service{
				Service: &pb.ServiceComponent{
					ProjectId:   projectId,
					ServiceName: serviceName,
					ServiceId:   id,
				},
			},
		}

		if err = l.generateApiExecutionStructure(
			componentData{
				ProjectId:        projectId,
				ParentId:         id,
				ParentType:       common.ConstReferenceTypeService,
				ApiExecutionData: service,
			}, child,
		); err != nil {
			return nil, err
		}

		serviceCases = append(serviceCases, serviceCase)
		serviceList = append(serviceList, service)
	}

	// 测试计划
	apiExecutionStructure := &pb.ApiExecutionData{
		Id:   planId,
		Type: pb.ApiExecutionDataType_API_PLAN,
		Data: planData,
		Children: []*pb.ApiExecutionData_ChildData{
			{
				Child: serviceList,
			},
		},
		ServiceCasesContent: serviceCases,
	}

	return apiExecutionStructure, nil
}

// getNormalPlanApiExecutionData 获取普通测试计划的所有用例
func (l *BaseLogic) getNormalPlanAllApiCase(planData *pb.ApiExecutionData_Plan) (
	caseMap map[string]*model.SearchCaseInApiPlanItem, err error,
) {
	var (
		projectID = planData.Plan.GetProjectId()
		planID    = planData.Plan.GetPlanId()
	)

	planEs, err := l.getNormalPlanApiExecutionStructure(planData)
	if err != nil {
		return nil, err
	}

	if len(planEs.GetChildren()) == 0 {
		return nil, errorx.Errorf(
			errorx.NotExists,
			"not found any children nodes of the plan, project_id: %s, plan_id: %s",
			projectID, planID,
		)
	}

	cases := make([]*model.SearchCaseInApiPlanItem, 0, 100)
	lock := sync.Mutex{}
	err = caller.MultiDo(
		l.ctx, planEs.GetChildren()[0].GetChild(), func(item any) error {
			source, ok := item.(*pb.ApiExecutionData)
			if !ok {
				return errorx.Errorf(
					errorx.TypeError,
					"invalid child node, expected %T, but got %T",
					(*pb.ApiExecutionData)(nil), item,
				)
			}

			var dst []*model.SearchCaseInApiPlanItem
			var err1 error
			switch source.GetType() {
			case pb.ApiExecutionDataType_API_SUITE:
				selectBuilder, _ := l.svcCtx.ApiPlanModel.GenerateSearchCaseInApiPlanSqlBuilder(
					model.SearchCaseInApiPlanReq{
						ApiCaseModel:       l.svcCtx.ApiCaseModel,
						InterfaceCaseModel: l.svcCtx.InterfaceCaseModel,
						ProjectId:          planData.Plan.GetProjectId(),
						PlanId:             planData.Plan.GetPlanId(),
						SuiteType:          common.ConstReferenceTypeApiSuite,
						SuiteId:            source.GetSuite().GetSuiteId(),
					},
				)

				dst, err1 = l.svcCtx.ApiPlanModel.FindCasesInApiPlan(l.ctx, selectBuilder)
			case pb.ApiExecutionDataType_INTERFACE_DOCUMENT:
				selectBuilder, _ := l.svcCtx.ApiPlanModel.GenerateSearchCaseInApiPlanSqlBuilder(
					model.SearchCaseInApiPlanReq{
						ApiCaseModel:       l.svcCtx.ApiCaseModel,
						InterfaceCaseModel: l.svcCtx.InterfaceCaseModel,
						ProjectId:          planData.Plan.GetProjectId(),
						PlanId:             planData.Plan.GetPlanId(),
						SuiteType:          common.ConstReferenceTypeInterfaceDocument,
						SuiteId:            source.GetInterfaceDocument().GetDocumentId(),
					},
				)

				dst, err1 = l.svcCtx.ApiPlanModel.FindCasesInApiPlan(l.ctx, selectBuilder)
			}
			if err1 != nil {
				return err1
			}

			// [2024-04-16] delete this rule:
			// 排除不可执行的用例
			// ddst := make([]*model.SearchCaseInApiPlanItem, 0, len(dst))
			// for i, d := range dst {
			//	if d.Validate() {
			//		ddst = append(ddst, dst[i])
			//	}
			// }

			if len(dst) > 0 {
				lock.Lock()
				cases = append(cases, dst...)
				lock.Unlock()
			}
			return nil
		}, 10,
	)
	if err != nil {
		return nil, err
	}

	// 排除重复用例
	caseMap = make(map[string]*model.SearchCaseInApiPlanItem)
	for i, c := range cases {
		caseMap[c.CaseId] = cases[i]
	}

	return caseMap, nil
}

// getPrecisionTestPlanApiExecutionStructureV2 获取精准测试计划的API执行结构
func (l *BaseLogic) getPrecisionTestPlanApiExecutionStructureV2(
	planData *pb.ApiExecutionData_Plan, services []string,
) (*pb.ApiExecutionData, error) {
	var (
		projectID       = planData.Plan.GetProjectId()
		planID          = planData.Plan.GetPlanId()
		generalConfigID = planData.Plan.GetGeneralConfig().GetConfigId()
	)

	allCases, err := l.getNormalPlanAllCases(planData)
	if err != nil {
		return nil, err
	}

	apiExecutionStructure := &pb.ApiExecutionData{
		Id:   planID,
		Type: pb.ApiExecutionDataType_API_PLAN,
		Data: planData,
		Children: []*pb.ApiExecutionData_ChildData{
			{
				Child: make([]*pb.ApiExecutionData, 0, len(services)),
			},
		},
		ServiceCasesContent: make([]*pb.ApiExecutionData_ServiceCasesData, 0, len(services)),
	}

	cache := make(map[string]lang.PlaceholderType, len(services))
	for _, service := range services {
		if _, ok := cache[service]; ok {
			continue
		}
		cache[service] = lang.Placeholder

		data1, data2, err := l.getServiceExecutionStructure(projectID, generalConfigID, service, allCases)
		if err != nil {
			return nil, err
		}

		apiExecutionStructure.Children[0].Child = append(apiExecutionStructure.Children[0].Child, data1)
		apiExecutionStructure.ServiceCasesContent = append(apiExecutionStructure.ServiceCasesContent, data2)
	}

	return apiExecutionStructure, nil
}

func (l *BaseLogic) getNormalPlanAllCases(planData *pb.ApiExecutionData_Plan) (
	map[string]*model.SearchCaseInApiPlanItem, error,
) {
	var (
		projectID = planData.Plan.GetProjectId()
		planID    = planData.Plan.GetPlanId()
	)

	planES, err := l.getNormalPlanApiExecutionStructure(planData)
	if err != nil {
		return nil, err
	} else if len(planES.GetChildren()) == 0 || len(planES.GetChildren()[0].GetChild()) == 0 {
		return nil, errorx.Errorf(
			errorx.NotExists,
			"not found any children nodes of the plan, project_id: %s, plan_id: %s",
			projectID, planID,
		)
	}

	cases := make(map[string]*model.SearchCaseInApiPlanItem, constants.ConstDefaultMakeMapSize)
	if err = mr.MapReduceVoid[*pb.ApiExecutionData, *model.SearchCaseInApiPlanItem](
		func(source chan<- *pb.ApiExecutionData) {
			for _, data := range planES.GetChildren()[0].GetChild() {
				source <- data
			}
		}, func(item *pb.ApiExecutionData, writer mr.Writer[*model.SearchCaseInApiPlanItem], cancel func(error)) {
			if item == nil {
				return
			}

			var (
				suiteType common.ReferenceType
				suiteID   string
				items     []*model.SearchCaseInApiPlanItem
				err       error
			)
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			switch item.GetType() {
			case pb.ApiExecutionDataType_API_SUITE:
				suiteType = common.ConstReferenceTypeApiSuite
				suiteID = item.GetSuite().GetSuiteId()
			case pb.ApiExecutionDataType_INTERFACE_DOCUMENT:
				suiteType = common.ConstReferenceTypeInterfaceDocument
				suiteID = item.GetInterfaceDocument().GetDocumentId()
			default:
				return
			}

			sb, _ := l.svcCtx.ApiPlanModel.GenerateSearchCaseInApiPlanSqlBuilder2(
				model.SearchCaseInApiPlanReq{
					ApiCaseModel:       l.svcCtx.ApiCaseModel,
					InterfaceCaseModel: l.svcCtx.InterfaceCaseModel,
					ProjectId:          projectID,
					PlanId:             planID,
					SuiteType:          suiteType,
					SuiteId:            suiteID,
				},
			)
			items, err = l.svcCtx.ApiPlanModel.FindCasesInApiPlan(l.ctx, sb)
			if err != nil {
				err = errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to get cases in api plan, project_id: %s, plan_id: %s, suite_type: %s, suite_id: %s, error: %+v",
					projectID, planID, suiteType, suiteID, err,
				)
				return
			}

			for _, v := range items {
				writer.Write(v)
			}
		}, func(pipe <-chan *model.SearchCaseInApiPlanItem, cancel func(error)) {
			for item := range pipe {
				cases[item.CaseId] = item
			}
		}, mr.WithContext(l.ctx),
	); err != nil {
		return nil, err
	}

	return cases, nil
}

// getServiceExecutionStructure 获取精准测试服务集合的执行结构
func (l *BaseLogic) getServiceExecutionStructure(
	projectID, generalConfigID, service string, allCases map[string]*model.SearchCaseInApiPlanItem,
) (*pb.ApiExecutionData, *pb.ApiExecutionData_ServiceCasesData, error) {
	out, err := l.svcCtx.RelationRPC.GetCaseByService(
		l.ctx, &relationpb.GetCaseByServiceReq{
			ProjectId:       projectID,
			GeneralConfigId: generalConfigID,
			Service:         service,
		},
	)
	if err != nil {
		l.Errorf(
			"failed to get case by service, project_id: %s, general_config_id: %s, service: %s, error: %+v",
			projectID, generalConfigID, service, err,
		)
		return nil, nil, err
	}

	relations := out.GetRelations()
	cache := make(map[string]lang.PlaceholderType, len(relations))
	serviceCases := make([]*model.SearchCaseInApiPlanItem, 0, len(relations))
	serviceCasesData := &pb.ApiExecutionData_ServiceCasesData{
		Service: service,
		Cases:   make([]string, 0, len(relations)),
	}
	for _, relation := range relations {
		caseID := relation.GetCaseId()
		if _, ok := cache[caseID]; ok {
			continue
		}
		cache[caseID] = lang.Placeholder

		v, ok := allCases[caseID]
		if !ok {
			continue
		}
		serviceCases = append(serviceCases, v)
		serviceCasesData.Cases = append(serviceCasesData.Cases, caseID)
	}

	serviceID := utils.GenServiceId()
	apiExecutionStructure := &pb.ApiExecutionData{
		Id:   serviceID,
		Type: pb.ApiExecutionDataType_API_SERVICE,
		Data: &pb.ApiExecutionData_Service{
			Service: &pb.ServiceComponent{
				ProjectId:   projectID,
				ServiceName: service,
				ServiceId:   serviceID,
			},
		},
	}

	if err = l.generateApiExecutionStructure(
		componentData{
			ProjectId:        projectID,
			ParentId:         serviceID,
			ParentType:       common.ConstReferenceTypeService,
			ApiExecutionData: apiExecutionStructure,
		}, serviceCases,
	); err != nil {
		return nil, nil, err
	}

	return apiExecutionStructure, serviceCasesData, nil
}

// getNormalPlanApiExecutionStructure 获取普通测试计划的API执行结构
func (l *BaseLogic) getNormalPlanApiExecutionStructure(planData *pb.ApiExecutionData_Plan) (
	*pb.ApiExecutionData, error,
) {
	projectId := planData.Plan.GetProjectId()
	planId := planData.Plan.GetPlanId()

	apiExecutionStructure := &pb.ApiExecutionData{
		Id:                  planId,
		Type:                pb.ApiExecutionDataType_API_PLAN,
		Data:                planData,
		Children:            []*pb.ApiExecutionData_ChildData{},
		ServiceCasesContent: []*pb.ApiExecutionData_ServiceCasesData{},
	}
	selectBuilder, _ := l.svcCtx.ApiPlanModel.GenerateSearchSuiteInApiPlanSqlBuilder(
		model.SearchSuiteInApiPlanReq{
			ApiSuiteModel:          l.svcCtx.ApiSuiteModel,
			InterfaceDocumentModel: l.svcCtx.InterfaceDocumentModel,
			ProjectId:              projectId,
			PlanId:                 planId,
		},
	)
	suites, err := l.svcCtx.ApiPlanModel.FindSuitesInApiPlan(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find suite in api plan with project_id[%s] and plan_id[%s], error: %+v", projectId, planId, err,
		)
	}

	if err = l.generateApiExecutionStructure(
		componentData{
			ProjectId:        projectId,
			ParentId:         planId,
			ParentType:       common.ConstReferenceTypeApiPlan,
			ApiExecutionData: apiExecutionStructure,
		}, suites,
	); err != nil {
		return nil, err
	}
	return apiExecutionStructure, nil
}

// getApiPlanExecutionStructure 获取API计划的执行结构
func (l *BaseLogic) getApiPlanExecutionStructure(projectId, planId string, extra *pb.ApiPlanExtraData) (
	*pb.ApiExecutionData, error,
) {
	// validate the plan_id
	apiPlan, err := model.CheckApiPlanByPlanId(l.ctx, l.svcCtx.ApiPlanModel, projectId, planId)
	if err != nil {
		return nil, err
	}

	planData := &pb.ApiExecutionData_Plan{Plan: &pb.PlanComponent{}}
	if err = qetutils.Copy(planData.Plan, apiPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy api plan to api execution data, api plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(apiPlan), err,
		)
	}

	if apiPlan.GeneralConfigId.Valid {
		// get the general configuration of api plan
		generalConfig, err := l.svcCtx.ApiPlanModel.FindGeneralConfigByPlanId(
			l.ctx, model.FindGeneralConfigByPlanIdReq{
				ProjectId: projectId,
				PlanId:    planId,
			},
		)
		if err != nil && !errors.Is(err, model.ErrNotFound) {
			return nil, err
		} else if generalConfig != nil {
			if generalConfig.Type != string(common.ConstTestTypeAPI) {
				return nil, errorx.Errorf(
					errorx.ValidateParamError,
					"the type of general configuration is not matching the target type, name: %s, type: %s, target type: %s",
					generalConfig.Name, generalConfig.Type, common.ConstTestTypeAPI,
				)
			}

			planData.Plan.GeneralConfig = &commonpb.GeneralConfig{}
			if err = qetutils.Copy(planData.Plan.GeneralConfig, generalConfig, l.converters...); err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.CopyToStructFailure, err.Error()),
					"failed to copy general configuration to api execution data, config: %s, error: %+v",
					jsonx.MarshalIgnoreError(generalConfig), err,
				)
			}
		}
	}

	// find the account configurations of api plan
	accountConfigs, err := l.svcCtx.ApiPlanModel.FindAccountConfigByPlanId(
		l.ctx, model.FindAccountConfigByPlanIdReq{
			ProjectId: projectId,
			PlanId:    planId,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find account configurations of api plan, project_id: %s, plan_id: %s, error: %+v",
			projectId, planId, err,
		)
	}

	planData.Plan.AccountConfigs = make([]*commonpb.AccountConfig, len(accountConfigs))
	for i, accountConfig := range accountConfigs {
		planData.Plan.AccountConfigs[i] = &commonpb.AccountConfig{}
		if err = qetutils.Copy(planData.Plan.AccountConfigs[i], accountConfig, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy account configuration to api execution data, config: %s, error: %+v",
				jsonx.MarshalIgnoreError(accountConfig), err,
			)
		}
	}

	var apiExecutionStructure *pb.ApiExecutionData
	if planData.Plan.Purpose == commonpb.PurposeType_PRECISION_TESTING { // 计划为精准测试模板计划
		if len(extra.GetServices()) == 0 {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"precision test plan must specify services, project_id: %s, plan_id: %s",
				projectId, planId,
			)
		} else if planData.Plan.GetType() != commonpb.TriggerMode_INTERFACE {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"precision test plan must be interface trigger mode, project_id: %s, plan_id: %s",
				projectId, planId,
			)
		}

		apiExecutionStructure, err = l.getPrecisionTestPlanApiExecutionStructureV2(planData, extra.GetServices())
	} else { // 计划为常规测试计划（本身包含真实的集合）
		apiExecutionStructure, err = l.getNormalPlanApiExecutionStructure(planData)
	}

	return apiExecutionStructure, err
}

// generateApiExecutionData 生成API执行数据
func (l *BaseLogic) generateApiExecutionData(root componentData, relations [][]*types.Relation) error {
	return mr.MapReduceVoid[*genApiExecutionData, any](
		func(source chan<- *genApiExecutionData) {
			for i, relation := range relations {
				root.ApiExecutionData.Children[i] = &pb.ApiExecutionData_ChildData{
					Child: make(
						[]*pb.ApiExecutionData, len(relation),
					),
				}
				for j, r := range relation {
					source <- &genApiExecutionData{root, int64(i), int64(j), r}
				}
			}
		}, func(item *genApiExecutionData, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			fn, ok := pb.ComponentExecutionDataFuncMap[item.Relation.Type]
			if !ok {
				err = errors.Wrapf(
					errorx.ErrGrpcInternal, "the type of component[%s] doesn't support", item.Relation.Type,
				)
				return
			}

			var c *model.Component
			c, err = model.CheckComponentByComponentId(
				l.ctx, l.svcCtx.ComponentModel, item.ProjectId, item.ParentId, item.ParentType, item.ParentVersion,
				item.Relation.Id,
			)
			if err != nil {
				return
			}

			v1, v2 := fn()
			if c.Data.Valid {
				if err = pb.DefaultUnmarshalOptions.Unmarshal(
					zeroutils.StringToByteSlice(c.Data.String), v2,
				); err != nil {
					err = errors.Wrapf(
						errorx.Err(errorx.SerializationError, err.Error()),
						"failed to unmarshal component data[%s], error: %+v", c.Data.String, err,
					)
					return
				}
			}

			// unwrap the reference of component group
			if item.Relation.ReferenceId != "" {
				var cg *model.ComponentGroup
				cg, err = model.CheckComponentGroupByComponentGroupId(
					l.ctx, l.svcCtx.ComponentGroupModel, item.ProjectId, item.Relation.ReferenceId, "",
				)
				if err != nil {
					return
				}

				rs := &types.Relation{}
				if err = jsonx.UnmarshalFromString(cg.ReferenceStructure, rs); err != nil {
					err = errors.Wrapf(
						errorx.Err(errorx.SerializationError, err.Error()),
						"failed to unmarshal component group structure[%s], error: %+v", cg.ReferenceStructure, err,
					)
					return
				}

				if r, ok := v2.(*pb.ReferenceComponent); ok {
					r.Version = cg.Version
					r.ReferenceComponentId = rs.Id
				}

				item.ParentId = cg.ComponentGroupId
				item.ParentType = constants.REFERENCE
				item.ParentVersion = cg.Version
				item.Relation.Children = [][]*types.Relation{{rs}}
			}

			item.ApiExecutionData.Children[item.ChildrenIndex].Child[item.ChildIndex] = &pb.ApiExecutionData{
				Id:       item.Relation.Id,
				Type:     pb.ApiExecutionDataType(pb.ApiExecutionDataType_value[item.Relation.Type]),
				Data:     v1,
				Children: make([]*pb.ApiExecutionData_ChildData, len(item.Relation.Children)),
			}
			if len(item.Relation.Children) > 0 {
				if err = l.generateApiExecutionData(
					componentData{
						ProjectId:        item.ProjectId,
						ParentId:         item.ParentId,
						ParentType:       item.ParentType,
						ParentVersion:    item.ParentVersion,
						ApiExecutionData: item.ApiExecutionData.Children[item.ChildrenIndex].Child[item.ChildIndex],
					}, item.Relation.Children,
				); err != nil {
					return
				}
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(l.ctx),
	)
}

// generateApiExecutionStructure 生成API执行结构
func (l *BaseLogic) generateApiExecutionStructure(cd componentData, items any) error {
	rv := reflect.ValueOf(items)
	if rv.Kind() != reflect.Slice && rv.Kind() != reflect.Array {
		return errorx.Errorf(errorx.DoesNotSupport, "the type of items[%T] is neither a slice nor an array", items)
	} else if rv.Len() == 0 {
		return nil
	}

	if cd.ApiExecutionData.Children == nil || cap(cd.ApiExecutionData.Children) == 0 {
		cd.ApiExecutionData.Children = make([]*pb.ApiExecutionData_ChildData, 1)
	}
	cd.ApiExecutionData.Children[0] = &pb.ApiExecutionData_ChildData{Child: make([]*pb.ApiExecutionData, 0, rv.Len())}

	return mr.MapReduceVoid[*genApiExecutionStructure, any](
		func(source chan<- *genApiExecutionStructure) {
			for i := 0; i < rv.Len(); i++ {
				cd.ApiExecutionData.Children[0].Child = append(
					cd.ApiExecutionData.Children[0].Child, &pb.ApiExecutionData{},
				)
				source <- &genApiExecutionStructure{
					componentData: componentData{
						ProjectId:        cd.ProjectId,
						ParentId:         cd.ParentId,
						ParentType:       cd.ParentType,
						ParentVersion:    cd.ParentVersion,
						ApiExecutionData: cd.ApiExecutionData.Children[0].Child[len(cd.ApiExecutionData.Children[0].Child)-1],
					},
					item: rv.Index(i).Interface(),
				}
			}
		}, func(item *genApiExecutionStructure, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			var (
				component componentData
				cases     []*model.SearchCaseInApiPlanItem
			)

			var target any
			switch vv := item.item.(type) {
			case *model.SearchSuiteInApiPlanItem:
				switch vv.SuiteType {
				case common.ConstReferenceTypeApiSuite:
					target = &pb.SuiteComponent{}
					item.ApiExecutionData.Id = vv.SuiteId
					item.ApiExecutionData.Type = pb.ApiExecutionDataType_API_SUITE
					item.ApiExecutionData.Data = &pb.ApiExecutionData_Suite{Suite: target.(*pb.SuiteComponent)}

					component = componentData{
						ProjectId:        item.ProjectId,
						ParentId:         vv.SuiteId,
						ParentType:       vv.SuiteType,
						ApiExecutionData: item.ApiExecutionData,
					}
				case common.ConstReferenceTypeInterfaceDocument:
					target = &pb.InterfaceDocumentComponent{DocumentId: vv.SuiteId} // 由于字段名称不一致，这里需要手工设置
					item.ApiExecutionData.Id = vv.SuiteId
					item.ApiExecutionData.Type = pb.ApiExecutionDataType_INTERFACE_DOCUMENT
					item.ApiExecutionData.Data = &pb.ApiExecutionData_InterfaceDocument{InterfaceDocument: target.(*pb.InterfaceDocumentComponent)}

					component = componentData{
						ProjectId:        item.ProjectId,
						ParentId:         vv.SuiteId,
						ParentType:       vv.SuiteType,
						ApiExecutionData: item.ApiExecutionData,
					}
				default:
					err = errors.Wrapf(errorx.ErrGrpcInternal, "the suite type[%s] doesn't support", vv.SuiteType)
					return
				}

				selectBuilder, _ := l.svcCtx.ApiPlanModel.GenerateSearchCaseInApiPlanSqlBuilder2(
					model.SearchCaseInApiPlanReq{
						ApiCaseModel:       l.svcCtx.ApiCaseModel,
						InterfaceCaseModel: l.svcCtx.InterfaceCaseModel,
						ProjectId:          item.ProjectId,
						PlanId:             item.ParentId,
						SuiteType:          vv.SuiteType,
						SuiteId:            vv.SuiteId,
					},
				)
				cases, err = l.svcCtx.ApiPlanModel.FindCasesInApiPlan(l.ctx, selectBuilder)
				if err != nil {
					return
				}
			case *model.SearchCaseInApiPlanItem:
				switch vv.CaseType {
				case common.ConstReferenceTypeApiCase:
					target = &pb.CaseComponent{}
					item.ApiExecutionData.Id = vv.CaseId
					item.ApiExecutionData.Type = pb.ApiExecutionDataType_API_CASE
					item.ApiExecutionData.Data = &pb.ApiExecutionData_Case{Case: target.(*pb.CaseComponent)}
				case common.ConstReferenceTypeInterfaceCase:
					target = &pb.InterfaceCaseComponent{DocumentId: vv.SuiteId} // 由于`item`缺少此字段，这里需要手工设置
					item.ApiExecutionData.Id = vv.CaseId
					item.ApiExecutionData.Type = pb.ApiExecutionDataType_INTERFACE_CASE
					item.ApiExecutionData.Data = &pb.ApiExecutionData_InterfaceCase{InterfaceCase: target.(*pb.InterfaceCaseComponent)}
				default:
					err = errors.Wrapf(errorx.ErrGrpcInternal, "the case type[%s] doesn't support", vv.CaseType)
					return
				}
			case *model.SearchCaseInApiSuiteItem:
				switch vv.CaseType {
				case common.ConstReferenceTypeApiCase:
					target = &pb.CaseComponent{}
					item.ApiExecutionData.Id = vv.CaseId
					item.ApiExecutionData.Type = pb.ApiExecutionDataType_API_CASE
					item.ApiExecutionData.Data = &pb.ApiExecutionData_Case{Case: target.(*pb.CaseComponent)}
				case common.ConstReferenceTypeInterfaceCase:
					target = &pb.InterfaceCaseComponent{DocumentId: vv.DocumentId}
					item.ApiExecutionData.Id = vv.CaseId
					item.ApiExecutionData.Type = pb.ApiExecutionDataType_INTERFACE_CASE
					item.ApiExecutionData.Data = &pb.ApiExecutionData_InterfaceCase{InterfaceCase: target.(*pb.InterfaceCaseComponent)}
				default:
					err = errors.Wrapf(errorx.ErrGrpcInternal, "the case type[%s] doesn't support", vv.CaseType)
					return
				}
			case *model.ApiCase:
				target = &pb.CaseComponent{}
				item.ApiExecutionData.Id = vv.CaseId
				item.ApiExecutionData.Type = pb.ApiExecutionDataType_API_CASE
				item.ApiExecutionData.Data = &pb.ApiExecutionData_Case{Case: target.(*pb.CaseComponent)}
			case *model.InterfaceCase:
				target = &pb.InterfaceCaseComponent{}
				item.ApiExecutionData.Id = vv.CaseId
				item.ApiExecutionData.Type = pb.ApiExecutionDataType_INTERFACE_CASE
				item.ApiExecutionData.Data = &pb.ApiExecutionData_InterfaceCase{InterfaceCase: target.(*pb.InterfaceCaseComponent)}
			}

			if target != nil {
				if err = qetutils.Copy(target, item.item, l.converters...); err != nil {
					return
				}
			}

			if !reflect.ValueOf(component).IsZero() && len(cases) != 0 {
				err = l.generateApiExecutionStructure(component, cases)
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(l.ctx),
	)
}

// generateApiExecutionDataWithError 生成带错误信息的API执行数据
func (l *BaseLogic) generateApiExecutionDataWithError(
	tp pb.ApiExecutionDataType, typeId string, err error,
) *pb.ApiExecutionData {
	data := &pb.ApiExecutionData{
		Id:   typeId,
		Type: tp,
	}

	if e, ok := err.(errorx.Errors); ok {
		data.Error = &pb.ApiExecutionData_ErrorData{
			Code:    uint32(e.Code()),
			Message: e.Message(),
		}
	} else {
		data.Error = &pb.ApiExecutionData_ErrorData{
			Code:    uint32(errorx.Unknown),
			Message: err.Error(),
		}
	}

	return data
}

// getUIPlanExecutionData 获取UI计划的执行数据
func (l *BaseLogic) getUIPlanExecutionData(projectID, planID string, extra *pb.UiPlanExtraData) (
	*pb.ApiExecutionData, error,
) {
	// validate the plan_id
	uiPlan, err := model.CheckUiPlanByPlanId(l.ctx, l.svcCtx.UiPlanModel, projectID, planID)
	if err != nil {
		return nil, err
	}

	// validate the state of ui plan
	if uiPlan.State != int64(pb.CommonState_CS_ENABLE) {
		return nil, errorx.Errorf(
			codes.PlanHasDisableFailure,
			"cannot to execute the ui plan[%s] whose the state is not enable", uiPlan.Name,
		)
	}

	planData := &pb.ApiExecutionData_UiPlan{UiPlan: &pb.UIPlanComponent{MetaData: &pb.UIPlanMetaData{}}}
	if err = qetutils.Copy(planData.UiPlan, uiPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy ui plan to api execution data, ui plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(uiPlan), err,
		)
	}
	if err = qetutils.Copy(planData.UiPlan.MetaData, uiPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy ui plan to ui plan meta data, ui plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(uiPlan), err,
		)
	}
	planData.UiPlan.MetaData.SuiteExecutionMode = pb.ExecutionMode_EM_PARALLEL
	planData.UiPlan.MetaData.CaseExecutionMode = pb.ExecutionMode(uiPlan.ExecutionMode)

	// handle the ui plan extra data
	if extra != nil && len(extra.GetDevices()) > 0 {
		if err = l.handleUIPlanExtraData(planData, extra); err == nil {
			planData.UiPlan.MetaData.Devices = extra.GetDevices()
			planData.UiPlan.MetaData.Together = extra.GetTogether()
		} else {
			l.Errorf(
				"failed to handle ui plan extra data, project_id: %s, plan_id: %s, extra: %s, error: %+v",
				projectID, planID, protobuf.MarshalJSONIgnoreError(extra), err,
			)
		}
	}

	// get the git configuration of ui plan
	gitConfig, err := model.CheckGitConfigByConfigID(l.ctx, l.svcCtx.GitConfigModel, projectID, uiPlan.GitConfigId)
	if err != nil {
		return nil, err
	}

	planData.UiPlan.MetaData.GitConfig = &commonpb.GitConfig{}
	if err = qetutils.Copy(planData.UiPlan.MetaData.GitConfig, gitConfig, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy git configuration to ui plan meta data, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(gitConfig), err,
		)
	}

	return l.generateUIExecutionDataWithUIPlan(planData)
}

func (l *BaseLogic) handleUIPlanExtraData(
	planData *pb.ApiExecutionData_UiPlan, extra *pb.UiPlanExtraData,
) error {
	if planData == nil || extra == nil || len(extra.GetDevices()) == 0 {
		return nil
	}

	var (
		uiPlan  = planData.UiPlan
		devices = extra.GetDevices()

		projectID = uiPlan.GetProjectId()
		usage     = commonpb.DeviceUsage_UI_TESTING
	)

	records, err := l.svcCtx.ProjectDeviceModel.FindAllBySearchReq(
		l.ctx, model.SearchProjectDeviceReq{
			BaseSearchReq: model.BaseSearchReq{
				ProjectID: projectID,
				Condition: &rpc.Condition{
					Group: &rpc.GroupCondition{
						Relationship: qetconstants.AND,
						Conditions: []*rpc.Condition{
							{
								Single: &rpc.SingleCondition{
									Field:   string(devicehubcommon.DeviceFieldOfUDID),
									Compare: qetconstants.DBIn,
									In:      devices,
								},
							},
							{
								Single: &rpc.SingleCondition{
									Field:   string(common.DeviceFieldOfUsage),
									Compare: qetconstants.EQ,
									Other: &rpc.Other{
										Value: strconv.Itoa(int(usage)),
									},
								},
							},
						},
					},
				},
			},
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find project device, project_id: %s, usage: %s, error: %+v",
			projectID, usage, err,
		)
	}

	result := make([]string, 0, len(records))
	for _, record := range records {
		result = append(result, record.Udid)
	}

	extra.Devices = result
	return nil
}

func (l *BaseLogic) generateUIExecutionDataWithUIPlan(planData *pb.ApiExecutionData_UiPlan) (
	*pb.ApiExecutionData, error,
) {
	var (
		uiPlan   = planData.UiPlan
		metadata = uiPlan.GetMetaData()
	)

	nodes, err := l.getAllGitProjectTreeNodes(uiPlan.GetProjectId(), metadata.GetGitConfig().GetConfigId())
	if err != nil {
		return nil, err
	}

	cases, err := l.getAllCasesInUIPlan(uiPlan.GetProjectId(), uiPlan.GetPlanId())
	if err != nil {
		return nil, err
	}

	devices := []string{""}
	if len(metadata.GetDevices()) > 0 && !metadata.GetTogether() {
		// 指定设备 且 分别执行
		devices = metadata.GetDevices()
	}
	multiple := len(devices)

	recorder := make(map[string]*pb.ApiExecutionData, cases.Size())
	suiteOfDisableCases := &pb.ApiExecutionData{
		Id:   common.ConstFakeSuiteOfDisableCasesID,
		Type: pb.ApiExecutionDataType_UI_SUITE,
		Data: &pb.ApiExecutionData_UiSuite{
			UiSuite: &pb.UISuiteComponent{
				ProjectId: uiPlan.GetProjectId(),
				SuiteId:   common.ConstFakeSuiteOfDisableCasesID,
				Name:      common.ConstFakeSuiteOfDisableCasesName,
				Alias:     common.ConstFakeSuiteOfDisableCasesName,
				Path:      common.ConstFakeSuiteOfDisableCasesID,
				State:     pb.CommonState_CS_ENABLE, // just the cases is disabled
			},
		},
		Children: nil,
	}
	cases.Each(
		func(key string, val *model.UiPlanReferenceRelationship) {
			var (
				ok, disable  bool
				self, parent *model.GitProjectTree
			)

			self, ok = nodes.Get(val.Path)
			if !ok {
				disable = true
			} else if parent, ok = nodes.Get(self.ParentPath.String); !ok {
				disable = true
			}

			for _, device := range devices {
				if disable {
					if len(suiteOfDisableCases.Children) == 0 {
						suiteOfDisableCases.Children = []*pb.ApiExecutionData_ChildData{
							{
								Child: make([]*pb.ApiExecutionData, 0, cases.Size()*multiple),
							},
						}

						// record the suite
						recorder[common.ConstFakeSuiteOfDisableCasesID] = suiteOfDisableCases
					}

					suiteOfDisableCases.Children[0].Child = append(
						suiteOfDisableCases.Children[0].Child, &pb.ApiExecutionData{
							Id:   val.Path,
							Type: pb.ApiExecutionDataType_UI_CASE,
							Data: &pb.ApiExecutionData_UiCase{
								UiCase: &pb.UICaseComponent{
									ProjectId: val.ProjectId,
									CaseId:    val.Path,
									Path:      val.Path,
									State:     pb.CommonState_CS_DISABLE,
									Udid:      device,
								},
							},
						},
					)
				} else {
					data, ok := recorder[parent.Path]
					if !ok {
						data = &pb.ApiExecutionData{
							Id:   parent.Path,
							Type: pb.ApiExecutionDataType_UI_SUITE,
							Data: &pb.ApiExecutionData_UiSuite{
								UiSuite: &pb.UISuiteComponent{
									ProjectId: parent.ProjectId,
									SuiteId:   parent.Path,
									Name:      parent.Name,
									Alias:     parent.Alias.String,
									Path:      parent.Path,
									State:     pb.CommonState_CS_ENABLE,
								},
							},
							Children: []*pb.ApiExecutionData_ChildData{
								{
									Child: make([]*pb.ApiExecutionData, 0, cases.Size()*multiple),
								},
							},
						}

						recorder[parent.Path] = data
					} else if len(data.Children) == 0 {
						data.Children = []*pb.ApiExecutionData_ChildData{
							{
								Child: make([]*pb.ApiExecutionData, 0, cases.Size()*multiple),
							},
						}
					}

					// put the case into the suite
					data.Children[0].Child = append(
						data.Children[0].Child, &pb.ApiExecutionData{
							Id:   self.Path,
							Type: pb.ApiExecutionDataType_UI_CASE,
							Data: &pb.ApiExecutionData_UiCase{
								UiCase: &pb.UICaseComponent{
									ProjectId: self.ProjectId,
									CaseId:    self.Path,
									Name:      self.Name,
									Alias:     self.Alias.String,
									Path:      self.Path,
									State:     pb.CommonState_CS_ENABLE,
									Udid:      device,
								},
							},
						},
					)
				}
			}
		},
	)

	apiExecutionData := &pb.ApiExecutionData{
		Id:       uiPlan.GetPlanId(),
		Type:     pb.ApiExecutionDataType_UI_PLAN,
		Data:     planData,
		Children: nil,
	}
	for _, data := range recorder {
		if len(apiExecutionData.Children) == 0 {
			apiExecutionData.Children = []*pb.ApiExecutionData_ChildData{
				{
					Child: make([]*pb.ApiExecutionData, 0, len(recorder)),
				},
			}
		}

		apiExecutionData.Children[0].Child = append(apiExecutionData.Children[0].Child, data)
	}

	return apiExecutionData, nil
}

func (l *BaseLogic) getAllGitProjectTreeNodes(projectID, gitConfigID string) (
	*hashmap.Map[string, *model.GitProjectTree], error,
) {
	nodes, err := l.svcCtx.GitProjectTreeModel.FindAll(l.ctx, projectID, gitConfigID)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find git project tree with project_id[%s] and git_config_id[%s], error: %+v",
			projectID, gitConfigID, err,
		)
	}

	m := hashmap.New[string, *model.GitProjectTree](uint64(len(nodes)), generic.Equals[string], generic.HashString)
	for _, node := range nodes {
		m.Put(node.Path, node)
	}

	return m, nil
}

func (l *BaseLogic) getAllCasesInUIPlan(projectID, planID string) (
	*hashmap.Map[string, *model.UiPlanReferenceRelationship], error,
) {
	rrs, err := l.svcCtx.UiPlanReferenceModel.FindReferenceByPlanId(l.ctx, projectID, planID)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find reference of ui plan with project_id[%s] and plan_id[%s], error: %+v",
			projectID, planID, err,
		)
	}

	m := hashmap.New[string, *model.UiPlanReferenceRelationship](
		uint64(len(rrs)), generic.Equals[string], generic.HashString,
	)
	for _, rr := range rrs {
		m.Put(rr.Path, rr)
	}

	return m, nil
}

// Deprecated: use `getPerfPlanV2ExecutionData` instead.
// getPerfPlanExecutionData 获取压测计划的执行数据
func (l *BaseLogic) getPerfPlanExecutionData(projectID, planID string) (*pb.ApiExecutionData, error) {
	// validate the plan_id
	perfPlan, err := model.CheckPerfPlanByPlanID(l.ctx, l.svcCtx.PerfPlanModel, projectID, planID)
	if err != nil {
		return nil, err
	}

	// validate the state of pref plan
	if perfPlan.State != int64(pb.CommonState_CS_ENABLE) {
		return nil, errorx.Errorf(
			codes.PlanHasDisableFailure,
			"cannot to execute the perf plan[%s] whose the state is not enable",
			perfPlan.Name,
		)
	}

	planData := &pb.ApiExecutionData_PerfPlan{PerfPlan: &pb.PerfPlanComponent{MetaData: &pb.PerfPlanMetaData{}}}
	if err = qetutils.Copy(planData.PerfPlan, perfPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf plan to api execution data, perf plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfPlan), err,
		)
	}
	if err = qetutils.Copy(planData.PerfPlan.MetaData, perfPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf plan to perf plan meta data, perf plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfPlan), err,
		)
	}

	// generate the protobuf configurations in perf plan
	if planData.PerfPlan.MetaData.ProtobufConfigs, err = l.generateProtobufConfigs(projectID, planID); err != nil {
		return nil, err
	}

	if perfPlan.GeneralConfigId.Valid && perfPlan.GeneralConfigId.String != "" {
		// generate the general configuration of perf plan
		if planData.PerfPlan.MetaData.GeneralConfig, err = l.generateGeneralConfig(
			projectID, perfPlan.GeneralConfigId.String, common.ConstTestTypePerf,
		); err != nil {
			return nil, err
		}
	}

	if perfPlan.AccountConfigId.Valid && perfPlan.AccountConfigId.String != "" {
		// generate the account configuration of perf plan
		if planData.PerfPlan.MetaData.AccountConfig, err = l.generateAccountConfig(
			projectID, perfPlan.AccountConfigId.String,
		); err != nil {
			return nil, err
		}
	}

	return l.generatePerfExecutionDataWithPerfPlan(planData)
}

func (l *BaseLogic) generatePerfStopRules(projectID, planID string) ([]*commonpb.PerfStopRule, error) {
	records, err := l.svcCtx.PerfPlanV2Model.FindRulesInPerfPlan(
		l.ctx, model.SearchRuleInPerfPlanV2Req{
			BaseSearchReq: model.BaseSearchReq{
				ProjectID: projectID,
				Condition: &rpc.Condition{
					Single: &rpc.SingleCondition{
						Field:   "`state`",
						Compare: qetconstants.EQ,
						Other: &rpc.Other{
							Value: strconv.FormatInt(int64(pb.CommonState_CS_ENABLE), 10),
						},
					},
				},
			},
			PlanID:            planID,
			PerfStopRuleModel: l.svcCtx.PerfStopRuleModel,
		},
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf stop rule in perf plan, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}

	rules := make([]*commonpb.PerfStopRule, 0, len(records))
	if err = qetutils.Copy(&rules, records, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf stop rules to response, rules: %s, error: %+v",
			jsonx.MarshalIgnoreError(records), err,
		)
	}

	return rules, nil
}

func (l *BaseLogic) generateProtobufConfigs(projectID, planID string) ([]*commonpb.ProtobufConfig, error) {
	records, err := l.svcCtx.PerfPlanV2Model.FindProtobufConfigsInPerfPlan(
		l.ctx, model.SearchProtobufConfigInPerfPlanV2Req{
			BaseSearchReq: model.BaseSearchReq{
				ProjectID: projectID,
			},
			PlanID:              planID,
			ProtobufConfigModel: l.svcCtx.ProtobufConfigModel,
		},
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find protobuf configuration in perf plan, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}

	configs := make([]*commonpb.ProtobufConfig, 0, len(records))
	for _, record := range records {
		config, err := l.generateProtobufConfig(projectID, record.ConfigId)
		if err != nil {
			return nil, err
		}

		configs = append(configs, config)
	}

	return configs, nil
}

func (l *BaseLogic) generateProtobufConfig(projectID, configID string) (*commonpb.ProtobufConfig, error) {
	// get protobuf configuration by config_id
	config, err := model.CheckProtobufConfigByConfigID(l.ctx, l.svcCtx.ProtobufConfigModel, projectID, configID)
	if err != nil {
		return nil, err
	}

	item := &commonpb.ProtobufConfig{}
	if err = qetutils.Copy(item, config, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy git configuration, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(config), err,
		)
	}

	// get git configuration by git_config_id
	gitCache := hashmap.New[string, *commonpb.GitConfig](
		constants.ConstDefaultMakeMapSize, generic.Equals[string], generic.HashString,
	)
	item.GitConfig, err = l.generateGitConfig(projectID, config.GitConfigId, gitCache)
	if err != nil {
		return nil, err
	}
	gitCache.Put(config.GitConfigId, item.GetGitConfig())

	// get protobuf dependencies by config_id
	pbCache := hashmap.New[string, *commonpb.ProtobufDependenceConfig](
		constants.ConstDefaultMakeMapSize, generic.Equals[string], generic.HashString,
	)
	pbCache.Put(
		configID, &commonpb.ProtobufDependenceConfig{
			ProjectId:   item.GetProjectId(),
			ConfigId:    item.GetConfigId(),
			Name:        item.GetName(),
			Description: item.GetDescription(),
			GitConfig:   item.GetGitConfig(),
			ImportPath:  item.GetImportPath(),
		},
	)
	item.Dependencies, err = l.generateProtobufDependence(projectID, configID, gitCache, pbCache)
	if err != nil {
		return nil, err
	}

	return item, nil
}

func (l *BaseLogic) generateGitConfig(
	projectID, configID string, cache *hashmap.Map[string, *commonpb.GitConfig],
) (gc *commonpb.GitConfig, err error) {
	if cache != nil {
		if val, ok := cache.Get(configID); ok {
			return val, nil
		}
	}

	defer func() {
		if cache != nil && gc != nil {
			cache.Put(configID, gc)
		}
	}()

	config, err := model.CheckGitConfigByConfigID(l.ctx, l.svcCtx.GitConfigModel, projectID, configID)
	if err != nil {
		return nil, err
	}

	gc = &commonpb.GitConfig{}
	if err = qetutils.Copy(gc, config, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy git configuration, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(config), err,
		)
	}

	return gc, nil
}

func (l *BaseLogic) generateProtobufDependence(
	projectID, configID string,
	gitCache *hashmap.Map[string, *commonpb.GitConfig],
	pbCache *hashmap.Map[string, *commonpb.ProtobufDependenceConfig],
) (
	[]*commonpb.ProtobufDependenceConfig, error,
) {
	if gitCache == nil {
		gitCache = hashmap.New[string, *commonpb.GitConfig](
			constants.ConstDefaultMakeMapSize, generic.Equals[string], generic.HashString,
		)
	}
	if pbCache == nil {
		pbCache = hashmap.New[string, *commonpb.ProtobufDependenceConfig](
			constants.ConstDefaultMakeMapSize, generic.Equals[string], generic.HashString,
		)
	}

	configs, err := l.svcCtx.ProtobufConfigModel.FindDependencies(l.ctx, projectID, configID)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find protobuf dependence config, project_id: %s, config_id: %s, error: %+v",
			projectID, configID, err,
		)
	}

	dependencies := make([]*commonpb.ProtobufDependenceConfig, 0, len(configs))
	for _, config := range configs {
		_, ok := pbCache.Get(config.ConfigId)
		if ok {
			continue
		}

		pdc := &commonpb.ProtobufDependenceConfig{
			ProjectId:   config.ProjectId,
			ConfigId:    config.ConfigId,
			Name:        config.Name,
			Description: config.Description.String,
			ImportPath:  config.ImportPath,
		}
		pdc.GitConfig, err = l.generateGitConfig(config.ProjectId, config.GitConfigId, gitCache)
		if err != nil {
			return nil, err
		}

		pbCache.Put(config.ConfigId, pdc)
		dependencies = append(dependencies, pdc)

		var deps []*commonpb.ProtobufDependenceConfig
		deps, err = l.generateProtobufDependence(projectID, config.ConfigId, gitCache, pbCache)
		if err != nil {
			return nil, err
		}

		dependencies = append(dependencies, deps...)
	}

	return dependencies, nil
}

func (l *BaseLogic) generateGeneralConfig(
	projectID, configID string, targetType common.TestType,
) (*commonpb.GeneralConfig, error) {
	config, err := model.CheckGeneralConfigByConfigId(
		l.ctx, l.svcCtx.GeneralConfigModel, projectID, configID,
	)
	if err != nil {
		return nil, err
	}
	if config.Type != string(targetType) {
		return nil, errorx.Errorf(
			errorx.ValidateParamError,
			"the type of general configuration is not matching the target type, name: %s, type: %s, target type: %s",
			config.Name, config.Type, targetType,
		)
	}

	generalConfig := &commonpb.GeneralConfig{}
	if err = qetutils.Copy(generalConfig, config, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy general configuration data, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(config), err,
		)
	}

	return generalConfig, nil
}

func (l *BaseLogic) generateAccountConfig(projectID, configID string) (*commonpb.AccountConfig, error) {
	config, err := model.CheckAccountConfigByConfigId(l.ctx, l.svcCtx.AccountConfigModel, projectID, configID)
	if err != nil {
		return nil, err
	}

	accountConfig := &commonpb.AccountConfig{}
	if err = qetutils.Copy(accountConfig, config, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy account configuration data, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(config), err,
		)
	}

	return accountConfig, nil
}

func (l *BaseLogic) generatePerfExecutionDataWithPerfPlan(planData *pb.ApiExecutionData_PerfPlan) (
	*pb.ApiExecutionData, error,
) {
	var (
		perfPlan = planData.PerfPlan

		projectID = perfPlan.GetProjectId()
		planID    = perfPlan.GetPlanId()
	)

	perfCases, err := l.svcCtx.PerfCaseModel.FindAllByPlanID(l.ctx, projectID, planID)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf cases by perf plan id, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}

	suiteOfEnableCases := &pb.ApiExecutionData{
		Id:   common.ConstFakeSuiteOfEnableCasesID,
		Type: pb.ApiExecutionDataType_PERF_SUITE,
		Data: &pb.ApiExecutionData_PerfSuite{
			PerfSuite: &pb.PerfSuiteComponent{
				ProjectId: perfPlan.GetProjectId(),
				SuiteId:   common.ConstFakeSuiteOfEnableCasesID,
				Name:      common.ConstFakeSuiteOfEnableCasesName,
				State:     pb.CommonState_CS_ENABLE,
			},
		},
		Children: nil,
	}
	suiteOfDisableCases := &pb.ApiExecutionData{
		Id:   common.ConstFakeSuiteOfDisableCasesID,
		Type: pb.ApiExecutionDataType_PERF_SUITE,
		Data: &pb.ApiExecutionData_PerfSuite{
			PerfSuite: &pb.PerfSuiteComponent{
				ProjectId: perfPlan.GetProjectId(),
				SuiteId:   common.ConstFakeSuiteOfDisableCasesID,
				Name:      common.ConstFakeSuiteOfDisableCasesName,
				State:     pb.CommonState_CS_ENABLE, // just the cases is disabled
			},
		},
		Children: nil,
	}
	for _, perfCase := range perfCases {
		var (
			data      *pb.ApiExecutionData
			state     pb.CommonState
			component *pb.PerfDataComponent
		)
		if perfCase.State == int64(pb.CommonState_CS_ENABLE) {
			data = suiteOfEnableCases
			state = pb.CommonState_CS_ENABLE

			if perfPlan.GetMetaData().GetTargetMaxRps() < perfCase.TargetRps {
				perfPlan.MetaData.TargetMaxRps = perfCase.TargetRps
			}
		} else {
			data = suiteOfDisableCases
			state = pb.CommonState_CS_DISABLE
		}

		if perfCase.PerfDataId != "" {
			perfData, err := model.CheckPerfDataByDataID(
				l.ctx, l.svcCtx.PerfDataModel, perfCase.ProjectId, perfCase.PerfDataId,
			)
			if err != nil {
				return nil, err
			}

			component = &pb.PerfDataComponent{}
			if err = qetutils.Copy(component, perfData, l.converters...); err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.CopyToStructFailure, err.Error()),
					"failed to copy perf data to component of perf data, data: %s, error: %+v",
					jsonx.MarshalIgnoreError(perfData), err,
				)
			}
		}

		if len(data.GetChildren()) == 0 {
			data.Children = []*pb.ApiExecutionData_ChildData{
				{
					Child: make([]*pb.ApiExecutionData, 0, len(perfCases)),
				},
			}
		}
		data.Children[0].Child = append(
			data.Children[0].Child, &pb.ApiExecutionData{
				Id:   perfCase.CaseId,
				Type: pb.ApiExecutionDataType_PERF_CASE,
				Data: &pb.ApiExecutionData_PerfCase{
					PerfCase: &pb.PerfCaseComponent{
						ProjectId:   perfCase.ProjectId,
						CaseId:      perfCase.CaseId,
						Name:        perfCase.Name,
						Description: perfCase.Description.String,
						Extension:   perfCase.Extension,
						Hash:        perfCase.Hash,
						Size:        uint32(perfCase.Size),
						Path:        perfCase.Path,
						TargetRps:   perfCase.TargetRps,
						State:       state,
						PerfData:    component,
						NumberOfVu:  uint32(perfCase.NumberOfVu),
						LoadGenerator: &commonpb.LoadGenerator{
							NumberOfLg:       uint32(perfCase.NumberOfLg),
							RequestsOfCpu:    perfCase.RequestsOfCpu,
							RequestsOfMemory: perfCase.RequestsOfMemory,
							LimitsOfCpu:      perfCase.LimitsOfCpu,
							LimitsOfMemory:   perfCase.LimitsOfMemory,
						},
					},
				},
			},
		)
	}

	return &pb.ApiExecutionData{
		Id:   perfPlan.GetPlanId(),
		Type: pb.ApiExecutionDataType_PERF_PLAN,
		Data: planData,
		Children: []*pb.ApiExecutionData_ChildData{
			{
				Child: []*pb.ApiExecutionData{suiteOfEnableCases, suiteOfDisableCases},
			},
		},
	}, nil
}

// getPerfPlanV2ExecutionData 获取压测计划V2的执行数据
func (l *BaseLogic) getPerfPlanV2ExecutionData(projectID, planID string) (*pb.ApiExecutionData, error) {
	// validate the plan_id
	perfPlan, err := model.CheckPerfPlanV2ByPlanID(l.ctx, l.svcCtx.PerfPlanV2Model, projectID, planID)
	if err != nil {
		return nil, err
	}

	// validate the state of pref plan
	if perfPlan.State != int64(pb.CommonState_CS_ENABLE) {
		return nil, errorx.Errorf(
			codes.PlanHasDisableFailure,
			"cannot to execute the perf plan[%s] whose the state is not enable",
			perfPlan.Name,
		)
	}

	planData := &pb.ApiExecutionData_PerfPlan{PerfPlan: &pb.PerfPlanComponent{MetaData: &pb.PerfPlanMetaData{}}}
	if err = qetutils.Copy(planData.PerfPlan, perfPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf plan to api execution data, perf plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfPlan), err,
		)
	}
	if err = qetutils.Copy(planData.PerfPlan.MetaData, perfPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf plan to perf plan meta data, perf plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfPlan), err,
		)
	}
	planData.PerfPlan.MetaData.RateLimits = calculate.GetDefaultPerfRateLimitsByTargetEnv(planData.PerfPlan.MetaData.GetTargetEnv())
	if planData.PerfPlan.GetMetaData().GetProtocol() == commonpb.Protocol_PROTOCOL_TT_AUTH &&
		perfPlan.AuthRateLimits.Valid && perfPlan.AuthRateLimits.String != "" {
		var authRateLimits []*commonpb.RateLimitV2
		if err = protobuf.UnmarshalJSONWithMessagesFromString(
			perfPlan.AuthRateLimits.String, &authRateLimits,
		); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.SerializationError, err.Error()),
				"failed to unmarshal auth rate limits of perf plan, perf plan: %s, error: %+v",
				jsonx.MarshalIgnoreError(perfPlan), err,
			)
		}

		planData.PerfPlan.MetaData.RateLimits.Auth = &commonpb.PerfRateLimits_AuthConfig{
			RateLimits: authRateLimits,
			Key:        "",
		}
	}

	// generate the perf stop rules in perf plan
	if planData.PerfPlan.MetaData.Rules, err = l.generatePerfStopRules(projectID, planID); err != nil {
		return nil, err
	}

	// generate the protobuf configurations in perf plan
	if planData.PerfPlan.MetaData.ProtobufConfigs, err = l.generateProtobufConfigs(projectID, planID); err != nil {
		return nil, err
	}

	if perfPlan.GeneralConfigId.Valid && perfPlan.GeneralConfigId.String != "" {
		// generate the general configuration of perf plan
		if planData.PerfPlan.MetaData.GeneralConfig, err = l.generateGeneralConfig(
			projectID, perfPlan.GeneralConfigId.String, common.ConstTestTypePerf,
		); err != nil {
			return nil, err
		}
	}

	if perfPlan.AccountConfigId.Valid && perfPlan.AccountConfigId.String != "" {
		// generate the account configuration of perf plan
		if planData.PerfPlan.MetaData.AccountConfig, err = l.generateAccountConfig(
			projectID, perfPlan.AccountConfigId.String,
		); err != nil {
			return nil, err
		}
	}

	return l.generatePerfExecutionDataWithPerfPlanV2(planData)
}

func (l *BaseLogic) generatePerfExecutionDataWithPerfPlanV2(planData *pb.ApiExecutionData_PerfPlan) (
	*pb.ApiExecutionData, error,
) {
	var (
		perfPlan     = planData.PerfPlan
		perfPlanData = &pb.ApiExecutionData{
			Id:   perfPlan.GetPlanId(),
			Type: pb.ApiExecutionDataType_PERF_PLAN,
			Data: planData,
		}

		projectID      = perfPlan.GetProjectId()
		planID         = perfPlan.GetPlanId()
		authRateLimits = perfPlan.GetMetaData().GetRateLimits().GetAuth().GetRateLimits()

		maxDuration  time.Duration
		planDuration = time.Duration(perfPlan.GetMetaData().GetDuration()) * time.Second
	)

	perfCases, err := l.svcCtx.PerfPlanV2Model.FindCasesInPerfPlan(
		l.ctx, model.SearchCaseInPerfPlanV2Req{
			BaseSearchReq: model.BaseSearchReq{
				ProjectID: projectID,
			},
			PlanID:          planID,
			PerfCaseV2Model: l.svcCtx.PerfCaseV2Model,
		},
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf cases by perf plan id, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}

	perfPlanData.Children = []*pb.ApiExecutionData_ChildData{
		{
			Child: make([]*pb.ApiExecutionData, 0, len(perfCases)),
		},
	}
	perfSuiteCache := make(map[string]*pb.ApiExecutionData, len(perfCases))
	perfMethodCache := make(map[string]*serviceMethodData, constants.ConstDefaultMakeMapSize)
	perfServiceCache := make(map[string]*commonpb.PerfServiceMetaData, constants.ConstDefaultMakeMapSize)
	userInfoCache := make(map[string]*commonpb.PerfUserInfo, constants.ConstDefaultMakeMapSize)
	for _, perfCase := range perfCases {
		var (
			perfSuiteData *pb.ApiExecutionData
			ok            bool
		)

		perfSuiteData, ok = perfSuiteCache[perfCase.CategoryId]
		if !ok {
			category, err := model.CheckCategoryByCategoryId(
				l.ctx, l.svcCtx.CategoryModel, projectID, common.ConstCategoryTreeTypePerfCase, perfCase.CategoryId,
			)
			if err != nil {
				return nil, err
			}

			perfSuiteData = &pb.ApiExecutionData{
				Id:   category.CategoryId, // category id as perf suite id
				Type: pb.ApiExecutionDataType_PERF_SUITE,
				Data: &pb.ApiExecutionData_PerfSuite{
					PerfSuite: &pb.PerfSuiteComponent{
						ProjectId:   category.ProjectId,
						SuiteId:     category.CategoryId,
						Name:        category.Name,
						Description: category.Description.String,
						State:       pb.CommonState_CS_ENABLE,
					},
				},
				Children: []*pb.ApiExecutionData_ChildData{
					{
						Child: make([]*pb.ApiExecutionData, 0, len(perfCases)),
					},
				},
			}
			perfPlanData.Children[0].Child = append(perfPlanData.Children[0].Child, perfSuiteData)
			perfSuiteCache[perfCase.CategoryId] = perfSuiteData
		}

		caseData, err := l.getPerfCaseData(perfPlan.GetMetaData().GetTargetEnv(), perfCase, authRateLimits)
		if err != nil {
			return nil, err
		}

		if perfCase.State == int64(pb.CommonState_CS_ENABLE) {
			if perfPlan.GetMetaData().GetTargetMaxRps() < perfCase.TargetRps {
				perfPlan.MetaData.TargetMaxRps = perfCase.TargetRps
			}

			options := make([]calculate.Option, 0, 2)
			protocol := perfPlan.GetMetaData().GetProtocol()
			if protocol == commonpb.Protocol_PROTOCOL_TT || protocol == commonpb.Protocol_PROTOCOL_TT_AUTH {
				if err = l.getPerfServiceMetadata(
					perfPlan.GetMetaData(), caseData, perfMethodCache, perfServiceCache, userInfoCache,
				); err != nil {
					return nil, err
				}

				options = append(options, calculate.WithAuthRateLimits(authRateLimits))
			} else {
				options = append(options, calculate.WithoutAuth())
			}
			options = append(options, calculate.WithGlobalRateLimits(caseData.PerfCase.GetRateLimits()))

			durations := calculate.CalculateTotalCaseDuration(
				&commonpb.PerfCaseContentV2{}, uint32(perfCase.NumberOfVu), options...,
			)
			if maxDuration < durations.TotalDuration {
				maxDuration = durations.TotalDuration
			}
		}

		perfSuiteData.Children[0].Child = append(
			perfSuiteData.Children[0].Child, &pb.ApiExecutionData{
				Id:   perfCase.CaseId,
				Type: pb.ApiExecutionDataType_PERF_CASE,
				Data: caseData,
			},
		)
	}

	if planDuration < maxDuration {
		l.Warnf(
			"the duration of perf plan is less than the currently calculated value, the currently calculated value will be used automatically, project_id: %s, plan_id: %s, duration: %s -> %s",
			projectID, planID, planDuration, maxDuration,
		)
		perfPlan.MetaData.Duration = uint32(maxDuration.Seconds())
	}

	if err = l.getLarkUserIDs(userInfoCache); err != nil {
		l.Errorf("failed to get lark user id, project_id: %s, plan_id: %s, error: %+v", projectID, planID, err)
	}

	perfPlan.MetaData.Services = make([]*commonpb.PerfServiceMetaData, 0, len(perfServiceCache))
	for _, service := range perfServiceCache {
		perfPlan.MetaData.Services = append(perfPlan.MetaData.Services, service)
	}

	return perfPlanData, nil
}

func (l *BaseLogic) getPerfCaseData(
	targetEnv commonpb.TargetEnvironment, perfCase *model.SearchCaseInPerfPlanV2Item,
	authRateLimits []*commonpb.RateLimitV2,
) (*pb.ApiExecutionData_PerfCase, error) {
	var (
		perfCaseState = pb.CommonState_CS_DISABLE

		rateLimits        []*commonpb.RateLimitV2
		perfDataComponent *pb.PerfDataComponent
		perfCaseComponent *pb.PerfCaseComponent
	)

	if perfCase.State == int64(pb.CommonState_CS_ENABLE) {
		perfCaseState = pb.CommonState_CS_ENABLE
	}

	if perfCase.RateLimits.Valid && perfCase.RateLimits.String != "" {
		if err := protobuf.UnmarshalJSONWithMessagesFromString(perfCase.RateLimits.String, &rateLimits); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.SerializationError, err.Error()),
				"failed to unmarshal rate limits of perf case, project_id: %s, plan_id: %s, case_id: %s, rate limits: %s, error: %+v",
				perfCase.ProjectId, perfCase.PlanId, perfCase.CaseId, perfCase.RateLimits.String, err,
			)
		}
	}

	if perfCase.PerfDataId != "" {
		perfData, err := model.CheckPerfDataByDataID(
			l.ctx, l.svcCtx.PerfDataModel, perfCase.ProjectId, perfCase.PerfDataId,
		)
		if err != nil {
			return nil, err
		}

		perfDataComponent = &pb.PerfDataComponent{}
		if err = qetutils.Copy(perfDataComponent, perfData, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy perf data to component of perf data, project_id: %s, plan_id: %s, case_id: %s, data: %s, error: %+v",
				perfCase.ProjectId, perfCase.PlanId, perfCase.CaseId, jsonx.MarshalIgnoreError(perfData), err,
			)
		}
	}

	content, err := l.getPerfCaseContent(targetEnv, perfCase, authRateLimits, rateLimits)
	if err != nil {
		return nil, err
	}

	perfCaseComponent = &pb.PerfCaseComponent{
		ProjectId:     perfCase.ProjectId,
		CaseId:        perfCase.CaseId,
		Name:          perfCase.Name,
		Description:   perfCase.Description.String,
		TargetRps:     perfCase.TargetRps,
		RateLimits:    rateLimits,
		SetupSteps:    content.GetSetupSteps(),
		SerialSteps:   content.GetSerialSteps(),
		ParallelSteps: content.GetParallelSteps(),
		TeardownSteps: content.GetTeardownSteps(),
		PerfData:      perfDataComponent,
		NumberOfVu:    uint32(perfCase.NumberOfVu),
		LoadGenerator: &commonpb.LoadGenerator{
			NumberOfLg:       uint32(perfCase.NumberOfLg),
			RequestsOfCpu:    perfCase.RequestsOfCpu,
			RequestsOfMemory: perfCase.RequestsOfMemory,
			LimitsOfCpu:      perfCase.LimitsOfCpu,
			LimitsOfMemory:   perfCase.LimitsOfMemory,
		},
		State: perfCaseState,
	}

	if perfDataComponent != nil {
		// get the number of virtual users of perf data
		perfCaseComponent.NumberOfVu = perfDataComponent.GetNumberOfVu()
	} else if perfCase.CustomVu == 0 || perfCase.NumberOfVu <= 0 {
		// calculate the number of virtual users by serial and parallel steps
		perfCaseComponent.NumberOfVu = uint32(
			calculate.CalculateNumberOfVirtualUsers(
				perfCaseComponent.GetSerialSteps(), perfCaseComponent.GetParallelSteps(), rateLimits,
			),
		)
	}

	lg1 := &commonpb.LoadGenerator{}
	if perfCase.CustomLg != 0 {
		lg1 = &commonpb.LoadGenerator{
			NumberOfLg:       uint32(perfCase.NumberOfLg),
			RequestsOfCpu:    perfCase.RequestsOfCpu,
			RequestsOfMemory: perfCase.RequestsOfMemory,
			LimitsOfCpu:      perfCase.LimitsOfCpu,
			LimitsOfMemory:   perfCase.LimitsOfMemory,
		}
	}

	// calculate the load generator resource
	lg2 := calculate.CalculateLoadGeneratorResource(perfCaseComponent.NumberOfVu, lg1)
	perfCaseComponent.LoadGenerator = &commonpb.LoadGenerator{
		NumberOfLg:       lg2.GetNumberOfLg(),
		RequestsOfCpu:    lg2.GetRequestsOfCpu(),
		RequestsOfMemory: lg2.GetRequestsOfMemory(),
		LimitsOfCpu:      lg2.GetLimitsOfCpu(),
		LimitsOfMemory:   lg2.GetLimitsOfMemory(),
	}

	return &pb.ApiExecutionData_PerfCase{
		PerfCase: perfCaseComponent,
	}, nil
}

func (l *BaseLogic) getPerfCaseContent(
	targetEnv commonpb.TargetEnvironment, perfCase *model.SearchCaseInPerfPlanV2Item,
	authRateLimits, caseRateLimits []*commonpb.RateLimitV2,
) (content *commonpb.PerfCaseContentV2, err error) {
	content = &commonpb.PerfCaseContentV2{
		SetupSteps:    []*commonpb.PerfCaseStepV2{},
		SerialSteps:   []*commonpb.PerfCaseStepV2{},
		ParallelSteps: []*commonpb.PerfCaseStepV2{},
		TeardownSteps: []*commonpb.PerfCaseStepV2{},
	}

	if perfCase.Protocol == protobuf.GetEnumStringOf(commonpb.Protocol_PROTOCOL_TT_AUTH) {
		step := common.GetTTAuthPerfCaseStep()
		if len(caseRateLimits) > 0 {
			step.RateLimits = caseRateLimits
		} else if len(authRateLimits) > 0 {
			step.RateLimits = authRateLimits
		} else {
			step.RateLimits = calculate.GetDefaultPerfRateLimitsByTargetEnv(targetEnv).GetAuth().GetRateLimits()
		}

		content.SerialSteps = append(content.SerialSteps, step)
	} else {
		steps, err := l.svcCtx.PerfCaseStepV2Model.FindByCaseID(l.ctx, perfCase.ProjectId, perfCase.CaseId)
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find perf case steps by case id, project_id: %s, plan_id: %s, case_id: %s, error: %+v",
				perfCase.ProjectId, perfCase.PlanId, perfCase.CaseId, err,
			)
		}

		// sort the steps by type and index
		sort.SliceStable(
			steps, func(i, j int) bool {
				if steps[i].Type != steps[j].Type {
					return perfCaseStepTypeOrders[steps[i].Type] < perfCaseStepTypeOrders[steps[j].Type]
				}

				return steps[i].Index < steps[j].Index
			},
		)
		for _, step := range steps {
			v := &commonpb.PerfCaseStepV2{
				Name:   step.Name,
				Url:    step.Url,
				Method: step.Method,
				Body:   step.Body,
				Sleep:  step.Sleep,
			}

			switch step.Type {
			// setup and teardown steps use the rate limits of step in case first
			case string(common.ConstPerfCaseStepTypeSetup), string(common.ConstPerfCaseStepTypeTeardown):
				if step.RateLimits.Valid && step.RateLimits.String != "" {
					stepRateLimits := make([]*commonpb.RateLimitV2, 0, constants.ConstDefaultMakeSliceSize)
					if err = protobuf.UnmarshalJSONWithMessagesFromString(
						step.RateLimits.String, &stepRateLimits,
					); err != nil {
						return nil, errors.Wrapf(
							errorx.Err(errorx.SerializationError, err.Error()),
							"failed to unmarshal rate limits of perf case step, project_id: %s, plan_id: %s, case_id: %s, step: %s, rate limits: %s, error: %+v",
							perfCase.ProjectId, perfCase.PlanId, perfCase.CaseId, step.Name, step.RateLimits.String,
							err,
						)
					}

					v.RateLimits = stepRateLimits
				} else {
					// if there is no rate limits set in the steps, then use the rate limits of the perf case
					v.RateLimits = caseRateLimits
				}
			// serial and parallel steps use the rate limits of case in plan
			case string(common.ConstPerfCaseStepTypeSerial), string(common.ConstPerfCaseStepTypeParallel):
				v.RateLimits = caseRateLimits
			}

			if step.Headers.Valid && step.Headers.String != "" {
				headers := make(map[string]string)
				if err = jsonx.UnmarshalFromString(step.Headers.String, &headers); err != nil {
					return nil, errors.Wrapf(
						errorx.Err(errorx.SerializationError, err.Error()),
						"failed to unmarshal headers of perf case step, project_id: %s, plan_id: %s, case_id: %s, step: %s, headers: %s, error: %+v",
						perfCase.ProjectId, perfCase.PlanId, perfCase.CaseId, step.Name, step.Headers.String, err,
					)
				}

				v.Headers = headers
			}

			if step.Exports.Valid && step.Exports.String != "" {
				exports := make([]*commonpb.PerfCaseStepV2_Export, 0, constants.ConstDefaultMakeSliceSize)
				if err = protobuf.UnmarshalJSONWithMessagesFromString(step.Exports.String, &exports); err != nil {
					return nil, errors.Wrapf(
						errorx.Err(errorx.SerializationError, err.Error()),
						"failed to unmarshal exports of perf case step, project_id: %s, plan_id: %s, case_id: %s, step: %s, exports: %s, error: %+v",
						perfCase.ProjectId, perfCase.PlanId, perfCase.CaseId, step.Name, step.Exports.String, err,
					)
				}

				v.Exports = exports
			}

			switch step.Type {
			case string(common.ConstPerfCaseStepTypeSetup):
				content.SetupSteps = append(content.SetupSteps, v)
			case string(common.ConstPerfCaseStepTypeSerial):
				content.SerialSteps = append(content.SerialSteps, v)
			case string(common.ConstPerfCaseStepTypeParallel):
				content.ParallelSteps = append(content.ParallelSteps, v)
			case string(common.ConstPerfCaseStepTypeTeardown):
				content.TeardownSteps = append(content.TeardownSteps, v)
			default:
				l.Warnf("unknown perf case step type: %s", step.Type)
				continue
			}
		}
	}

	return content, nil
}

func (l *BaseLogic) getPerfServiceMetadata(
	metadata *pb.PerfPlanMetaData, caseData *pb.ApiExecutionData_PerfCase, methodCache map[string]*serviceMethodData,
	serviceCache map[string]*commonpb.PerfServiceMetaData, userCache map[string]*commonpb.PerfUserInfo,
) error {
	var (
		perfCase = caseData.PerfCase

		baseURL = l.getHttpBaseUrl(metadata.GetGeneralConfig().GetBaseUrl())
	)

	fn := func(step *commonpb.PerfCaseStepV2) (err error) {
		var (
			url_   = http.BuildURL(baseURL, step.GetUrl())
			method = step.GetMethod()
		)

		defer func() {
			if errors.Is(err, skipError) {
				err = nil
			}
		}()

		if http.IsHTTPMethod(method) {
			err = l.handleHttpUrl(url_, step, methodCache)
		} else {
			err = l.handleGrpcMethod(method, step, methodCache)
		}
		if err != nil {
			return err
		}

		if step.GetService() != "" && step.GetNamespace() != "" {
			return l.handleService(step.GetService(), step.GetNamespace(), step, serviceCache, userCache)
		}

		return nil
	}

	for _, steps := range [][]*commonpb.PerfCaseStepV2{
		perfCase.GetSetupSteps(),
		perfCase.GetSerialSteps(),
		perfCase.GetParallelSteps(),
		perfCase.GetTeardownSteps(),
	} {
		for _, step := range steps {
			if err := fn(step); err != nil {
				return err
			}
		}
	}

	return nil
}

func (l *BaseLogic) getHttpBaseUrl(baseUrl string) string {
	urls := strings.Split(baseUrl, constants.ConstAddressSeparator)
	for _, url_ := range urls {
		u, err := url.Parse(url_)
		if err != nil {
			return baseUrl
		}

		url__ := fmt.Sprintf("%s://%s", u.Scheme, u.Host)
		switch u.Scheme {
		case string(qetconstants.HTTP), string(qetconstants.HTTPS):
			return url__
		default:
			continue
		}
	}

	return baseUrl
}

func (l *BaseLogic) handleHttpUrl(
	url_ string, step *commonpb.PerfCaseStepV2, methodCache map[string]*serviceMethodData,
) error {
	name := step.GetName()

	data, ok := methodCache[url_]
	if !ok {
		u, err := url.Parse(url_)
		if err != nil {
			l.Errorf("failed to parse the url, step: %s, url: %s, error: %+v", name, url_, err)
			return skipError
		}

		path := u.Path
		if !strings.HasPrefix(path, "/") {
			path = "/" + path
		}

		paths := strings.Split(path, "/")
		if len(paths) < 3 {
			l.Warnf("the parts of url path cannot be less than 3, step: %s, url: %s, path: %s", name, url_, path)
			return nil
		}

		data = &serviceMethodData{
			Service:   paths[2],
			Namespace: paths[1],
		}
		methodCache[url_] = data
	}

	if step.GetService() == "" && data.Service != "" {
		step.Service = data.Service
	}
	if step.GetNamespace() == "" && data.Namespace != "" {
		step.Namespace = data.Namespace
	}

	return nil
}

func (l *BaseLogic) handleGrpcMethod(
	method string, step *commonpb.PerfCaseStepV2, methodCache map[string]*serviceMethodData,
) error {
	name := step.GetName()

	data, ok := methodCache[method]
	if !ok {
		out, err := l.svcCtx.RelationRPC.GetServiceByMethod(
			l.ctx, &relationpb.GetServiceByMethodReq{
				Method: method,
			},
		)
		if err != nil {
			l.Errorf("failed to find service by method, step: %s, method: %s, error: %+v", name, method, err)
			return skipError
		} else if len(out.GetRelations()) == 0 {
			l.Warnf("not found service by method, step: %s, method: %s", name, method)
			return nil
		} else if len(out.GetRelations()) > 1 {
			l.Warnf(
				"found multiple services by method, step: %s, method: %s, relations: %s",
				name, method, protobuf.MarshalJSONWithMessagesToStringIgnoreError(out.GetRelations()),
			)
		}

		relation := out.GetRelations()[0]
		data = &serviceMethodData{
			Service:    relation.GetService(),
			Namespace:  relation.GetNamespace(),
			Cmd:        relation.GetCmd(),
			GrpcPath:   relation.GetReqPath(),
			QueryPath:  relation.GetQueryPath(),
			Deprecated: relation.GetDeprecated(),
		}

		methodCache[method] = data
	}

	if step.GetService() == "" && data.Service != "" {
		step.Service = data.Service
	}
	if step.GetNamespace() == "" && data.Namespace != "" {
		step.Namespace = data.Namespace
	}
	if step.GetCmd() == 0 && data.Cmd != 0 {
		step.Cmd = data.Cmd
	}
	if step.GetGrpcPath() == "" && data.GrpcPath != "" {
		step.GrpcPath = data.GrpcPath
	}
	if step.GetQueryPath() == "" && data.QueryPath != "" {
		step.QueryPath = data.QueryPath
	}
	step.Deprecated = data.Deprecated

	return nil
}

func (l *BaseLogic) handleService(
	service, namespace string, step *commonpb.PerfCaseStepV2,
	serviceCache map[string]*commonpb.PerfServiceMetaData, userCache map[string]*commonpb.PerfUserInfo,
) error {
	name := step.GetName()

	if _, ok := serviceCache[service]; ok {
		return nil
	}

	serviceCache[service] = &commonpb.PerfServiceMetaData{
		Name:      service,
		Namespace: namespace,
	}

	resource, err := l.svcCtx.CMDBClient.GetResourceModuleInfo(cmdb.MakeReqOfGetApplicationInfo(service))
	if err != nil {
		l.Errorf("failed to call api of CMDB, step: %s, service: %s, error: %+v", name, service, err)
		return skipError
	}

	serviceCache[service].Developers = make(
		[]*commonpb.PerfUserInfo, 0, len(resource.DevelopUserList),
	)
	for _, developer := range resource.DevelopUserList {
		v, ok := userCache[developer.Email]
		if !ok {
			v = &commonpb.PerfUserInfo{
				Account:    developer.UserNO,
				Fullname:   developer.RealName,
				Email:      developer.Email,
				LarkUserId: "", // set it later
			}
			userCache[developer.Email] = v
		}
		serviceCache[service].Developers = append(serviceCache[service].Developers, v)
	}

	serviceCache[service].Maintainers = make(
		[]*commonpb.PerfUserInfo, 0, len(resource.OpsUserList),
	)
	for _, maintainer := range resource.OpsUserList {
		v, ok := userCache[maintainer.Email]
		if !ok {
			v = &commonpb.PerfUserInfo{
				Account:    maintainer.UserNO,
				Fullname:   maintainer.RealName,
				Email:      maintainer.Email,
				LarkUserId: "", // set it later
			}
			userCache[maintainer.Email] = v
		}
		serviceCache[service].Maintainers = append(serviceCache[service].Maintainers, v)
	}

	serviceCache[service].DatabaseAdministrators = make(
		[]*commonpb.PerfUserInfo, 0, len(resource.DBAUserList),
	)
	for _, DatabaseAdministrator := range resource.DBAUserList {
		v, ok := userCache[DatabaseAdministrator.Email]
		if !ok {
			v = &commonpb.PerfUserInfo{
				Account:    DatabaseAdministrator.UserNO,
				Fullname:   DatabaseAdministrator.RealName,
				Email:      DatabaseAdministrator.Email,
				LarkUserId: "", // set it later
			}
			userCache[DatabaseAdministrator.Email] = v
		}
		serviceCache[service].DatabaseAdministrators = append(serviceCache[service].DatabaseAdministrators, v)
	}

	return nil
}

func (l *BaseLogic) getLarkUserIDs(userCache map[string]*commonpb.PerfUserInfo) error {
	emails := make([]string, 0, len(userCache))
	for key := range userCache {
		emails = append(emails, key)
	}

	if len(emails) == 0 {
		return nil
	}

	out, err := l.svcCtx.LarkProxyRPC.GetBatchUserID(
		l.ctx, &larkproxypb.GetBatchUserIDReq{
			Emails: emails,
		},
	)
	if err != nil {
		return err
	}

	cache := make(map[string]*larkproxypb.UserContactInfo, len(out.GetItems()))
	for _, item := range out.GetItems() {
		if _, ok := cache[item.GetEmail()]; !ok {
			cache[item.GetEmail()] = item
		}
	}

	for key, val := range userCache {
		v, ok := cache[key]
		if !ok {
			continue
		}

		val.LarkUserId = v.GetUserId()
	}

	return nil
}

// getStabilityPlanExecutionData 获取稳测计划的执行数据
func (l *BaseLogic) getStabilityPlanExecutionData(projectID, planID string) (*pb.ApiExecutionData, error) {
	// validate the plan_id
	stabilityPlan, err := model.CheckStabilityPlanByPlanId(l.ctx, l.svcCtx.StabilityPlanModel, projectID, planID)
	if err != nil {
		return nil, err
	}

	// validate the state of stability plan
	if stabilityPlan.State != int64(pb.CommonState_CS_ENABLE) {
		return nil, errorx.Errorf(
			codes.PlanHasDisableFailure,
			"cannot to execute the stability plan[%s] whose the state is not enable",
			stabilityPlan.Name,
		)
	}

	planData := &pb.ApiExecutionData_StabilityPlan{StabilityPlan: &pb.StabilityPlanComponent{MetaData: &pb.StabilityPlanMetaData{}}}
	if err = qetutils.Copy(planData.StabilityPlan, stabilityPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy stability plan to api execution data, stability plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(stabilityPlan), err,
		)
	}
	if err = qetutils.Copy(planData.StabilityPlan.MetaData, stabilityPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy stability plan to stability plan meta data, stability plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(stabilityPlan), err,
		)
	}

	// generate the account configuration of stability plan
	if stabilityPlan.AccountConfigId.Valid && stabilityPlan.AccountConfigId.String != "" {
		if planData.StabilityPlan.MetaData.AccountConfig, err = l.generateAccountConfig(
			projectID, stabilityPlan.AccountConfigId.String,
		); err != nil {
			return nil, err
		}
	}

	notifies, err := l.listPlanNotify(projectID, planID)
	if err != nil {
		l.Errorf(
			"failed to list plan notify, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}

	planData.StabilityPlan.MetaData.LarkChats = make([]*commonpb.LarkChat, 0, len(notifies))
	for _, notify := range notifies {
		planData.StabilityPlan.MetaData.LarkChats = append(
			planData.StabilityPlan.MetaData.LarkChats,
			&commonpb.LarkChat{
				ChatId: notify.Receiver,
				Name:   notify.ReceiverName,
			},
		)
	}

	return &pb.ApiExecutionData{
		Id:   planID,
		Type: pb.ApiExecutionDataType_STABILITY_PLAN,
		Data: planData,
	}, nil
}

func (l *BaseLogic) listPlanNotify(projectID, planID string) ([]*model.Notify, error) {
	selectBuilder, _ := l.svcCtx.NotifyModel.SearchNotifySqlBuilder(
		model.SearchNotifyReq{
			ProjectId: projectID,
			PlanId:    planID,
		},
	)
	return l.svcCtx.NotifyModel.FindNotify(l.ctx, selectBuilder)
}

// getUIAgentComponentExecutionData 获取`UI Agent`组件的执行数据
func (l *BaseLogic) getUIAgentComponentExecutionData(projectID, componentID string) (*pb.ApiExecutionData, error) {
	component, err := model.CheckUIAgentComponentByComponentID(
		l.ctx, l.svcCtx.UIAgentComponentModel, projectID, componentID,
	)
	if err != nil {
		return nil, err
	}

	uiAgentComponentData := &pb.ApiExecutionData_UiAgentComponent{
		UiAgentComponent: &pb.UIAgentComponentComponent{
			Steps: []*commonpb.UIAgentComponentStep{},
		},
	}
	if err = qetutils.Copy(uiAgentComponentData.UiAgentComponent, component, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy ui agent component to api execution data, component: %s, error: %+v",
			jsonx.MarshalIgnoreError(component), err,
		)
	}

	// generate the application configuration of ui agent component
	uiAgentComponentData.UiAgentComponent.ApplicationConfig, err = l.generateApplicationConfig(
		projectID, component.ApplicationId,
	)
	if err != nil {
		return nil, err
	}

	var steps string
	if component.Mode == int64(commonpb.UIAgentMode_UIAgentMode_STEP) {
		steps = component.StepModeSteps
	} else {
		steps = component.AgentModeSteps
	}
	if err = protobuf.UnmarshalJSONWithMessagesFromString(
		steps, &uiAgentComponentData.UiAgentComponent.Steps,
	); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to unmarshal ui agent component steps, component: %s, error: %+v",
			jsonx.MarshalIgnoreError(component), err,
		)
	}

	return &pb.ApiExecutionData{
		Id:   componentID,
		Type: pb.ApiExecutionDataType_UI_AGENT_COMPONENT,
		Data: uiAgentComponentData,
	}, nil
}

func (l *BaseLogic) generateApplicationConfig(projectID, configID string) (*commonpb.ApplicationConfig, error) {
	config, err := model.CheckApplicationConfigurationByConfigID(
		l.ctx, l.svcCtx.ApplicationConfigModel, projectID, configID,
	)
	if err != nil {
		return nil, err
	}

	applicationConfig := &commonpb.ApplicationConfig{}
	if err = qetutils.Copy(applicationConfig, config, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy application configuration data, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(config), err,
		)
	}

	return applicationConfig, nil
}
