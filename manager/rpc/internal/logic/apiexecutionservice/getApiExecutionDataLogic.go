package apiexecutionservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetApiExecutionDataLogic struct {
	*BaseLogic
}

func NewGetApiExecutionDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetApiExecutionDataLogic {
	return &GetApiExecutionDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetApiExecutionData 获取API执行数据
func (l *GetApiExecutionDataLogic) GetApiExecutionData(in *pb.GetApiExecutionDataReq) (
	resp *pb.ApiExecutionData, err error,
) {
	var (
		projectID         = in.GetProjectId()
		executionDateType = in.GetType()
		typeID            = in.GetId()
		version           = in.GetVersion()
	)

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	switch executionDateType {
	case pb.ApiExecutionDataType_API_COMPONENT_GROUP: // API组件组
		return l.getComponentGroupExecutionData(projectID, typeID, version)
	case pb.ApiExecutionDataType_API_CASE: // API用例
		return l.getApiCaseExecutionData(projectID, typeID, version)
	case pb.ApiExecutionDataType_INTERFACE_CASE: // 接口用例
		return l.getInterfaceCaseExecutionData(projectID, typeID, version)
	case pb.ApiExecutionDataType_UI_PLAN: // UI计划
		return l.getUIPlanExecutionData(projectID, typeID, in.GetUiPlanExtraData())
	case pb.ApiExecutionDataType_PERF_PLAN: // 压测计划
		return l.getPerfPlanV2ExecutionData(projectID, typeID)
	case pb.ApiExecutionDataType_STABILITY_PLAN: // 稳测计划
		return l.getStabilityPlanExecutionData(projectID, typeID)
	case pb.ApiExecutionDataType_UI_AGENT_COMPONENT: // UI Agent组件
		return l.getUIAgentComponentExecutionData(projectID, typeID)
	}

	return nil, errorx.Errorf(
		codes.GenerateApiExecutionDataFailure,
		"the type of api execution data is not currently supported, project_id: %s, type: %s, type_id: %s",
		projectID, executionDateType, typeID,
	)
}
