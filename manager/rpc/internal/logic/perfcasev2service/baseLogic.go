package perfcasev2servicelogic

import (
	"context"
	"database/sql"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	tagservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/tagservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	createTagLogic *tagservicelogic.CreateTagLogic

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		createTagLogic: tagservicelogic.NewCreateTagLogic(ctx, svcCtx),

		converters: []qetutils.TypeConverter{
			commonpb.StringToProtocol(),
			logic.Int64ToCommonState(),
			logic.SqlNullStringToTags(),
			logic.StringToRateLimits(),
			logic.StringToPerfCaseSteps(),
			logic.SqlNullStringToPerfCaseSteps(),
		},
	}
}

func (l *BaseLogic) generatePerfCaseID(projectID string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenPerfCaseID), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.PerfCaseV2Model.FindOneByProjectIdCaseId(l.ctx, projectID, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	caseID := g.Next()
	if caseID == "" {
		return "", errors.WithStack(
			errorx.Err(
				errorx.GenerateUniqueIdFailure, "failed to generate perf case id, please try it later",
			),
		)
	}

	return caseID, nil
}

func (l *BaseLogic) getUserInfoByAccount(account string) (*userpb.UserInfo, error) {
	resp, err := l.svcCtx.UserRPC.ViewUser(
		l.ctx, &userpb.GetUserReq{
			Account: account,
		},
	)
	if err != nil {
		return nil, err
	}

	return resp.GetUserInfo(), nil
}

func (l *BaseLogic) createPerfCaseSteps(
	ctx context.Context, session sqlx.Session, perfCase *model.PerfCaseV2, caseRPS int64,
	stepType commonpb.PerfCaseStepType, steps []*commonpb.PerfCaseStepV2,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	if stepType == commonpb.PerfCaseStepType_PerfCaseStepType_NULL {
		return nil
	} else if len(steps) == 0 {
		return nil
	}

	return mr.MapReduceVoid[*model.PerfCaseStepV2, any](
		func(source chan<- *model.PerfCaseStepV2) {
			for i, step := range steps {
				var (
					rateLimits, headers, exports sql.NullString
					targetRPS                    int64
				)

				if len(step.GetRateLimits()) > 0 {
					rateLimits.String = protobuf.MarshalJSONWithMessagesToStringIgnoreError(step.GetRateLimits()) // []*RateLimitV2
					rateLimits.Valid = true

					targetRPS = utils.GetMaxRPSFromRateLimits(step.GetRateLimits())
				} else {
					targetRPS = caseRPS
				}
				if len(step.GetHeaders()) > 0 {
					headers.String = jsonx.MarshalToStringIgnoreError(step.GetHeaders()) // map[string]string
					headers.Valid = true
				}
				if len(step.GetExports()) > 0 {
					exports.String = protobuf.MarshalJSONWithMessagesToStringIgnoreError(step.GetExports()) // []*PerfCaseStepV2_Export
					exports.Valid = true
				}

				source <- &model.PerfCaseStepV2{
					ProjectId:  perfCase.ProjectId,
					CaseId:     perfCase.CaseId,
					StepId:     utils.GenPerfCaseStepID(),
					Type:       protobuf.GetEnumStringOf(stepType),
					Index:      int64(i),
					Name:       step.GetName(),
					RateLimits: rateLimits,
					Url:        step.GetUrl(),
					Method:     step.GetMethod(),
					Headers:    headers,
					Body:       step.GetBody(),
					Exports:    exports,
					Sleep:      step.GetSleep(),
					TargetRps:  targetRPS,
					State:      int64(qetconstants.EnableStatus),
					CreatedBy:  perfCase.CreatedBy,
					UpdatedBy:  perfCase.UpdatedBy,
					CreatedAt:  perfCase.CreatedAt,
					UpdatedAt:  perfCase.UpdatedAt,
				}
			}
		}, func(item *model.PerfCaseStepV2, writer mr.Writer[any], cancel func(error)) {
			if item == nil {
				return
			}

			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			if _, err = l.svcCtx.PerfCaseStepV2Model.Insert(ctx, session, item); err != nil {
				err = errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PerfCaseStepV2Model.Table(), jsonx.MarshalIgnoreError(item), err,
				)
				return
			}
		}, func(pipe <-chan any, cancel func(error)) {
		},
	)
}

func (l *BaseLogic) setReferenceQPS(
	ctx context.Context, projectId string, protocol commonpb.Protocol, items ...[]*commonpb.PerfCaseStepV2,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	if len(items) == 0 {
		return nil
	}

	switch protocol {
	case commonpb.Protocol_PROTOCOL_TT,
		commonpb.Protocol_PROTOCOL_TT_AUTH:
		return mr.MapReduceVoid[*commonpb.PerfCaseStepV2, any](
			func(source chan<- *commonpb.PerfCaseStepV2) {
				for _, steps := range items {
					for _, step := range steps {
						source <- step
					}
				}
			}, func(step *commonpb.PerfCaseStepV2, writer mr.Writer[any], cancel func(error)) {
				if step == nil {
					return
				}

				m, err := model.CheckInterfaceMetricsReferenceByMethod(
					ctx, l.svcCtx.InterfaceMetricsReferenceModel,
					projectId, string(common.ConstInterfaceMetricsReferenceProtocolTT), step.GetMethod(),
				)
				if err != nil {
					l.Logger.Error(err)
					return
				}

				if m.ReferenceQps.Valid {
					step.ReferenceQps = &[]int64{m.ReferenceQps.Int64}[0]
				}
			}, func(pipe <-chan any, cancel func(error)) {
			},
		)
	}

	return nil
}
