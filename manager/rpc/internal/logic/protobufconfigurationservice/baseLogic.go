package protobufconfigurationservicelogic

import (
	"context"

	dll "github.com/emirpasic/gods/lists/doublylinkedlist"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []qetutils.TypeConverter{
			logic.SqlNullStringToExcludePaths(),
			logic.SqlNullStringToExcludeFiles(),
		},
	}
}

func (l *BaseLogic) generateConfigID(projectID string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenProtobufConfigID), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.ProtobufConfigModel.FindOneByProjectIdConfigId(l.ctx, projectID, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	configID := g.Next()
	if configID == "" {
		return "", errorx.Err(
			errorx.GenerateUniqueIdFailure, "failed to generate protobuf config id, please try it later",
		)
	}

	return configID, nil
}

func (l *BaseLogic) checkCircularReference(projectID string, dependencies []string, stack *dll.List) error {
	if stack == nil {
		stack = dll.New()
	}

	for _, dependence := range dependencies {
		if dependence == "" {
			continue
		}

		if stack.Contains(dependence) {
			return errors.WithStack(
				errorx.Errorf(
					errorx.ProhibitedBehavior, "circular references are not allowed, stack: %s, dependence: %s",
					logic.ContainerToString(stack), dependence,
				),
			)
		}
		stack.Prepend(dependence)

		pds, err := l.svcCtx.ProtobufDependenceModel.FindByConfigID(l.ctx, projectID, dependence)
		if err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find protobuf dependence config, project_id: %s, config_id: %s, error: %+v",
				projectID, dependence, err,
			)
		}

		if len(pds) > 0 {
			deps := make([]string, 0, len(pds))
			for _, pd := range pds {
				deps = append(deps, pd.DepConfigId)
			}

			if err = l.checkCircularReference(projectID, deps, stack); err != nil {
				return err
			}
		}

		stack.Remove(0)
	}

	return nil
}

func (l *BaseLogic) getDependencies(projectID, configID string) (
	*hashmap.Map[string, *model.ProtobufDependence], *set.Set[string], error,
) {
	dependencies, err := l.svcCtx.ProtobufDependenceModel.FindByConfigID(l.ctx, projectID, configID)
	if err != nil {
		return nil, nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find protobuf dependence config, project_id: %s, config_id: %s, error: %+v",
			projectID, configID, err,
		)
	}

	m := hashmap.New[string, *model.ProtobufDependence](
		uint64(len(dependencies)), generic.Equals[string], generic.HashString,
	)
	s := set.NewHashset(uint64(len(dependencies)), generic.Equals[string], generic.HashString)
	for _, dep := range dependencies {
		if dep.DepConfigId == "" {
			continue
		}

		m.Put(dep.DepConfigId, dep)
		s.Put(dep.DepConfigId)
	}

	return m, &s, nil
}
