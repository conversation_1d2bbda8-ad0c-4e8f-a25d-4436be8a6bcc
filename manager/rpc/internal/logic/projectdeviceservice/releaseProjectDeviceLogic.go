package projectdeviceservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	permissionpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/permission/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ReleaseProjectDeviceLogic struct {
	*BaseLogic
}

func NewReleaseProjectDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReleaseProjectDeviceLogic {
	return &ReleaseProjectDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ReleaseProjectDevice 释放项目设备
func (l *ReleaseProjectDeviceLogic) ReleaseProjectDevice(in *pb.ReleaseProjectDeviceReq) (
	out *pb.ReleaseProjectDeviceResp, err error,
) {
	var (
		projectID = in.GetProjectId()
		usage     = in.GetUsage()
		udid      = in.GetUdid()
		token     = in.GetToken()
	)

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	// NOTE: when releasing the device, there is no need to check
	// whether the device is still bound to the current project,
	// as it may have been unbound from the project during the process of occupying the device
	if _, err = l.checkDeviceUsage(projectID, usage, udid); err != nil {
		l.Warn(err)
	}

	if token == "" {
		// NOTE: only platform administrators or project administrators can release devices without a token
		if err = l.checkUserRole(projectID); err != nil {
			return nil, err
		}

		device, e := l.getDevice(udid)
		if e != nil {
			return nil, e
		}

		token = device.GetToken()
	}

	if _, err = l.svcCtx.DeviceHubRPC.ReleaseDevice(
		l.ctx, &devicehubpb.ReleaseDeviceReq{
			Udid:  udid,
			Token: token,
		},
	); err != nil {
		return nil, err
	}

	return &pb.ReleaseProjectDeviceResp{}, nil
}

func (l *ReleaseProjectDeviceLogic) checkUserRole(projectID string) error {
	out, err := l.svcCtx.PermissionRoleRPC.GetUserAllProjectRoles(
		l.ctx, &permissionpb.UserViewAllReq{
			UserId:       l.currentUser.Account,
			DomainSource: constants.DomainSourceOfProbe,
		},
	)
	if err != nil {
		return err
	}

	for _, role := range out.GetRoles() {
		if role == nil {
			continue
		}

		switch role.GetDomainType() {
		case constants.DomainTypeOfPlatform:
			if role.GetRoleType() == permissionpb.RoleType_Admin {
				return nil
			}
		case constants.DomainTypeOfProject:
			if role.GetDomainId() == projectID && role.GetRoleType() == permissionpb.RoleType_Admin {
				return nil
			}
		}
	}

	return errorx.Errorf(
		errorx.ProhibitedBehavior,
		"current user is not platform administrator or project administrator, account: %s",
		l.currentUser.Account,
	)
}
