package projectdeviceservicelogic

import (
	"context"

	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type AcquireProjectDeviceByUDIDLogic struct {
	*BaseLogic
}

func NewAcquireProjectDeviceByUDIDLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *AcquireProjectDeviceByUDIDLogic {
	return &AcquireProjectDeviceByUDIDLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// AcquireProjectDeviceByUDID 占用项目设备（通过`udid`）
func (l *AcquireProjectDeviceByUDIDLogic) AcquireProjectDeviceByUDID(in *pb.AcquireProjectDeviceByUDIDReq) (
	out *pb.AcquireProjectDeviceByUDIDResp, err error,
) {
	var (
		projectID  = in.GetProjectId()
		usage      = in.GetUsage()
		udid       = in.GetUdid()
		expiration = in.GetExpiration()
	)

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	// validate the udid and the usage in req
	if _, err = l.checkDeviceUsage(projectID, usage, udid); err != nil {
		return nil, err
	}

	resp, err := l.svcCtx.DeviceHubRPC.AcquireDeviceByUDID(
		l.ctx, &devicehubpb.AcquireDeviceReq{
			Udid:       udid,
			Expiration: expiration,
		},
	)
	if err != nil {
		return nil, err
	}

	return &pb.AcquireProjectDeviceByUDIDResp{
		Device: &pb.ProjectDevice{
			Device: resp.GetDevice(),

			ProjectId: projectID,
			Usage:     usage,
		},
	}, nil
}
