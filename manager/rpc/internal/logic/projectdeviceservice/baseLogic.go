package projectdeviceservicelogic

import (
	"context"
	"strconv"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []qetutils.TypeConverter{},
	}
}

func (l *BaseLogic) checkDeviceUsage(projectID string, usage commonpb.DeviceUsage, udid string) (
	*model.ProjectDevice, error, //nolint: unparam
) {
	projectDevice, err := model.CheckProjectDeviceByUDID(l.ctx, l.svcCtx.ProjectDeviceModel, projectID, udid)
	if err != nil {
		return nil, err
	} else {
		projectDeviceUsage := commonpb.DeviceUsage(projectDevice.Usage)
		if projectDeviceUsage != usage {
			return nil, errorx.Errorf(
				errorx.NotExists,
				"the usage of project device is not match, project_id: %s, udid: %s, expected: %s, but got %s",
				projectID, udid, protobuf.GetEnumStringOf(usage), protobuf.GetEnumStringOf(projectDeviceUsage),
			)
		}
	}

	return projectDevice, nil
}

func (l *BaseLogic) getAllDevices() (
	*set.Set[string], *hashmap.Map[string, *devicehubpb.Device], error, //nolint: unparam
) {
	out, err := l.svcCtx.DeviceHubRPC.SearchDevice(l.ctx, &devicehubpb.SearchDeviceReq{})
	if err != nil {
		return nil, nil, err
	}

	capacity := uint64(len(out.GetItems()))
	s := set.NewHashset(capacity, generic.Equals[string], generic.HashString)
	m := hashmap.New[string, *devicehubpb.Device](capacity, generic.Equals[string], generic.HashString)
	for _, item := range out.GetItems() {
		udid := item.GetUdid()
		if udid == "" {
			continue
		}

		if _, ok := m.Get(udid); ok {
			continue
		}

		s.Put(udid)
		m.Put(udid, item)
	}

	return &s, m, nil
}

func (l *BaseLogic) getProjectDevices(projectID string, usage commonpb.DeviceUsage) (
	*set.Set[string], *hashmap.Map[string, *model.ProjectDevice], error,
) {
	var (
		records []*model.ProjectDevice
		err     error
	)

	if usage != commonpb.DeviceUsage_DU_NULL {
		records, err = l.svcCtx.ProjectDeviceModel.FindAllBySearchReq(
			l.ctx, model.SearchProjectDeviceReq{
				BaseSearchReq: model.BaseSearchReq{
					ProjectID: projectID,
					Condition: &rpc.Condition{
						Single: &rpc.SingleCondition{
							Field:   string(common.DeviceFieldOfUsage),
							Compare: constants.EQ,
							Other: &rpc.Other{
								Value: strconv.Itoa(int(usage)),
							},
						},
					},
				},
			},
		)
	} else {
		records, err = l.svcCtx.ProjectDeviceModel.FindAll(l.ctx, projectID)
	}
	if err != nil {
		return nil, nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find project device, project_id: %s, usage: %s, error: %+v",
			projectID, usage, err,
		)
	}

	capacity := uint64(len(records))
	s := set.NewHashset(capacity, generic.Equals[string], generic.HashString)
	m := hashmap.New[string, *model.ProjectDevice](capacity, generic.Equals[string], generic.HashString)
	for _, record := range records {
		if record == nil || record.Id == 0 || record.Udid == "" {
			continue
		}

		s.Put(record.Udid)
		m.Put(record.Udid, record)
	}

	return &s, m, nil
}

func (l *BaseLogic) getProjectDeviceRelationship(projectID string) (*hashmap.Map[string, int64], error) {
	records, err := l.svcCtx.ProjectDeviceReferenceModel.FindByProjectID(l.ctx, projectID)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find project device relationship, project_id: %s, error: %+v",
			projectID, err,
		)
	}

	cache := hashmap.New[string, int64](uint64(len(records)), generic.Equals[string], generic.HashString)
	for _, record := range records {
		if record == nil || record.Udid == "" {
			continue
		}

		v, ok := cache.Get(record.Udid)
		if ok {
			cache.Put(record.Udid, v+1)
		} else {
			cache.Put(record.Udid, 1)
		}
	}

	return cache, nil
}

func (l *BaseLogic) getDevice(udid string) (*devicehubpb.Device, error) {
	out, err := l.svcCtx.DeviceHubRPC.GetDevice(l.ctx, &devicehubpb.GetDeviceReq{Udid: udid})
	if err != nil {
		return nil, err
	}

	return out.GetDevice(), nil
}
