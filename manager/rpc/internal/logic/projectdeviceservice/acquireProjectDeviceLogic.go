package projectdeviceservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	devicehubcommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// Deprecated: use `AcquireProjectDeviceByConditionLogic` instead.
type AcquireProjectDeviceLogic struct {
	*BaseLogic
}

func NewAcquireProjectDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AcquireProjectDeviceLogic {
	return &AcquireProjectDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// AcquireProjectDevice 获取项目设备
// Deprecated: use `AcquireProjectDeviceByConditionLogic.AcquireProjectDeviceByCondition` instead.
func (l *AcquireProjectDeviceLogic) AcquireProjectDevice(in *pb.AcquireProjectDeviceReq) (
	out *pb.AcquireProjectDeviceResp, err error,
) {
	var (
		projectID  = in.GetProjectId()
		usage      = in.GetUsage()
		count      = in.GetCount()
		expiration = in.GetExpiration()
	)

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	ids, _, err := l.getProjectDevices(projectID, usage)
	if err != nil {
		return nil, err
	} else if ids.Size() < int(count) {
		return nil, errorx.Errorf(
			codes.InsufficientProjectDevicesError,
			"not enough project devices that meet the requirements, expected: %d, but got %d",
			count, ids.Size(),
		)
	}

	devices, err := l.svcCtx.DeviceHubRPC.AcquireDeviceByCondition(
		l.ctx, &devicehubpb.SearchAcquireDeviceReq{
			Condition: &rpc.Condition{
				Group: &rpc.GroupCondition{
					Relationship: constants.AND,
					Conditions: []*rpc.Condition{
						{
							Single: &rpc.SingleCondition{
								Field:   string(devicehubcommon.DeviceFieldOfUDID),
								Compare: constants.DBIn,
								In:      ids.Keys(),
							},
						},
						in.GetCondition(),
					},
				},
			},
			Count:      count,
			Expiration: expiration,
		},
	)
	if err != nil {
		return nil, err
	}

	out = &pb.AcquireProjectDeviceResp{Devices: make([]*pb.ProjectDevice, 0, len(devices.GetDevices()))}
	for _, device := range devices.GetDevices() {
		out.Devices = append(
			out.Devices, &pb.ProjectDevice{
				Device: device,

				ProjectId: projectID,
				Usage:     usage,
			},
		)
	}

	return out, nil
}
