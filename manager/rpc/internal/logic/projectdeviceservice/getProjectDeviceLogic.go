package projectdeviceservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type GetProjectDeviceLogic struct {
	*BaseLogic
}

func NewGetProjectDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetProjectDeviceLogic {
	return &GetProjectDeviceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetProjectDevice 获取项目设备
func (l *GetProjectDeviceLogic) GetProjectDevice(in *pb.GetProjectDeviceReq) (out *pb.GetProjectDeviceResp, err error) {
	var (
		projectID = in.GetProjectId()
		usage     = in.GetUsage()
		udid      = in.GetUdid()
	)

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	// validate the udid and the usage in req
	if _, err = l.checkDeviceUsage(projectID, usage, udid); err != nil {
		return nil, err
	}

	device, err := l.getDevice(udid)
	if err != nil {
		return nil, err
	}

	return &pb.GetProjectDeviceResp{
		Device: &pb.ProjectDevice{
			Device: device,

			ProjectId: projectID,
			Usage:     usage,
		},
	}, nil
}
