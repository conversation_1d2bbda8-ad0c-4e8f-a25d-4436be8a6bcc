package uiagentcomponentservicelogic

import (
	"context"
	"fmt"
	"path/filepath"
	"regexp"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	tagservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic/tagservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
)

var (
	// 正则表达式匹配`{{.xxx}}`格式的变量
	variablePattern = regexp.MustCompile(`\{\{\s*\.([^}]+)\s*\}\}`)

	// `Go`语言标识符规则：以字母或下划线开头，后续可以是字母、数字或下划线
	identifierPattern = regexp.MustCompile(`^[a-zA-Z_][a-zA-Z0-9_]*$`)

	// `Go`语言关键字列表
	goKeywords = map[string]lang.PlaceholderType{
		"break":       lang.Placeholder,
		"case":        lang.Placeholder,
		"chan":        lang.Placeholder,
		"const":       lang.Placeholder,
		"continue":    lang.Placeholder,
		"default":     lang.Placeholder,
		"defer":       lang.Placeholder,
		"else":        lang.Placeholder,
		"fallthrough": lang.Placeholder,
		"for":         lang.Placeholder,
		"func":        lang.Placeholder,
		"go":          lang.Placeholder,
		"goto":        lang.Placeholder,
		"if":          lang.Placeholder,
		"import":      lang.Placeholder,
		"interface":   lang.Placeholder,
		"map":         lang.Placeholder,
		"package":     lang.Placeholder,
		"range":       lang.Placeholder,
		"return":      lang.Placeholder,
		"select":      lang.Placeholder,
		"struct":      lang.Placeholder,
		"switch":      lang.Placeholder,
		"type":        lang.Placeholder,
		"var":         lang.Placeholder,
	}
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	createTagLogic *tagservicelogic.CreateTagLogic

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		createTagLogic: tagservicelogic.NewCreateTagLogic(ctx, svcCtx),

		converters: []qetutils.TypeConverter{
			logic.Int64ToCommonState(),                         // state
			logic.SqlNullStringToTags(),                        // tags
			logic.StringToUIAgentComponentSteps(),              // steps
			logic.SqlNullStringToUIAgentComponentExpectation(), // expectation
			logic.StringToVariables(),                          // variables
		},
	}
}

func (l *BaseLogic) Context() context.Context {
	return l.ctx
}

func (l *BaseLogic) ServiceContext() *svc.ServiceContext {
	return l.svcCtx
}

func (l *BaseLogic) generateComponentID(projectID string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenUIAgentComponentID), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.UIAgentComponentModel.FindOneByProjectIdComponentId(l.ctx, projectID, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	componentID := g.Next()
	if componentID == "" {
		return "", errorx.Err(
			errorx.GenerateUniqueIdFailure, "failed to generate ui agent component id, please try it later",
		)
	}

	return componentID, nil
}

// generateLockKey 生成分布式锁的key
func (l *BaseLogic) generateLockKey(projectID, componentID string) string {
	return fmt.Sprintf("%s:%s:%s", common.ConstLockUIAgentComponentProjectIDComponentIDPrefix, projectID, componentID)
}

func (l *BaseLogic) getImagesFromSteps(projectID string, steps []*commonpb.UIAgentComponentStep) (
	[]*model.UiAgentImage, error,
) {
	images := make([]*model.UiAgentImage, 0, len(steps))
	if err := mr.MapReduceVoid[string, *model.UiAgentImage](
		func(source chan<- string) {
			cache := make(map[string]lang.PlaceholderType, len(steps))
			for _, step := range steps {
				imageID := step.GetExpectation().GetImage()
				if len(imageID) == 0 {
					continue
				}

				if _, ok := cache[imageID]; ok {
					continue
				}
				cache[imageID] = lang.Placeholder

				source <- imageID
			}
		},
		func(item string, writer mr.Writer[*model.UiAgentImage], cancel func(error)) {
			var (
				image *model.UiAgentImage
				err   error
			)
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			if image, err = l.getImageByImageID(projectID, item); err != nil {
				return
			}

			writer.Write(image)
		},
		func(pipe <-chan *model.UiAgentImage, cancel func(error)) {
			for item := range pipe {
				if item == nil {
					continue
				}

				images = append(images, item)
			}
		},
		mr.WithContext(l.ctx),
	); err != nil {
		return nil, err
	}

	return images, nil
}

func (l *BaseLogic) getImageByImageID(projectID, imageID string) (*model.UiAgentImage, error) {
	image, err := model.CheckUIAgentImageByImageID(l.ctx, l.svcCtx.UIAgentImageModel, projectID, imageID)
	if err != nil {
		return nil, err
	}

	if !l.checkImageIsExist(projectID, image.ImageId) {
		return nil, errorx.Errorf(
			errorx.NotExists,
			"image not found in file system, project_id: %s, image_id: %s, name: %s",
			projectID, image.ImageId, image.Name,
		)
	}

	return image, nil
}

func (l *BaseLogic) checkImageIsExist(projectID, imageID string) bool {
	if len(l.svcCtx.Config.PVCPath) == 0 {
		return false
	}

	// image path: /app/data/files/ui_agent_test/${project_id}/${image_id}
	filePath := filepath.Join(
		l.svcCtx.Config.PVCPath,
		constants.ConstStoragePathOfFiles,
		constants.ConstStoragePathOfUIAgentTest,
		projectID,
		imageID,
	)
	return qetutils.Exists(filePath)
}

func (l *BaseLogic) getUserInfoByAccount(account string) (*userpb.UserInfo, error) {
	resp, err := l.svcCtx.UserRPC.ViewUser(
		l.ctx, &userpb.GetUserReq{
			Account: account,
		},
	)
	if err != nil {
		return nil, err
	}

	return resp.GetUserInfo(), nil
}

func (l *BaseLogic) updateApplicationConfigRelationship(
	ctx context.Context, session sqlx.Session, component *model.UiAgentComponent, applicationID string,
) error {
	var (
		projectID   = component.ProjectId
		componentID = component.ComponentId

		now = time.Now()
	)

	if ctx == nil {
		ctx = l.ctx
	}

	rs, err := l.svcCtx.ApplicationConfigReferenceModel.FindByReference(
		ctx, projectID, common.ConstReferenceTypeUIAgentComponent, componentID,
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find application configuration relationship, project_id: %s, component_id: %s, error: %+v",
			projectID, componentID, err,
		)
	}

	if applicationID == "" && len(rs) == 0 {
		return nil
	}

	from := make(map[string]*model.ApplicationConfigurationReferenceRelationship, len(rs))
	for _, r := range rs {
		if r == nil {
			continue
		}

		from[r.ConfigId] = r
	}

	if _, ok := from[applicationID]; !ok {
		item := &model.ApplicationConfigurationReferenceRelationship{
			ProjectId:     projectID,
			ReferenceType: common.ConstReferenceTypeUIAgentComponent,
			ReferenceId:   componentID,
			ConfigId:      applicationID,
			CreatedBy:     l.currentUser.Account,
			UpdatedBy:     l.currentUser.Account,
			CreatedAt:     now,
			UpdatedAt:     now,
		}
		if _, err = l.svcCtx.ApplicationConfigReferenceModel.Insert(ctx, session, item); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to insert values to table, table: %s, values: %s, error: %+v",
				l.svcCtx.ApplicationConfigReferenceModel.Table(), jsonx.MarshalIgnoreError(item), err,
			)
		}
	}

	for key, val := range from {
		if key == applicationID {
			continue
		}

		if err = l.svcCtx.ApplicationConfigReferenceModel.Delete(ctx, session, val.Id); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to delete item from table, table: %s, item: %s, error: %+v",
				l.svcCtx.ApplicationConfigReferenceModel.Table(), jsonx.MarshalIgnoreError(val), err,
			)
		}
	}

	return nil
}

func (l *BaseLogic) updateImageRelationship(
	ctx context.Context, session sqlx.Session, component *model.UiAgentComponent, images []*model.UiAgentImage,
) error {
	var (
		projectID   = component.ProjectId
		componentID = component.ComponentId

		now = time.Now()
	)

	if ctx == nil {
		ctx = l.ctx
	}

	rs, err := l.svcCtx.UIAgentImageReferenceModel.FindByReference(
		ctx, projectID, common.ConstReferenceTypeUIAgentComponent, componentID,
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find ui agent image reference relationship, project_id: %s, component_id: %s, error: %+v",
			projectID, componentID, err,
		)
	}

	if len(images) == 0 && len(rs) == 0 {
		return nil
	}

	from := make(map[string]*model.UiAgentImageReferenceRelationship, len(rs))
	for _, r := range rs {
		if r == nil {
			continue
		}

		from[r.ImageId] = r
	}

	to := make(map[string]*model.UiAgentImage, len(images))
	for _, image := range images {
		if image == nil {
			continue
		}

		to[image.ImageId] = image

		if _, ok := from[image.ImageId]; !ok {
			item := &model.UiAgentImageReferenceRelationship{
				ProjectId:     projectID,
				ReferenceType: common.ConstReferenceTypeUIAgentComponent,
				ReferenceId:   componentID,
				ImageId:       image.ImageId,
				CreatedBy:     l.currentUser.Account,
				UpdatedBy:     l.currentUser.Account,
				CreatedAt:     now,
				UpdatedAt:     now,
			}
			if _, err = l.svcCtx.UIAgentImageReferenceModel.Insert(ctx, session, item); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.UIAgentImageReferenceModel.Table(), jsonx.MarshalIgnoreError(item), err,
				)
			}
		}
	}

	for _, item := range from {
		if item == nil {
			continue
		}

		if _, ok := to[item.ImageId]; !ok {
			if err = l.svcCtx.UIAgentImageReferenceModel.Delete(ctx, session, item.Id); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to delete item from table, table: %s, item: %s, error: %+v",
					l.svcCtx.UIAgentImageReferenceModel.Table(), jsonx.MarshalIgnoreError(item), err,
				)
			}
		}
	}

	return nil
}

func getVariablesFromSteps(steps []*commonpb.UIAgentComponentStep) (*set.Set[string], error) {
	variables := set.NewHashset[string](
		uint64(constants.ConstDefaultMakeSliceSize), generic.Equals[string], generic.HashString,
	)

	for _, step := range steps {
		if step == nil {
			continue
		}

		// 从步骤内容中提取变量
		if content := step.GetContent(); content != "" {
			vars, err := getVariablesFromContent(content)
			if err != nil {
				return nil, err
			}

			variables.InPlaceUnion(vars)
		}

		// 从期望结果的文本中提取变量
		if expectation := step.GetExpectation(); expectation != nil {
			if text := expectation.GetText(); text != "" {
				vars, err := getVariablesFromContent(text)
				if err != nil {
					return nil, err
				}

				variables.InPlaceUnion(vars)
			}
		}
	}

	return &variables, nil
}

func getVariablesFromPairs(pairs []*commonpb.GeneralConfigVar) (*set.Set[string], error) {
	variables := set.NewHashset[string](uint64(len(pairs)), generic.Equals[string], generic.HashString)
	for _, pair := range pairs {
		if pair == nil || len(pair.GetKey()) == 0 {
			continue
		}

		key := pair.GetKey()

		// 验证变量名是否符合`Go`语言标识符规则
		if !identifierPattern.MatchString(key) {
			return nil, fmt.Errorf("does not match Go identifier rules, variable_name: %s", key)
		}
		variables.Put(key)
	}

	return &variables, nil
}

func getVariablesFromContent(content string) (*set.Set[string], error) {
	variables := set.NewHashset[string](
		uint64(constants.ConstDefaultMakeSliceSize), generic.Equals[string], generic.HashString,
	)
	matches := variablePattern.FindAllStringSubmatch(content, -1)
	for _, match := range matches {
		if len(match) > 1 {
			varName := match[1]

			// 验证变量名是否符合`Go`语言标识符规则
			if !identifierPattern.MatchString(varName) {
				return nil, fmt.Errorf(
					"does not match Go identifier rules, content: %s, variable_name: %s",
					content, varName,
				)
			}

			// 检查是否为`Go`语言关键字
			//if _, ok := goKeywords[varName]; ok {
			//	return nil, fmt.Errorf(
			//		"cannot use Go language keyword as variable name, content: %s, variable_name: %s",
			//		content, varName,
			//	)
			//}

			variables.Put(varName)
		}
	}

	return &variables, nil
}
