package uiagentcomponentservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewUIAgentComponentLogic struct {
	*BaseLogic
}

func NewViewUIAgentComponentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewUIAgentComponentLogic {
	return &ViewUIAgentComponentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewUIAgentComponent 查看UIAgent组件
func (l *ViewUIAgentComponentLogic) ViewUIAgentComponent(in *pb.ViewUIAgentComponentReq) (
	out *pb.ViewUIAgentComponentResp, err error,
) {
	var (
		projectID   = in.GetProjectId()
		componentID = in.GetComponentId()
	)

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	component, err := model.CheckUIAgentComponentByComponentID(
		l.ctx, l.svcCtx.UIAgentComponentModel, projectID, componentID,
	)
	if err != nil {
		return nil, err
	}

	out = &pb.ViewUIAgentComponentResp{Component: &pb.UIAgentComponent{}}
	if err = utils.Copy(out.Component, component, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy ui agent component to response, component: %s, error: %+v",
			jsonx.MarshalIgnoreError(component), err,
		)
	}

	return out, nil
}
