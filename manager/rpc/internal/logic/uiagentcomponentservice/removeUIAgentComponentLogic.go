package uiagentcomponentservicelogic

import (
	"context"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveUIAgentComponentLogic struct {
	*BaseLogic
}

func NewRemoveUIAgentComponentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveUIAgentComponentLogic {
	return &RemoveUIAgentComponentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveUIAgentComponent 删除UIAgent组件
func (l *RemoveUIAgentComponentLogic) RemoveUIAgentComponent(in *pb.RemoveUIAgentComponentReq) (
	out *pb.RemoveUIAgentComponentResp, err error,
) {
	projectID := in.GetProjectId()

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	componentIDs := in.GetComponentIds()
	workers := len(componentIDs)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	mr.ForEach[string](
		func(source chan<- string) {
			for _, componentID := range componentIDs {
				source <- componentID
			}
		}, func(item string) {
			if e := l.remove(projectID, item); e != nil {
				err = multierror.Append(err, e)
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemoveUIAgentComponentResp{}, nil
}

func (l *RemoveUIAgentComponentLogic) remove(projectID, componentID string) error {
	// validate the component_id in req
	component, err := model.CheckUIAgentComponentByComponentID(
		l.ctx, l.svcCtx.UIAgentComponentModel, projectID, componentID,
	)
	if err != nil {
		if re, ok := errorx.RootError(err); ok && re.Code() == errorx.NotExists {
			return nil
		}

		return err
	}

	// TODO: check if the ui agent component is referenced by other resources

	key := l.generateLockKey(projectID, componentID)
	fn := func() error {
		// remove ui agent components in a transaction
		return l.svcCtx.UIAgentComponentModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				// Table: application_configuration_reference_relationship
				if _, err := l.svcCtx.ApplicationConfigReferenceModel.RemoveByReference(
					context, session, projectID, common.ConstReferenceTypeUIAgentComponent, componentID,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove application configuration reference relationship, project_id: %s, reference_type: %s, reference_id: %s, error: %+v",
						projectID, common.ConstReferenceTypeUIAgentComponent, componentID, err,
					)
				}

				// Table: ui_agent_image_reference_relationship
				if _, err := l.svcCtx.UIAgentImageReferenceModel.RemoveByReference(
					context, session, projectID, common.ConstReferenceTypeUIAgentComponent, componentID,
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove ui agent image reference relationship, project_id: %s, reference_type: %s, reference_id: %s, error: %+v",
						projectID, common.ConstReferenceTypeUIAgentComponent, componentID, err,
					)
				}

				// Table: tag_reference_relationship
				if _, err := l.svcCtx.TagReferenceModel.RemoveByReferenceId(
					context, session, projectID, common.ConstReferenceTypeUIAgentComponent, componentID, "",
				); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove tag reference relationship, project_id: %s, reference_type: %s, reference_id: %s, error: %+v",
						projectID, common.ConstReferenceTypeUIAgentComponent, componentID, err,
					)
				}

				// Table: ui_agent_component
				if err := l.svcCtx.UIAgentComponentModel.LogicDelete(context, session, component.Id); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to remove ui agent component, project_id: %s, component_id: %s, error: %+v",
						projectID, componentID, err,
					)
				}

				return nil
			},
		)
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}
