package uiagentcomponentservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type UpdateUIAgentComponentResultLogic struct {
	*BaseLogic
}

func NewUpdateUIAgentComponentResultLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *UpdateUIAgentComponentResultLogic {
	return &UpdateUIAgentComponentResultLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// UpdateUIAgentComponentResult 更新`UI Agent`组件的执行结果
func (l *UpdateUIAgentComponentResultLogic) UpdateUIAgentComponentResult(in *pb.UpdateUIAgentComponentResultReq) (
	out *pb.UpdateUIAgentComponentResultResp, err error,
) {
	var (
		projectID   = in.GetProjectId()
		componentID = in.GetComponentId()
		result      = in.GetResult()
	)

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	// validate the component_id in req
	component, err := model.CheckUIAgentComponentByComponentID(
		l.ctx, l.svcCtx.UIAgentComponentModel, projectID, componentID,
	)
	if err != nil {
		return nil, err
	}

	if result != commonpb.ExecutedResult_TER_INIT {
		key := fmt.Sprintf("%s:%s:%s", common.ConstLockUIAgentComponentProjectIDComponentIDPrefix, projectID, componentID)
		fn := func() error {
			component, err = model.CheckUIAgentComponentByComponentID(
				l.ctx, l.svcCtx.UIAgentComponentModel, projectID, componentID,
			)
			if err != nil {
				return err
			}

			fromResult := component.LatestResult
			toResult := int64(result)

			executedAt := time.UnixMilli(in.GetExecutedAt())
			if executedAt.IsZero() {
				executedAt = time.Now()
			}

			component.LatestResult = toResult
			component.LatestExecutedAt = sql.NullTime{
				Time:  executedAt,
				Valid: true,
			}

			if _, err = l.svcCtx.UIAgentComponentModel.Update(l.ctx, nil, component); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed toResult update ui agent component result, project_id: %s, component_id: %s, result: %d => %d, executed_at: %s",
					projectID, componentID, fromResult, toResult, executedAt.Format(time.DateTime),
				)
			}

			return nil
		}
		if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
			return nil, err
		}
	}

	return &pb.UpdateUIAgentComponentResultResp{}, nil
}
