package casecommonservicelogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []commonutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []commonutils.TypeConverter{
			logic.SqlNullStringToTags(),
			logic.StringToAccountConfig(),
		},
	}
}

func (l *BaseLogic) DeleteFailLogCaseHandler(
	ctx context.Context, session sqlx.Session, projectId, caseId, caseType string,
) error {
	if _, err := l.svcCtx.CaseFailStatModel.LogicDeleteByProjectIdCaseIdCaseType(
		ctx, session, projectId, caseId, caseType,
	); err != nil {
		return err
	}

	// 删除reporter数据
	if _, err := l.svcCtx.ReporterRPC.DelCaseFailStatForPlan(
		ctx, &reporter.DelCaseFailStatForPlanReq{
			ProjectId: projectId,
			CaseId:    caseId,
			CaseType:  caseType,
		},
	); err != nil {
		return err
	}

	return nil
}
