package casecommonservicelogic

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

const lockTimeout = 5 * time.Second

type CreateOrModifyFailedCaseLogic struct {
	*BaseLogic
}

func NewCreateOrModifyFailedCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateOrModifyFailedCaseLogic {
	return &CreateOrModifyFailedCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateOrModifyFailedCase 创建或修改失败用例记录
func (l *CreateOrModifyFailedCaseLogic) CreateOrModifyFailedCase(in *pb.CreateOrModifyFailedCaseReq) (
	out *pb.CreateOrModifyFailedCaseResp, err error,
) {
	var (
		projectID = in.GetProjectId()
		caseType  = in.GetCaseType()
		caseID    = in.GetCaseId()

		user = userinfo.System()
	)

	resp, err := l.svcCtx.ReporterRPC.CountFailedCaseInLastNDays(
		l.ctx, &reporterpb.CountFailedCaseInLastNDaysReq{
			ProjectId: projectID,
			CaseType:  caseType,
			CaseId:    caseID,
			Days:      common.ConstCaseFailStatNDays,
		},
	)
	if err != nil {
		return nil, err
	}

	var (
		count     = resp.GetCount()
		updatedAt = time.UnixMilli(resp.GetLastUpdatedAt())
	)

	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockCaseFailStatProjectIDCaseTypeCaseIDPrefix, projectID, caseType, caseID,
	)
	fn := func() error {
		cfs, err := l.svcCtx.CaseFailStatModel.FindNoCacheByProjectIdCaseIdCaseType(l.ctx, projectID, caseID, caseType)
		if err != nil && !errors.Is(err, model.ErrNotFound) {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find failed case records, project_id: %s, case_id: %s, case_type: %s, error: %+v",
				projectID, caseID, caseType, err,
			)
		} else if cfs == nil && count == 0 {
			// the current case has no record of execution failures
			return nil
		}

		if cfs == nil {
			cfs = &model.CaseFailStat{
				ProjectId: projectID,
				CaseId:    caseID,
				BranchId:  "",
				CaseType:  caseType,
				FailCount: count,
				Version:   count,
				CreatedBy: user.Account,
				UpdatedBy: user.Account,
				CreatedAt: updatedAt,
				UpdatedAt: updatedAt,
			}

			switch caseType {
			case constants.CaseTypeApiCase:
				apiCase, err := model.CheckApiCaseByCaseId(l.ctx, l.svcCtx.ApiCaseModel, projectID, caseID, "")
				if err != nil {
					return err
				}

				cfs.BranchId = apiCase.CategoryId
			case constants.CaseTypeInterfaceCase:
				interfaceCase, err := model.CheckInterfaceCaseByCaseId(
					l.ctx, l.svcCtx.InterfaceCaseModel, projectID, "", caseID, "",
				)
				if err != nil {
					return err
				}

				cfs.BranchId = interfaceCase.DocumentId
			default:
				return errorx.Errorf(errorx.DoesNotSupport, "invalid case type: %s", caseType)
			}

			_, err = l.svcCtx.CaseFailStatModel.InsertTX(l.ctx, nil, cfs)
		} else if count == 0 {
			err = l.svcCtx.CaseFailStatModel.Delete(l.ctx, nil, cfs.Id)
		} else {
			cfs.FailCount = count
			cfs.Version = count
			if !updatedAt.IsZero() {
				cfs.UpdatedAt = updatedAt
			}

			_, err = l.svcCtx.CaseFailStatModel.UpdateTX(l.ctx, nil, cfs)
		}

		return err
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeout)); err != nil {
		return nil, err
	}

	return &pb.CreateOrModifyFailedCaseResp{}, nil
}
