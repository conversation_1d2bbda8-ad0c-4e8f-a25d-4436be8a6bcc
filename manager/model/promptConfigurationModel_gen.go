// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	promptConfigurationTableName           = "`prompt_configuration`"
	promptConfigurationFieldNames          = builder.RawFieldNames(&PromptConfiguration{})
	promptConfigurationRows                = strings.Join(promptConfigurationFieldNames, ",")
	promptConfigurationRowsExpectAutoSet   = strings.Join(stringx.Remove(promptConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	promptConfigurationRowsWithPlaceHolder = strings.Join(stringx.Remove(promptConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPromptConfigurationIdPrefix                = "cache:manager:promptConfiguration:id:"
	cacheManagerPromptConfigurationProjectIdConfigIdPrefix = "cache:manager:promptConfiguration:projectId:configId:"
)

type (
	promptConfigurationModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PromptConfiguration) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PromptConfiguration, error)
		FindOneByProjectIdConfigId(ctx context.Context, projectId string, configId string) (*PromptConfiguration, error)
		Update(ctx context.Context, session sqlx.Session, data *PromptConfiguration) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPromptConfigurationModel struct {
		sqlc.CachedConn
		table string
	}

	PromptConfiguration struct {
		Id          int64          `db:"id"`          // 自增ID
		ProjectId   string         `db:"project_id"`  // 项目ID
		ConfigId    string         `db:"config_id"`   // Prompt配置ID
		Purpose     int64          `db:"purpose"`     // 用途（UI_AGENT）
		Category    int64          `db:"category"`    // 分类（背景、UI组件、异常处理）
		Name        string         `db:"name"`        // Prompt配置名称
		Description sql.NullString `db:"description"` // Prompt配置描述
		Content     string         `db:"content"`     // Prompt配置内容
		Deleted     int64          `db:"deleted"`     // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`  // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`  // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`  // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`  // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`  // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`  // 删除时间
	}
)

func newPromptConfigurationModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPromptConfigurationModel {
	return &defaultPromptConfigurationModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`prompt_configuration`",
	}
}

func (m *defaultPromptConfigurationModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPromptConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerPromptConfigurationIdPrefix, id)
	managerPromptConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPromptConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPromptConfigurationIdKey, managerPromptConfigurationProjectIdConfigIdKey)
	return err
}

func (m *defaultPromptConfigurationModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerPromptConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerPromptConfigurationIdPrefix, id)
	managerPromptConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPromptConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPromptConfigurationIdKey, managerPromptConfigurationProjectIdConfigIdKey)
	return err
}

func (m *defaultPromptConfigurationModel) FindOne(ctx context.Context, id int64) (*PromptConfiguration, error) {
	managerPromptConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerPromptConfigurationIdPrefix, id)
	var resp PromptConfiguration
	err := m.QueryRowCtx(ctx, &resp, managerPromptConfigurationIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", promptConfigurationRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPromptConfigurationModel) FindOneByProjectIdConfigId(ctx context.Context, projectId string, configId string) (*PromptConfiguration, error) {
	managerPromptConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPromptConfigurationProjectIdConfigIdPrefix, projectId, configId)
	var resp PromptConfiguration
	err := m.QueryRowIndexCtx(ctx, &resp, managerPromptConfigurationProjectIdConfigIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `config_id` = ? and `deleted` = ? limit 1", promptConfigurationRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, configId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPromptConfigurationModel) Insert(ctx context.Context, session sqlx.Session, data *PromptConfiguration) (sql.Result, error) {
	managerPromptConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerPromptConfigurationIdPrefix, data.Id)
	managerPromptConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPromptConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, promptConfigurationRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.Purpose, data.Category, data.Name, data.Description, data.Content, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.Purpose, data.Category, data.Name, data.Description, data.Content, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPromptConfigurationIdKey, managerPromptConfigurationProjectIdConfigIdKey)
}

func (m *defaultPromptConfigurationModel) Update(ctx context.Context, session sqlx.Session, newData *PromptConfiguration) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerPromptConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerPromptConfigurationIdPrefix, data.Id)
	managerPromptConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerPromptConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, promptConfigurationRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.ConfigId, newData.Purpose, newData.Category, newData.Name, newData.Description, newData.Content, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.ConfigId, newData.Purpose, newData.Category, newData.Name, newData.Description, newData.Content, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerPromptConfigurationIdKey, managerPromptConfigurationProjectIdConfigIdKey)
}

func (m *defaultPromptConfigurationModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPromptConfigurationIdPrefix, primary)
}

func (m *defaultPromptConfigurationModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", promptConfigurationRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPromptConfigurationModel) tableName() string {
	return m.table
}
