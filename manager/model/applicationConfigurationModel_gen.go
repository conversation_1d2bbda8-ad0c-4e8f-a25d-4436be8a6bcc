// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	applicationConfigurationTableName           = "`application_configuration`"
	applicationConfigurationFieldNames          = builder.RawFieldNames(&ApplicationConfiguration{})
	applicationConfigurationRows                = strings.Join(applicationConfigurationFieldNames, ",")
	applicationConfigurationRowsExpectAutoSet   = strings.Join(stringx.Remove(applicationConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	applicationConfigurationRowsWithPlaceHolder = strings.Join(stringx.Remove(applicationConfigurationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerApplicationConfigurationIdPrefix                = "cache:manager:applicationConfiguration:id:"
	cacheManagerApplicationConfigurationProjectIdConfigIdPrefix = "cache:manager:applicationConfiguration:projectId:configId:"
)

type (
	applicationConfigurationModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ApplicationConfiguration) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ApplicationConfiguration, error)
		FindOneByProjectIdConfigId(ctx context.Context, projectId string, configId string) (*ApplicationConfiguration, error)
		Update(ctx context.Context, session sqlx.Session, data *ApplicationConfiguration) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultApplicationConfigurationModel struct {
		sqlc.CachedConn
		table string
	}

	ApplicationConfiguration struct {
		Id              int64          `db:"id"`                // 自增ID
		ProjectId       string         `db:"project_id"`        // 项目ID
		ConfigId        string         `db:"config_id"`         // 应用配置ID
		Name            string         `db:"name"`              // 应用配置名称
		Description     sql.NullString `db:"description"`       // 应用配置描述
		PlatformType    int64          `db:"platform_type"`     // 平台类型（Android、IOS）
		AppId           string         `db:"app_id"`            // 应用ID（Android：package_name；IOS：bundle_id）
		AppDownloadLink string         `db:"app_download_link"` // APP下载地址
		Deleted         int64          `db:"deleted"`           // 逻辑删除标识（未删除、已删除）
		CreatedBy       string         `db:"created_by"`        // 创建者的用户ID
		UpdatedBy       string         `db:"updated_by"`        // 最近一次更新者的用户ID
		DeletedBy       sql.NullString `db:"deleted_by"`        // 删除者的用户ID
		CreatedAt       time.Time      `db:"created_at"`        // 创建时间
		UpdatedAt       time.Time      `db:"updated_at"`        // 更新时间
		DeletedAt       sql.NullTime   `db:"deleted_at"`        // 删除时间
	}
)

func newApplicationConfigurationModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultApplicationConfigurationModel {
	return &defaultApplicationConfigurationModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`application_configuration`",
	}
}

func (m *defaultApplicationConfigurationModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerApplicationConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerApplicationConfigurationIdPrefix, id)
	managerApplicationConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerApplicationConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerApplicationConfigurationIdKey, managerApplicationConfigurationProjectIdConfigIdKey)
	return err
}

func (m *defaultApplicationConfigurationModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerApplicationConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerApplicationConfigurationIdPrefix, id)
	managerApplicationConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerApplicationConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerApplicationConfigurationIdKey, managerApplicationConfigurationProjectIdConfigIdKey)
	return err
}

func (m *defaultApplicationConfigurationModel) FindOne(ctx context.Context, id int64) (*ApplicationConfiguration, error) {
	managerApplicationConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerApplicationConfigurationIdPrefix, id)
	var resp ApplicationConfiguration
	err := m.QueryRowCtx(ctx, &resp, managerApplicationConfigurationIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", applicationConfigurationRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultApplicationConfigurationModel) FindOneByProjectIdConfigId(ctx context.Context, projectId string, configId string) (*ApplicationConfiguration, error) {
	managerApplicationConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerApplicationConfigurationProjectIdConfigIdPrefix, projectId, configId)
	var resp ApplicationConfiguration
	err := m.QueryRowIndexCtx(ctx, &resp, managerApplicationConfigurationProjectIdConfigIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `config_id` = ? and `deleted` = ? limit 1", applicationConfigurationRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, configId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultApplicationConfigurationModel) Insert(ctx context.Context, session sqlx.Session, data *ApplicationConfiguration) (sql.Result, error) {
	managerApplicationConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerApplicationConfigurationIdPrefix, data.Id)
	managerApplicationConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerApplicationConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, applicationConfigurationRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.Name, data.Description, data.PlatformType, data.AppId, data.AppDownloadLink, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ConfigId, data.Name, data.Description, data.PlatformType, data.AppId, data.AppDownloadLink, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerApplicationConfigurationIdKey, managerApplicationConfigurationProjectIdConfigIdKey)
}

func (m *defaultApplicationConfigurationModel) Update(ctx context.Context, session sqlx.Session, newData *ApplicationConfiguration) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerApplicationConfigurationIdKey := fmt.Sprintf("%s%v", cacheManagerApplicationConfigurationIdPrefix, data.Id)
	managerApplicationConfigurationProjectIdConfigIdKey := fmt.Sprintf("%s%v:%v", cacheManagerApplicationConfigurationProjectIdConfigIdPrefix, data.ProjectId, data.ConfigId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, applicationConfigurationRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.ConfigId, newData.Name, newData.Description, newData.PlatformType, newData.AppId, newData.AppDownloadLink, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.ConfigId, newData.Name, newData.Description, newData.PlatformType, newData.AppId, newData.AppDownloadLink, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerApplicationConfigurationIdKey, managerApplicationConfigurationProjectIdConfigIdKey)
}

func (m *defaultApplicationConfigurationModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerApplicationConfigurationIdPrefix, primary)
}

func (m *defaultApplicationConfigurationModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", applicationConfigurationRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultApplicationConfigurationModel) tableName() string {
	return m.table
}
