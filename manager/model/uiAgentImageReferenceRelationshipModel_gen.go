// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	uiAgentImageReferenceRelationshipTableName           = "`ui_agent_image_reference_relationship`"
	uiAgentImageReferenceRelationshipFieldNames          = builder.RawFieldNames(&UiAgentImageReferenceRelationship{})
	uiAgentImageReferenceRelationshipRows                = strings.Join(uiAgentImageReferenceRelationshipFieldNames, ",")
	uiAgentImageReferenceRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(uiAgentImageReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	uiAgentImageReferenceRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(uiAgentImageReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerUiAgentImageReferenceRelationshipIdPrefix = "cache:manager:uiAgentImageReferenceRelationship:id:"
)

type (
	uiAgentImageReferenceRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *UiAgentImageReferenceRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UiAgentImageReferenceRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *UiAgentImageReferenceRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultUiAgentImageReferenceRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	UiAgentImageReferenceRelationship struct {
		Id            int64          `db:"id"`
		ProjectId     string         `db:"project_id"`     // 项目ID
		ReferenceType string         `db:"reference_type"` // 引用类型（UI Agent组件）
		ReferenceId   string         `db:"reference_id"`   // 引用ID（UI Agent组件ID）
		ImageId       string         `db:"image_id"`       // 图片ID
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newUiAgentImageReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultUiAgentImageReferenceRelationshipModel {
	return &defaultUiAgentImageReferenceRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`ui_agent_image_reference_relationship`",
	}
}

func (m *defaultUiAgentImageReferenceRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerUiAgentImageReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerUiAgentImageReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerUiAgentImageReferenceRelationshipIdKey)
	return err
}

func (m *defaultUiAgentImageReferenceRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerUiAgentImageReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerUiAgentImageReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerUiAgentImageReferenceRelationshipIdKey)
	return err
}

func (m *defaultUiAgentImageReferenceRelationshipModel) FindOne(ctx context.Context, id int64) (*UiAgentImageReferenceRelationship, error) {
	managerUiAgentImageReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerUiAgentImageReferenceRelationshipIdPrefix, id)
	var resp UiAgentImageReferenceRelationship
	err := m.QueryRowCtx(ctx, &resp, managerUiAgentImageReferenceRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", uiAgentImageReferenceRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiAgentImageReferenceRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *UiAgentImageReferenceRelationship) (sql.Result, error) {
	managerUiAgentImageReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerUiAgentImageReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, uiAgentImageReferenceRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ImageId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ImageId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerUiAgentImageReferenceRelationshipIdKey)
}

func (m *defaultUiAgentImageReferenceRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *UiAgentImageReferenceRelationship) (sql.Result, error) {

	managerUiAgentImageReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerUiAgentImageReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, uiAgentImageReferenceRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ImageId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ImageId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerUiAgentImageReferenceRelationshipIdKey)
}

func (m *defaultUiAgentImageReferenceRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerUiAgentImageReferenceRelationshipIdPrefix, primary)
}

func (m *defaultUiAgentImageReferenceRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", uiAgentImageReferenceRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultUiAgentImageReferenceRelationshipModel) tableName() string {
	return m.table
}
