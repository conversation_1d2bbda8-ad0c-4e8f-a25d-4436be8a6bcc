package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
)

var (
	_                                             ApplicationConfigurationModel = (*customApplicationConfigurationModel)(nil)
	VirtualApplicationConfigurationReferenceModel types.DBModel                 = (*virtualApplicationConfigurationReferenceModel)(nil)

	applicationConfigurationInsertFields = stringx.Remove(
		applicationConfigurationFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)

	virtualApplicationConfigurationReferenceFieldNames = builder.RawFieldNames(&SearchApplicationConfigurationReferenceItem{})
)

type (
	// ApplicationConfigurationModel is an interface to be customized, add more methods here,
	// and implement the added methods in customApplicationConfigurationModel.
	ApplicationConfigurationModel interface {
		applicationConfigurationModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ApplicationConfiguration) squirrel.InsertBuilder
		UpdateBuilder(data *ApplicationConfiguration) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*ApplicationConfiguration, error,
		)

		FindCountBySearchReq(ctx context.Context, req SearchApplicationConfigurationReq) (int64, error)
		FindAllBySearchReq(ctx context.Context, req SearchApplicationConfigurationReq) (
			[]*ApplicationConfiguration, error,
		)
		FindCountReferenceBySearchReq(ctx context.Context, req SearchApplicationConfigurationReferenceReq) (
			int64, error,
		)
		FindReferenceBySearchReq(
			ctx context.Context, req SearchApplicationConfigurationReferenceReq,
		) ([]*SearchApplicationConfigurationReferenceItem, error)
		RemoveByConfigID(ctx context.Context, session sqlx.Session, projectID, configID string) (sql.Result, error)
	}

	customApplicationConfigurationModel struct {
		*defaultApplicationConfigurationModel

		conn sqlx.SqlConn
	}
)

// NewApplicationConfigurationModel returns a model for the database table.
func NewApplicationConfigurationModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) ApplicationConfigurationModel {
	return &customApplicationConfigurationModel{
		defaultApplicationConfigurationModel: newApplicationConfigurationModel(conn, c, opts...),
		conn:                                 conn,
	}
}

func (m *customApplicationConfigurationModel) Table() string {
	return m.table
}

func (m *customApplicationConfigurationModel) Fields() []string {
	return applicationConfigurationFieldNames
}

func (m *customApplicationConfigurationModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customApplicationConfigurationModel) InsertBuilder(data *ApplicationConfiguration) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(applicationConfigurationInsertFields...).Values(
		data.ProjectId, data.ConfigId, data.Name, data.Description, data.PlatformType, data.AppId, data.AppDownloadLink,
		data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customApplicationConfigurationModel) UpdateBuilder(data *ApplicationConfiguration) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":              data.Name,
		"`description`":       data.Description,
		"`platform_type`":     data.PlatformType,
		"`app_id`":            data.AppId,
		"`app_download_link`": data.AppDownloadLink,
		"`deleted`":           data.Deleted,
		"`updated_by`":        data.UpdatedBy,
		"`deleted_by`":        data.DeletedBy,
		"`deleted_at`":        data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customApplicationConfigurationModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(applicationConfigurationFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customApplicationConfigurationModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customApplicationConfigurationModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customApplicationConfigurationModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*ApplicationConfiguration, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ApplicationConfiguration
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customApplicationConfigurationModel) FindCountBySearchReq(
	ctx context.Context, req SearchApplicationConfigurationReq,
) (int64, error) {
	sb := m.SelectCountBuilder().Where("`project_id` = ?", req.ProjectID)

	return m.FindCount(
		ctx, sqlbuilder.SearchOptions(
			sb,
			sqlbuilder.WithCondition(m, req.Condition),
		),
	)
}

func (m *customApplicationConfigurationModel) FindAllBySearchReq(
	ctx context.Context, req SearchApplicationConfigurationReq,
) ([]*ApplicationConfiguration, error) {
	sb := m.SelectBuilder().Where("`project_id` = ?", req.ProjectID)

	return m.FindNoCacheByQuery(
		ctx, sqlbuilder.SearchOptions(
			sb,
			sqlbuilder.WithCondition(m, req.Condition),
			sqlbuilder.WithPagination(m, req.Pagination),
			sqlbuilder.WithSort(m, req.Sort),
		),
	)
}

type (
	virtualApplicationConfigurationReferenceModel struct{}

	SearchApplicationConfigurationReferenceItem struct {
		ProjectID     string         `db:"project_id"`     // 项目ID
		ConfigID      string         `db:"config_id"`      // 应用配置ID
		ReferenceType string         `db:"reference_type"` // 引用对象类型（UI Agent组件）
		ReferenceId   string         `db:"reference_id"`   // 引用对象ID
		Name          string         `db:"name"`           // 引用对象名称
		Description   sql.NullString `db:"description"`    // 引用对象描述
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
	}
)

func (m *virtualApplicationConfigurationReferenceModel) Table() string {
	return "`virtual_application_configuration_references`"
}

func (m *virtualApplicationConfigurationReferenceModel) Fields() []string {
	return virtualApplicationConfigurationReferenceFieldNames
}

func (m *customApplicationConfigurationModel) FindCountReferenceBySearchReq(
	ctx context.Context, req SearchApplicationConfigurationReferenceReq,
) (int64, error) {
	/*
		SQL:
		SELECT COUNT(*)
		FROM (SELECT t.`project_id`,
		             t.`config_id`,
		             t.`reference_type`,
		             t.`reference_id`,
		             t.`name`,
		             t.`description`,
		             t.`created_by`,
		             t.`updated_by`,
		             t.`created_at`,
		             t.`updated_at`
		      FROM (SELECT t1.`project_id`,
		                   t1.`config_id`,
		                   t2.`reference_type`,
		                   t2.`reference_id`,
		                   t3.`name`,
		                   t3.`description`,
		                   t3.`created_by`,
		                   t3.`updated_by`,
		                   t3.`created_at`,
		                   t3.`updated_at`
		            FROM `application_configuration` AS t1
		                     INNER JOIN `application_configuration_reference_relationship` AS t2
		                                ON t1.`project_id` = t2.`project_id` AND
		                                   t1.`config_id` = t2.`config_id` AND
		                                   t1.`deleted` = t2.`deleted`
		                     LEFT JOIN `ui_agent_component` AS t3
		                               ON t1.`project_id` = t3.`project_id` AND
		                                  t1.`deleted` = t3.`deleted` AND
		                                  t2.`reference_type` = 'UI_AGENT_COMPONENT' AND
		                                  t2.`reference_id` = t3.`component_id`
		            WHERE t1.`project_id` = ?
		              AND t1.`config_id` = ?
		              AND t1.`deleted` = ?) AS t) AS t;
	*/

	var (
		aliasT  = "t"
		aliasT1 = "t1"
		aliasT2 = "t2"
		aliasT3 = "t3"

		fields = []string{
			aliasT + ".`project_id`",
			aliasT + ".`config_id`",
			aliasT + ".`reference_type`",
			aliasT + ".`reference_id`",
			aliasT + ".`name`",
			aliasT + ".`description`",
			aliasT + ".`created_by`",
			aliasT + ".`updated_by`",
			aliasT + ".`created_at`",
			aliasT + ".`updated_at`",
		}
		subFields = []string{
			aliasT1 + ".`project_id`",
			aliasT1 + ".`config_id`",
			aliasT2 + ".`reference_type`",
			aliasT2 + ".`reference_id`",
			aliasT3 + ".`name`",
			aliasT3 + ".`description`",
			aliasT3 + ".`created_by`",
			aliasT3 + ".`updated_by`",
			aliasT3 + ".`created_at`",
			aliasT3 + ".`updated_at`",
		}

		tmp = VirtualApplicationConfigurationReferenceModel
	)

	sb := squirrel.Select(fields...).
		FromSelect(
			squirrel.Select(subFields...).
				From(m.table+" AS "+aliasT1).
				InnerJoin(
					fmt.Sprintf(
						"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`config_id` = %s.`config_id` AND %s.`deleted` = %s.`deleted`",
						applicationConfigurationReferenceRelationshipTableName, aliasT2,
						aliasT1, aliasT2,
						aliasT1, aliasT2,
						aliasT1, aliasT2,
					),
				).
				LeftJoin(
					fmt.Sprintf(
						"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`deleted` = %s.`deleted` AND %s.`reference_type` = '%s' AND %s.`reference_id` = %s.`component_id`",
						uiAgentComponentTableName, aliasT3,
						aliasT1, aliasT3,
						aliasT1, aliasT3,
						aliasT2, common.ConstReferenceTypeUIAgentComponent,
						aliasT2, aliasT3,
					),
				).
				Where(
					fmt.Sprintf(
						"%s.`project_id` = ? AND %s.`config_id` = ? AND %s.`deleted` = ?",
						aliasT1, aliasT1, aliasT1,
					),
					req.ProjectID, req.ConfigID, constants.NotDeleted,
				),
			aliasT,
		)
	sb = squirrel.Select("COUNT(*)").
		FromSelect(
			sqlbuilder.SearchOptionsWithAlias(sb, aliasT, sqlbuilder.WithCondition(tmp, req.Condition)),
			aliasT,
		)

	return m.FindCount(ctx, sb)
}

func (m *customApplicationConfigurationModel) FindReferenceBySearchReq(
	ctx context.Context, req SearchApplicationConfigurationReferenceReq,
) ([]*SearchApplicationConfigurationReferenceItem, error) {
	/*
		SQL:
		SELECT t.`project_id`,
		       t.`config_id`,
		       t.`reference_type`,
		       t.`reference_id`,
		       t.`name`,
		       t.`description`,
		       t.`created_by`,
		       t.`updated_by`,
		       t.`created_at`,
		       t.`updated_at`
		FROM (SELECT t1.`project_id`,
		             t1.`config_id`,
		             t2.`reference_type`,
		             t2.`reference_id`,
		             t3.`name`,
		             t3.`description`,
		             t3.`created_by`,
		             t3.`updated_by`,
		             t3.`created_at`,
		             t3.`updated_at`
		      FROM `application_configuration` AS t1
		               INNER JOIN `application_configuration_reference_relationship` AS t2
		                          ON t1.`project_id` = t2.`project_id` AND
		                             t1.`config_id` = t2.`config_id` AND
		                             t1.`deleted` = t2.`deleted`
		               LEFT JOIN `ui_agent_component` AS t3
		                         ON t1.`project_id` = t3.`project_id` AND
		                            t1.`deleted` = t3.`deleted` AND
		                            t2.`reference_type` = 'UI_AGENT_COMPONENT' AND
		                            t2.`reference_id` = t3.`component_id`
		      WHERE t1.`project_id` = ?
		        AND t1.`config_id` = ?
		        AND t1.`deleted` = ?) AS t;
	*/

	var (
		aliasT  = "t"
		aliasT1 = "t1"
		aliasT2 = "t2"
		aliasT3 = "t3"

		fields = []string{
			aliasT + ".`project_id`",
			aliasT + ".`config_id`",
			aliasT + ".`reference_type`",
			aliasT + ".`reference_id`",
			aliasT + ".`name`",
			aliasT + ".`description`",
			aliasT + ".`created_by`",
			aliasT + ".`updated_by`",
			aliasT + ".`created_at`",
			aliasT + ".`updated_at`",
		}
		subFields = []string{
			aliasT1 + ".`project_id`",
			aliasT1 + ".`config_id`",
			aliasT2 + ".`reference_type`",
			aliasT2 + ".`reference_id`",
			aliasT3 + ".`name`",
			aliasT3 + ".`description`",
			aliasT3 + ".`created_by`",
			aliasT3 + ".`updated_by`",
			aliasT3 + ".`created_at`",
			aliasT3 + ".`updated_at`",
		}

		tmp = VirtualApplicationConfigurationReferenceModel
	)

	sb := squirrel.Select(fields...).
		FromSelect(
			squirrel.Select(subFields...).
				From(m.table+" AS "+aliasT1).
				InnerJoin(
					fmt.Sprintf(
						"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`config_id` = %s.`config_id` AND %s.`deleted` = %s.`deleted`",
						applicationConfigurationReferenceRelationshipTableName, aliasT2,
						aliasT1, aliasT2,
						aliasT1, aliasT2,
						aliasT1, aliasT2,
					),
				).
				LeftJoin(
					fmt.Sprintf(
						"%s AS %s ON %s.`project_id` = %s.`project_id` AND %s.`deleted` = %s.`deleted` AND %s.`reference_type` = '%s' AND %s.`reference_id` = %s.`component_id`",
						uiAgentComponentTableName, aliasT3,
						aliasT1, aliasT3,
						aliasT1, aliasT3,
						aliasT2, common.ConstReferenceTypeUIAgentComponent,
						aliasT2, aliasT3,
					),
				).
				Where(
					fmt.Sprintf(
						"%s.`project_id` = ? AND %s.`config_id` = ? AND %s.`deleted` = ?",
						aliasT1, aliasT1, aliasT1,
					),
					req.ProjectID, req.ConfigID, constants.NotDeleted,
				),
			aliasT,
		)
	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, aliasT,
		sqlbuilder.WithCondition(tmp, req.Condition),
		sqlbuilder.WithPagination(tmp, req.Pagination),
		sqlbuilder.WithSort(tmp, req.Sort),
	)

	var (
		resp []*SearchApplicationConfigurationReferenceItem
		err  error
	)
	err = utils.FindRows(
		ctx, m.conn, sb, func() any {
			return &resp
		},
	)
	return resp, err
}

func (m *customApplicationConfigurationModel) RemoveByConfigID(
	ctx context.Context, session sqlx.Session, projectID, configID string,
) (sql.Result, error) {
	keys := m.getKeysByConfigID(ctx, projectID, configID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			stmt, values, err := squirrel.Update(m.table).
				Set("`deleted`", constants.HasDeleted).
				Set("`deleted_at`", sql.NullTime{Time: time.Now(), Valid: true}).
				Where("`project_id` = ? AND `config_id` = ?", projectID, configID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customApplicationConfigurationModel) getKeysByConfigID(
	ctx context.Context, projectID, configID string,
) []string {
	config, err := m.FindOneByProjectIdConfigId(ctx, projectID, configID)
	if err != nil {
		return []string{}
	}

	return []string{
		fmt.Sprintf("%s%v", cacheManagerApplicationConfigurationIdPrefix, config.Id),
		fmt.Sprintf("%s%v:%v", cacheManagerApplicationConfigurationProjectIdConfigIdPrefix, projectID, configID),
	}
}
