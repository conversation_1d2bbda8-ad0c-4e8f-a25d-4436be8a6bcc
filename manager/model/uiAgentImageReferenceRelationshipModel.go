package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ UiAgentImageReferenceRelationshipModel = (*customUiAgentImageReferenceRelationshipModel)(nil)

	uiAgentImageReferenceRelationshipInsertFields = stringx.Remove(
		uiAgentImageReferenceRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`",
		"`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// UiAgentImageReferenceRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUiAgentImageReferenceRelationshipModel.
	UiAgentImageReferenceRelationshipModel interface {
		uiAgentImageReferenceRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *UiAgentImageReferenceRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *UiAgentImageReferenceRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(
			ctx context.Context, selectBuilder squirrel.SelectBuilder,
		) ([]*UiAgentImageReferenceRelationship, error)

		FindByProjectID(ctx context.Context, projectID string) ([]*UiAgentImageReferenceRelationship, error)
		FindByImageID(ctx context.Context, projectID, imageID string) ([]*UiAgentImageReferenceRelationship, error)
		FindByReference(
			ctx context.Context, projectID, referenceType, referenceID string,
		) ([]*UiAgentImageReferenceRelationship, error)
		RemoveByImageID(ctx context.Context, session sqlx.Session, projectID, imageID string) (sql.Result, error)
		RemoveByReference(
			ctx context.Context, session sqlx.Session, projectID, referenceType, referenceID string,
		) (sql.Result, error)
	}

	customUiAgentImageReferenceRelationshipModel struct {
		*defaultUiAgentImageReferenceRelationshipModel

		conn sqlx.SqlConn
	}
)

// NewUiAgentImageReferenceRelationshipModel returns a model for the database table.
func NewUiAgentImageReferenceRelationshipModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) UiAgentImageReferenceRelationshipModel {
	return &customUiAgentImageReferenceRelationshipModel{
		defaultUiAgentImageReferenceRelationshipModel: newUiAgentImageReferenceRelationshipModel(conn, c, opts...),
		conn: conn,
	}
}

func (m *customUiAgentImageReferenceRelationshipModel) Table() string {
	return m.table
}

func (m *customUiAgentImageReferenceRelationshipModel) Fields() []string {
	return uiAgentImageReferenceRelationshipFieldNames
}

func (m *customUiAgentImageReferenceRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customUiAgentImageReferenceRelationshipModel) InsertBuilder(data *UiAgentImageReferenceRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(uiAgentImageReferenceRelationshipInsertFields...).Values(
		data.ProjectId, data.ReferenceType, data.ReferenceId, data.ImageId, data.Deleted, data.CreatedBy,
		data.UpdatedBy,
	)
}

func (m *customUiAgentImageReferenceRelationshipModel) UpdateBuilder(data *UiAgentImageReferenceRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customUiAgentImageReferenceRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(uiAgentImageReferenceRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customUiAgentImageReferenceRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiAgentImageReferenceRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customUiAgentImageReferenceRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*UiAgentImageReferenceRelationship, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiAgentImageReferenceRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customUiAgentImageReferenceRelationshipModel) FindByProjectID(
	ctx context.Context, projectID string,
) ([]*UiAgentImageReferenceRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `ui_agent_image_reference_relationship`
		WHERE `project_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ?", projectID))
}

func (m *customUiAgentImageReferenceRelationshipModel) FindByImageID(
	ctx context.Context, projectID, imageID string,
) ([]*UiAgentImageReferenceRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `ui_agent_image_reference_relationship`
		WHERE `project_id` = ?
		  AND `image_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where("`project_id` = ? AND `image_id` = ?", projectID, imageID),
	)
}

func (m *customUiAgentImageReferenceRelationshipModel) FindByReference(
	ctx context.Context, projectID, referenceType, referenceID string,
) ([]*UiAgentImageReferenceRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `ui_agent_image_reference_relationship`
		WHERE `project_id` = ?
		  AND `reference_type` = ?
		  AND `reference_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where(
			"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
			projectID, referenceType, referenceID,
		),
	)
}

func (m *customUiAgentImageReferenceRelationshipModel) RemoveByImageID(
	ctx context.Context, session sqlx.Session, projectID, imageID string,
) (sql.Result, error) {
	keys := m.getKeysByImageID(ctx, projectID, imageID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `ui_agent_image_reference_relationship`
				WHERE `project_id` = ?
				  AND `image_id` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where("`project_id` = ? AND `image_id` = ?", projectID, imageID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customUiAgentImageReferenceRelationshipModel) getKeysByImageID(
	ctx context.Context, projectID, imageID string,
) []string {
	cs, err := m.FindByImageID(ctx, projectID, imageID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerUiAgentImageReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}

func (m *customUiAgentImageReferenceRelationshipModel) RemoveByReference(
	ctx context.Context, session sqlx.Session, projectID, referenceType, referenceID string,
) (sql.Result, error) {
	keys := m.getKeysByReference(ctx, projectID, referenceType, referenceID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `ui_agent_image_reference_relationship`
				WHERE `project_id` = ?
				  AND `reference_type` = ?
				  AND `reference_id` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where(
					"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
					projectID, referenceType, referenceID,
				).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customUiAgentImageReferenceRelationshipModel) getKeysByReference(
	ctx context.Context, projectID, referenceType, referenceID string,
) []string {
	cs, err := m.FindByReference(ctx, projectID, referenceType, referenceID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerUiAgentImageReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}
