// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	applicationConfigurationReferenceRelationshipTableName           = "`application_configuration_reference_relationship`"
	applicationConfigurationReferenceRelationshipFieldNames          = builder.RawFieldNames(&ApplicationConfigurationReferenceRelationship{})
	applicationConfigurationReferenceRelationshipRows                = strings.Join(applicationConfigurationReferenceRelationshipFieldNames, ",")
	applicationConfigurationReferenceRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(applicationConfigurationReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	applicationConfigurationReferenceRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(applicationConfigurationReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerApplicationConfigurationReferenceRelationshipIdPrefix = "cache:manager:applicationConfigurationReferenceRelationship:id:"
)

type (
	applicationConfigurationReferenceRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ApplicationConfigurationReferenceRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ApplicationConfigurationReferenceRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *ApplicationConfigurationReferenceRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultApplicationConfigurationReferenceRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	ApplicationConfigurationReferenceRelationship struct {
		Id            int64          `db:"id"`
		ProjectId     string         `db:"project_id"`     // 项目ID
		ReferenceType string         `db:"reference_type"` // 引用类型（UI Agent组件）
		ReferenceId   string         `db:"reference_id"`   // 引用ID（UI Agent组件ID）
		ConfigId      string         `db:"config_id"`      // 应用配置ID
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newApplicationConfigurationReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultApplicationConfigurationReferenceRelationshipModel {
	return &defaultApplicationConfigurationReferenceRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`application_configuration_reference_relationship`",
	}
}

func (m *defaultApplicationConfigurationReferenceRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerApplicationConfigurationReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApplicationConfigurationReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerApplicationConfigurationReferenceRelationshipIdKey)
	return err
}

func (m *defaultApplicationConfigurationReferenceRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerApplicationConfigurationReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApplicationConfigurationReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerApplicationConfigurationReferenceRelationshipIdKey)
	return err
}

func (m *defaultApplicationConfigurationReferenceRelationshipModel) FindOne(ctx context.Context, id int64) (*ApplicationConfigurationReferenceRelationship, error) {
	managerApplicationConfigurationReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApplicationConfigurationReferenceRelationshipIdPrefix, id)
	var resp ApplicationConfigurationReferenceRelationship
	err := m.QueryRowCtx(ctx, &resp, managerApplicationConfigurationReferenceRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", applicationConfigurationReferenceRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultApplicationConfigurationReferenceRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *ApplicationConfigurationReferenceRelationship) (sql.Result, error) {
	managerApplicationConfigurationReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApplicationConfigurationReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, applicationConfigurationReferenceRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerApplicationConfigurationReferenceRelationshipIdKey)
}

func (m *defaultApplicationConfigurationReferenceRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *ApplicationConfigurationReferenceRelationship) (sql.Result, error) {

	managerApplicationConfigurationReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApplicationConfigurationReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, applicationConfigurationReferenceRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerApplicationConfigurationReferenceRelationshipIdKey)
}

func (m *defaultApplicationConfigurationReferenceRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerApplicationConfigurationReferenceRelationshipIdPrefix, primary)
}

func (m *defaultApplicationConfigurationReferenceRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", applicationConfigurationReferenceRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultApplicationConfigurationReferenceRelationshipModel) tableName() string {
	return m.table
}
