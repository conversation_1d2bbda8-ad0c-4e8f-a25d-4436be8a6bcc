package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ ApplicationConfigurationReferenceRelationshipModel = (*customApplicationConfigurationReferenceRelationshipModel)(nil)

	applicationConfigurationReferenceRelationshipInsertFields = stringx.Remove(
		applicationConfigurationReferenceRelationshipFieldNames, "`id`", "`create_time`", "`update_time`",
		"`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// ApplicationConfigurationReferenceRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customApplicationConfigurationReferenceRelationshipModel.
	ApplicationConfigurationReferenceRelationshipModel interface {
		applicationConfigurationReferenceRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ApplicationConfigurationReferenceRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *ApplicationConfigurationReferenceRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(
			ctx context.Context, selectBuilder squirrel.SelectBuilder,
		) ([]*ApplicationConfigurationReferenceRelationship, error)

		FindByProjectID(ctx context.Context, projectID string) ([]*ApplicationConfigurationReferenceRelationship, error)
		FindByConfigID(ctx context.Context, projectID, configID string) (
			[]*ApplicationConfigurationReferenceRelationship, error,
		)
		FindByReference(
			ctx context.Context, projectID, referenceType, referenceID string,
		) ([]*ApplicationConfigurationReferenceRelationship, error)
		RemoveByConfigID(ctx context.Context, session sqlx.Session, projectID, configID string) (sql.Result, error)
		RemoveByReference(
			ctx context.Context, session sqlx.Session, projectID, referenceType, referenceID string,
		) (sql.Result, error)
	}

	customApplicationConfigurationReferenceRelationshipModel struct {
		*defaultApplicationConfigurationReferenceRelationshipModel

		conn sqlx.SqlConn
	}
)

// NewApplicationConfigurationReferenceRelationshipModel returns a model for the database table.
func NewApplicationConfigurationReferenceRelationshipModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) ApplicationConfigurationReferenceRelationshipModel {
	return &customApplicationConfigurationReferenceRelationshipModel{
		defaultApplicationConfigurationReferenceRelationshipModel: newApplicationConfigurationReferenceRelationshipModel(
			conn, c, opts...,
		),
		conn: conn,
	}
}

func (m *customApplicationConfigurationReferenceRelationshipModel) Table() string {
	return m.table
}

func (m *customApplicationConfigurationReferenceRelationshipModel) Fields() []string {
	return applicationConfigurationReferenceRelationshipFieldNames
}

func (m *customApplicationConfigurationReferenceRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customApplicationConfigurationReferenceRelationshipModel) InsertBuilder(data *ApplicationConfigurationReferenceRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(applicationConfigurationReferenceRelationshipInsertFields...).Values(
		data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy,
		data.UpdatedBy,
	)
}

func (m *customApplicationConfigurationReferenceRelationshipModel) UpdateBuilder(data *ApplicationConfigurationReferenceRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customApplicationConfigurationReferenceRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(applicationConfigurationReferenceRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customApplicationConfigurationReferenceRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customApplicationConfigurationReferenceRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customApplicationConfigurationReferenceRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*ApplicationConfigurationReferenceRelationship, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ApplicationConfigurationReferenceRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customApplicationConfigurationReferenceRelationshipModel) FindByProjectID(
	ctx context.Context, projectID string,
) ([]*ApplicationConfigurationReferenceRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `application_configuration_reference_relationship`
		WHERE `project_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ?", projectID))
}

func (m *customApplicationConfigurationReferenceRelationshipModel) FindByConfigID(
	ctx context.Context, projectID, configID string,
) ([]*ApplicationConfigurationReferenceRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `application_configuration_reference_relationship`
		WHERE `project_id` = ?
		  AND `config_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where("`project_id` = ? AND `config_id` = ?", projectID, configID),
	)
}

func (m *customApplicationConfigurationReferenceRelationshipModel) FindByReference(
	ctx context.Context, projectID, referenceType, referenceID string,
) ([]*ApplicationConfigurationReferenceRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `application_configuration_reference_relationship`
		WHERE `project_id` = ?
		  AND `reference_type` = ?
		  AND `reference_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where(
			"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
			projectID, referenceType, referenceID,
		),
	)
}

func (m *customApplicationConfigurationReferenceRelationshipModel) RemoveByConfigID(
	ctx context.Context, session sqlx.Session, projectID, configID string,
) (sql.Result, error) {
	keys := m.getKeysByConfigID(ctx, projectID, configID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `application_configuration_reference_relationship`
				WHERE `project_id` = ?
				  AND `config_id` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where("`project_id` = ? AND `config_id` = ?", projectID, configID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customApplicationConfigurationReferenceRelationshipModel) getKeysByConfigID(
	ctx context.Context, projectID, configID string,
) []string {
	cs, err := m.FindByConfigID(ctx, projectID, configID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(
			keys, fmt.Sprintf("%s%v", cacheManagerApplicationConfigurationReferenceRelationshipIdPrefix, c.Id),
		)
	}

	return keys
}

func (m *customApplicationConfigurationReferenceRelationshipModel) RemoveByReference(
	ctx context.Context, session sqlx.Session, projectID, referenceType, referenceID string,
) (sql.Result, error) {
	keys := m.getKeysByReference(ctx, projectID, referenceType, referenceID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `application_configuration_reference_relationship`
				WHERE `project_id` = ?
				  AND `reference_type` = ?
				  AND `reference_id` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where(
					"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
					projectID, referenceType, referenceID,
				).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customApplicationConfigurationReferenceRelationshipModel) getKeysByReference(
	ctx context.Context, projectID, referenceType, referenceID string,
) []string {
	cs, err := m.FindByReference(ctx, projectID, referenceType, referenceID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(
			keys, fmt.Sprintf("%s%v", cacheManagerApplicationConfigurationReferenceRelationshipIdPrefix, c.Id),
		)
	}

	return keys
}
