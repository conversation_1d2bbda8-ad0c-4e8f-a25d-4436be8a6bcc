package model

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ PromptConfigurationReferenceRelationshipModel = (*customPromptConfigurationReferenceRelationshipModel)(nil)

	promptConfigurationReferenceRelationshipInsertFields = stringx.Remove(
		promptConfigurationReferenceRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`",
		"`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// PromptConfigurationReferenceRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPromptConfigurationReferenceRelationshipModel.
	PromptConfigurationReferenceRelationshipModel interface {
		promptConfigurationReferenceRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PromptConfigurationReferenceRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *PromptConfigurationReferenceRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(
			ctx context.Context, selectBuilder squirrel.SelectBuilder,
		) ([]*PromptConfigurationReferenceRelationship, error)

		BatchInsert(
			ctx context.Context, session sqlx.Session, configs []*PromptConfigurationReferenceRelationship,
		) (sql.Result, error)
		FindByProjectID(ctx context.Context, projectID string) ([]*PromptConfigurationReferenceRelationship, error)
		FindByConfigID(ctx context.Context, projectID, configID string) (
			[]*PromptConfigurationReferenceRelationship, error,
		)
		FindByReference(
			ctx context.Context, projectID, referenceType, referenceID string,
		) ([]*PromptConfigurationReferenceRelationship, error)
		RemoveByConfigID(ctx context.Context, session sqlx.Session, projectID, configID string) (sql.Result, error)
		RemoveByReference(
			ctx context.Context, session sqlx.Session, projectID, referenceType, referenceID string,
		) (sql.Result, error)
	}

	customPromptConfigurationReferenceRelationshipModel struct {
		*defaultPromptConfigurationReferenceRelationshipModel

		conn sqlx.SqlConn
	}
)

// NewPromptConfigurationReferenceRelationshipModel returns a model for the database table.
func NewPromptConfigurationReferenceRelationshipModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) PromptConfigurationReferenceRelationshipModel {
	return &customPromptConfigurationReferenceRelationshipModel{
		defaultPromptConfigurationReferenceRelationshipModel: newPromptConfigurationReferenceRelationshipModel(
			conn, c, opts...,
		),
		conn: conn,
	}
}

func (m *customPromptConfigurationReferenceRelationshipModel) Table() string {
	return m.table
}

func (m *customPromptConfigurationReferenceRelationshipModel) Fields() []string {
	return promptConfigurationReferenceRelationshipFieldNames
}

func (m *customPromptConfigurationReferenceRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPromptConfigurationReferenceRelationshipModel) InsertBuilder(data *PromptConfigurationReferenceRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(promptConfigurationReferenceRelationshipInsertFields...).Values(
		data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy,
		data.UpdatedBy,
	)
}

func (m *customPromptConfigurationReferenceRelationshipModel) UpdateBuilder(data *PromptConfigurationReferenceRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPromptConfigurationReferenceRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(promptConfigurationReferenceRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customPromptConfigurationReferenceRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPromptConfigurationReferenceRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPromptConfigurationReferenceRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PromptConfigurationReferenceRelationship, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PromptConfigurationReferenceRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPromptConfigurationReferenceRelationshipModel) BatchInsert(
	ctx context.Context, session sqlx.Session, configs []*PromptConfigurationReferenceRelationship,
) (sql.Result, error) {
	count := len(configs)
	if count == 0 {
		return nil, nil
	} else if count == 1 {
		return m.Insert(ctx, session, configs[0])
	}

	builder := squirrel.Insert(m.table).Columns(promptConfigurationReferenceRelationshipInsertFields...)
	for _, config := range configs {
		builder = builder.Values(
			config.ProjectId, config.ReferenceType, config.ReferenceId, config.ConfigId, config.Deleted,
			config.CreatedBy, config.UpdatedBy,
		)
	}

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			query, values, err := builder.ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return session.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		},
	)
}

func (m *customPromptConfigurationReferenceRelationshipModel) FindByProjectID(
	ctx context.Context, projectID string,
) ([]*PromptConfigurationReferenceRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `prompt_configuration_reference_relationship`
		WHERE `project_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(ctx, m.SelectBuilder().Where("`project_id` = ?", projectID))
}

func (m *customPromptConfigurationReferenceRelationshipModel) FindByConfigID(
	ctx context.Context, projectID, configID string,
) ([]*PromptConfigurationReferenceRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `prompt_configuration_reference_relationship`
		WHERE `project_id` = ?
		  AND `config_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where("`project_id` = ? AND `config_id` = ?", projectID, configID),
	)
}

func (m *customPromptConfigurationReferenceRelationshipModel) FindByReference(
	ctx context.Context, projectID, referenceType, referenceID string,
) ([]*PromptConfigurationReferenceRelationship, error) {
	/*
		SQL:
		SELECT *
		FROM `prompt_configuration_reference_relationship`
		WHERE `project_id` = ?
		  AND `reference_type` = ?
		  AND `reference_id` = ?
		  AND `deleted` = ?
	*/

	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where(
			"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
			projectID, referenceType, referenceID,
		),
	)
}

func (m *customPromptConfigurationReferenceRelationshipModel) RemoveByConfigID(
	ctx context.Context, session sqlx.Session, projectID, configID string,
) (sql.Result, error) {
	keys := m.getKeysByConfigID(ctx, projectID, configID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `prompt_configuration_reference_relationship`
				WHERE `project_id` = ?
				  AND `config_id` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where("`project_id` = ? AND `config_id` = ?", projectID, configID).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customPromptConfigurationReferenceRelationshipModel) getKeysByConfigID(
	ctx context.Context, projectID, configID string,
) []string {
	cs, err := m.FindByConfigID(ctx, projectID, configID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerPromptConfigurationReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}

func (m *customPromptConfigurationReferenceRelationshipModel) RemoveByReference(
	ctx context.Context, session sqlx.Session, projectID, referenceType, referenceID string,
) (sql.Result, error) {
	keys := m.getKeysByReference(ctx, projectID, referenceType, referenceID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				DELETE FROM `prompt_configuration_reference_relationship`
				WHERE `project_id` = ?
				  AND `reference_type` = ?
				  AND `reference_id` = ?
			*/

			stmt, values, err := squirrel.Delete(m.table).
				Where(
					"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
					projectID, referenceType, referenceID,
				).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customPromptConfigurationReferenceRelationshipModel) getKeysByReference(
	ctx context.Context, projectID, referenceType, referenceID string,
) []string {
	cs, err := m.FindByReference(ctx, projectID, referenceType, referenceID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerPromptConfigurationReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}
