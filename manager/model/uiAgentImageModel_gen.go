// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	uiAgentImageTableName           = "`ui_agent_image`"
	uiAgentImageFieldNames          = builder.RawFieldNames(&UiAgentImage{})
	uiAgentImageRows                = strings.Join(uiAgentImageFieldNames, ",")
	uiAgentImageRowsExpectAutoSet   = strings.Join(stringx.Remove(uiAgentImageFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	uiAgentImageRowsWithPlaceHolder = strings.Join(stringx.Remove(uiAgentImageFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerUiAgentImageIdPrefix               = "cache:manager:uiAgentImage:id:"
	cacheManagerUiAgentImageProjectIdImageIdPrefix = "cache:manager:uiAgentImage:projectId:imageId:"
)

type (
	uiAgentImageModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *UiAgentImage) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UiAgentImage, error)
		FindOneByProjectIdImageId(ctx context.Context, projectId string, imageId string) (*UiAgentImage, error)
		Update(ctx context.Context, session sqlx.Session, data *UiAgentImage) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultUiAgentImageModel struct {
		sqlc.CachedConn
		table string
	}

	UiAgentImage struct {
		Id          int64          `db:"id"`          // 自增ID
		ProjectId   string         `db:"project_id"`  // 项目ID
		ImageId     string         `db:"image_id"`    // 图片ID
		Name        string         `db:"name"`        // 图片名称
		Description sql.NullString `db:"description"` // 图片描述
		Extension   string         `db:"extension"`   // 图片文件的扩展名
		Hash        string         `db:"hash"`        // 图片文件的一致性哈希值（MD5）
		Size        int64          `db:"size"`        // 图片文件的大小
		Path        string         `db:"path"`        // 图片文件的路径
		Deleted     int64          `db:"deleted"`     // 逻辑删除标识（未删除、已删除）
		CreatedBy   string         `db:"created_by"`  // 创建者的用户ID
		UpdatedBy   string         `db:"updated_by"`  // 最近一次更新者的用户ID
		DeletedBy   sql.NullString `db:"deleted_by"`  // 删除者的用户ID
		CreatedAt   time.Time      `db:"created_at"`  // 创建时间
		UpdatedAt   time.Time      `db:"updated_at"`  // 更新时间
		DeletedAt   sql.NullTime   `db:"deleted_at"`  // 删除时间
	}
)

func newUiAgentImageModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultUiAgentImageModel {
	return &defaultUiAgentImageModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`ui_agent_image`",
	}
}

func (m *defaultUiAgentImageModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerUiAgentImageIdKey := fmt.Sprintf("%s%v", cacheManagerUiAgentImageIdPrefix, id)
	managerUiAgentImageProjectIdImageIdKey := fmt.Sprintf("%s%v:%v", cacheManagerUiAgentImageProjectIdImageIdPrefix, data.ProjectId, data.ImageId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerUiAgentImageIdKey, managerUiAgentImageProjectIdImageIdKey)
	return err
}

func (m *defaultUiAgentImageModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	managerUiAgentImageIdKey := fmt.Sprintf("%s%v", cacheManagerUiAgentImageIdPrefix, id)
	managerUiAgentImageProjectIdImageIdKey := fmt.Sprintf("%s%v:%v", cacheManagerUiAgentImageProjectIdImageIdPrefix, data.ProjectId, data.ImageId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerUiAgentImageIdKey, managerUiAgentImageProjectIdImageIdKey)
	return err
}

func (m *defaultUiAgentImageModel) FindOne(ctx context.Context, id int64) (*UiAgentImage, error) {
	managerUiAgentImageIdKey := fmt.Sprintf("%s%v", cacheManagerUiAgentImageIdPrefix, id)
	var resp UiAgentImage
	err := m.QueryRowCtx(ctx, &resp, managerUiAgentImageIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", uiAgentImageRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiAgentImageModel) FindOneByProjectIdImageId(ctx context.Context, projectId string, imageId string) (*UiAgentImage, error) {
	managerUiAgentImageProjectIdImageIdKey := fmt.Sprintf("%s%v:%v", cacheManagerUiAgentImageProjectIdImageIdPrefix, projectId, imageId)
	var resp UiAgentImage
	err := m.QueryRowIndexCtx(ctx, &resp, managerUiAgentImageProjectIdImageIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `project_id` = ? and `image_id` = ? and `deleted` = ? limit 1", uiAgentImageRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, projectId, imageId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiAgentImageModel) Insert(ctx context.Context, session sqlx.Session, data *UiAgentImage) (sql.Result, error) {
	managerUiAgentImageIdKey := fmt.Sprintf("%s%v", cacheManagerUiAgentImageIdPrefix, data.Id)
	managerUiAgentImageProjectIdImageIdKey := fmt.Sprintf("%s%v:%v", cacheManagerUiAgentImageProjectIdImageIdPrefix, data.ProjectId, data.ImageId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, uiAgentImageRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ImageId, data.Name, data.Description, data.Extension, data.Hash, data.Size, data.Path, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ImageId, data.Name, data.Description, data.Extension, data.Hash, data.Size, data.Path, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerUiAgentImageIdKey, managerUiAgentImageProjectIdImageIdKey)
}

func (m *defaultUiAgentImageModel) Update(ctx context.Context, session sqlx.Session, newData *UiAgentImage) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	managerUiAgentImageIdKey := fmt.Sprintf("%s%v", cacheManagerUiAgentImageIdPrefix, data.Id)
	managerUiAgentImageProjectIdImageIdKey := fmt.Sprintf("%s%v:%v", cacheManagerUiAgentImageProjectIdImageIdPrefix, data.ProjectId, data.ImageId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, uiAgentImageRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.ProjectId, newData.ImageId, newData.Name, newData.Description, newData.Extension, newData.Hash, newData.Size, newData.Path, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.ProjectId, newData.ImageId, newData.Name, newData.Description, newData.Extension, newData.Hash, newData.Size, newData.Path, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, managerUiAgentImageIdKey, managerUiAgentImageProjectIdImageIdKey)
}

func (m *defaultUiAgentImageModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerUiAgentImageIdPrefix, primary)
}

func (m *defaultUiAgentImageModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", uiAgentImageRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultUiAgentImageModel) tableName() string {
	return m.table
}
