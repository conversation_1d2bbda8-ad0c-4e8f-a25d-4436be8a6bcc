// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	promptConfigurationReferenceRelationshipTableName           = "`prompt_configuration_reference_relationship`"
	promptConfigurationReferenceRelationshipFieldNames          = builder.RawFieldNames(&PromptConfigurationReferenceRelationship{})
	promptConfigurationReferenceRelationshipRows                = strings.Join(promptConfigurationReferenceRelationshipFieldNames, ",")
	promptConfigurationReferenceRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(promptConfigurationReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	promptConfigurationReferenceRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(promptConfigurationReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerPromptConfigurationReferenceRelationshipIdPrefix = "cache:manager:promptConfigurationReferenceRelationship:id:"
)

type (
	promptConfigurationReferenceRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PromptConfigurationReferenceRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PromptConfigurationReferenceRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *PromptConfigurationReferenceRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPromptConfigurationReferenceRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	PromptConfigurationReferenceRelationship struct {
		Id            int64          `db:"id"`
		ProjectId     string         `db:"project_id"`     // 项目ID
		ReferenceType string         `db:"reference_type"` // 引用类型（应用配置）
		ReferenceId   string         `db:"reference_id"`   // 引用ID（应用配置ID）
		ConfigId      string         `db:"config_id"`      // Prompt配置ID
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newPromptConfigurationReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPromptConfigurationReferenceRelationshipModel {
	return &defaultPromptConfigurationReferenceRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`prompt_configuration_reference_relationship`",
	}
}

func (m *defaultPromptConfigurationReferenceRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerPromptConfigurationReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPromptConfigurationReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerPromptConfigurationReferenceRelationshipIdKey)
	return err
}

func (m *defaultPromptConfigurationReferenceRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerPromptConfigurationReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPromptConfigurationReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerPromptConfigurationReferenceRelationshipIdKey)
	return err
}

func (m *defaultPromptConfigurationReferenceRelationshipModel) FindOne(ctx context.Context, id int64) (*PromptConfigurationReferenceRelationship, error) {
	managerPromptConfigurationReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPromptConfigurationReferenceRelationshipIdPrefix, id)
	var resp PromptConfigurationReferenceRelationship
	err := m.QueryRowCtx(ctx, &resp, managerPromptConfigurationReferenceRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", promptConfigurationReferenceRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPromptConfigurationReferenceRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *PromptConfigurationReferenceRelationship) (sql.Result, error) {
	managerPromptConfigurationReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPromptConfigurationReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, promptConfigurationReferenceRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerPromptConfigurationReferenceRelationshipIdKey)
}

func (m *defaultPromptConfigurationReferenceRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *PromptConfigurationReferenceRelationship) (sql.Result, error) {

	managerPromptConfigurationReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPromptConfigurationReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, promptConfigurationReferenceRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerPromptConfigurationReferenceRelationshipIdKey)
}

func (m *defaultPromptConfigurationReferenceRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerPromptConfigurationReferenceRelationshipIdPrefix, primary)
}

func (m *defaultPromptConfigurationReferenceRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", promptConfigurationReferenceRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPromptConfigurationReferenceRelationshipModel) tableName() string {
	return m.table
}
