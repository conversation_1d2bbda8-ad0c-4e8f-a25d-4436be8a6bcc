syntax = "proto3";

package dispatcher;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb";

import "common/config.proto";
import "common/enum.proto";
import "common/ui_agent.proto";
import "manager/base.proto";
import "manager/manager.proto";
import "manager/component.proto";
import "dispatcher/base.proto";


enum PublishType {
  PublishType_UNKNOWN = 0;

  PublishType_API_COMPONENT_GROUP = 1; // API组件组
  PublishType_API_CASE = 2; // API测试用例
  PublishType_API_SUITE = 3; // API测试集合
  PublishType_API_PLAN = 4; // API测试计划
  PublishType_INTERFACE_CASE = 5; // 接口用例
  PublishType_INTERFACE_DOCUMENT = 6; // 接口文档（即接口集合）
  PublishType_UI_CASE = 7;  // UI测试用例
  PublishType_UI_SUITE = 8; // UI测试集合
  PublishType_UI_PLAN = 9;  // UI测试计划
  PublishType_API_PRECISION_SUITE = 10;  // Deprecated: API精准测试集合
  PublishType_PRECISION_INTERFACE_DOCUMENT = 11;  // Deprecated: API精准测试接口文档
  PublishType_API_SERVICE = 12; // API精准测试服务集合
  PublishType_PERF_CASE = 13; // 压力测试用例
  PublishType_PERF_SUITE = 14; // 压力测试集合
  PublishType_PERF_PLAN = 15; // 压力测试计划
  PublishType_STABILITY_PLAN = 16; // 稳定性测试计划
  PublishType_UI_AGENT_COMPONENT = 17; // UI Agent组件
  PublishType_UI_AGENT_CASE = 18; // UI Agent用例
  PublishType_UI_AGENT_SUITE = 19; // UI Agent集合
  PublishType_UI_AGENT_PLAN = 20; // UI Agent计划
}

message PlanPublishInfo {
  string plan_id = 1;
  string callback_url = 2;
  int64 callback_timeout = 3;
//  repeated string services = 4;
  ApiPlanInfo api_plan_info = 5;

  reserved 4;
}

message InterfaceDocumentPublishInfo {
  manager.ExecutionMode case_execution_mode = 1; // 用例执行方式
  string interface_document_id = 2; // 接口文档ID
  string interface_execute_id = 3;  // 接口执行ID
  common.GeneralConfig general_config = 4;  // 通用配置信息
  repeated common.AccountConfig account_config = 5; // 池账号信息
  repeated manager.ApiExecutionData interface_cases = 6;  // 接口用例列表
  string plan_id = 7; // 计划ID
  string plan_execute_id = 8; // 计划执行ID
  string plan_name = 9; // 计划名称
}

message SuitePublishInfo {
  manager.ExecutionMode case_execution_mode = 1; // 用例执行方式
  string suite_id = 2; // 集合ID
  string suite_execute_id = 3;  // 集合执行ID
  common.GeneralConfig general_config = 4;  // 通用配置信息
  repeated common.AccountConfig account_config = 5; // 池账号信息
  string plan_id = 6; // 计划ID
  string plan_execute_id = 7; // 计划执行ID
  repeated manager.ApiExecutionData cases = 8;  // 用例列表
  string plan_name = 9; // 计划名称
}

message CasePublishInfo {
  string case_id = 1; // 用例ID
  string case_execute_id = 2;  // 用例执行ID
  common.GeneralConfig general_config = 3;  // 通用配置信息
  repeated common.AccountConfig account_config = 4; // 池账号信息
  string version = 5; // 版本
  string suite_id = 6;  // 集合ID
  string suite_execute_id = 7; // 集合执行ID

  string case_name = 21; // 用例名称
  string maintained_by = 22; // 用例维护人ID
  string suite_name = 23; // 集合名称
  string plan_id = 24; // 计划ID
  string plan_execute_id = 25; // 计划执行ID
  string plan_name = 26; // 计划名称
}

message InterfaceCasePublishInfo {
  string interface_case_id = 1; // 接口用例ID
  string interface_case_execute_id = 2;  // 接口用例执行ID
  common.GeneralConfig general_config = 3;  // 通用配置信息
  repeated common.AccountConfig account_config = 4; // 池账号信息
  string version = 5; // 版本
  string interface_id = 6;  // 接口ID
  string interface_execute_id = 7; // 接口执行ID
  string document_id = 8; // 接口文档ID

  string case_name = 21; // 接口用例名称
  string maintained_by = 22; // 接口用例维护人ID
  string document_name = 23; // 接口文档名称
  string plan_id = 24; // 计划ID
  string plan_execute_id = 25; // 计划执行ID
  string plan_name = 26; // 计划名称
}

message ComponentGroupPublishInfo {
  string component_group_id = 1; // 业务组件ID
  string component_group_execute_id = 2;  // 业务组件执行ID
  common.GeneralConfig general_config = 3;  // 通用配置信息
  repeated common.AccountConfig account_config = 4; // 池账号信息
  string version = 5; // 版本
}

message UICasePublishInfo {
  string ui_case_id = 1; // 用例id
  string ui_case_execute_id = 2;  // 用例执行id
  string ui_suite_id = 3;  // 父集合id
  string ui_suite_execute_id = 4; // 父集合执行id
  string ui_plan_id = 5; // 父计划id
  string ui_plan_execute_id = 6; // 父计划执行id
  manager.ApiExecutionData ui_case = 7;
  manager.UIPlanMetaData meta_data = 8; // ui用例数据
}

message UISuitePublishInfo {
  manager.ExecutionMode case_execution_mode = 1; // 用例执行方式
  string ui_suite_id = 2; // 集合id
  string ui_suite_execute_id = 3;  // 集合执行id
  string ui_plan_id = 4; // 父计划id
  string ui_plan_execute_id = 5; // 父计划执行id
  repeated manager.ApiExecutionData ui_cases = 6;  // 用例列表
  manager.ApiExecutionData ui_suite = 7;
  manager.UIPlanMetaData meta_data = 8; // ui集合数据
}

message UIPlanPublishInfo {
  string ui_plan_id = 1;
  string callback_url = 2;
  int64 callback_timeout = 3;
  UiPlanInfo ui_plan_info = 4;
}

message PrecisionSuitePublishInfo {
  manager.ExecutionMode case_execution_mode = 1; // 用例执行方式
  string suite_id = 2; // 集合ID
  string suite_execute_id = 3;  // 集合执行ID
  common.GeneralConfig general_config = 4;  // 通用配置信息
  repeated common.AccountConfig account_config = 5; // 池账号信息
  string plan_id = 6; // 计划ID
  string plan_execute_id = 7; // 计划执行ID
  repeated manager.ApiExecutionData cases = 8;  // 用例列表
  string suite_name = 9; // 集合名称
  string plan_name = 10; // 计划名称
}

message PrecisionInterfaceDocumentPublishInfo {
  manager.ExecutionMode case_execution_mode = 1; // 用例执行方式
  string interface_document_id = 2; // 接口文档ID
  string interface_execute_id = 3;  // 接口执行ID
  common.GeneralConfig general_config = 4;  // 通用配置信息
  repeated common.AccountConfig account_config = 5; // 池账号信息
  repeated manager.ApiExecutionData interface_cases = 6;  // 接口用例列表
  string plan_id = 7; // 计划ID
  string plan_execute_id = 8; // 计划执行ID
  string document_name = 9; // 接口文档名称
  string plan_name = 10; // 计划名称
}

message ServicePublishInfo {
  string service_id = 1; // 服务ID
  string service_execute_id = 2; // 服务执行ID
  string service_name = 3; // 服务名称
  common.GeneralConfig general_config = 4; // 通用配置信息
  repeated common.AccountConfig account_config = 5; // 池账号信息
  string plan_id = 6; // 计划ID
  string plan_execute_id = 7; // 计划执行ID
  repeated manager.ApiExecutionData cases = 8; // 用例列表
  repeated manager.ApiExecutionData interface_cases = 9; // 接口用例列表
  string plan_name = 10; // 计划名称
}

message PerfCasePublishInfo {
  string perf_case_id = 1; // 用例ID
  string perf_case_execute_id = 2;  // 用例执行ID
  string perf_suite_id = 3;  // 集合ID
  string perf_suite_execute_id = 4; // 集合执行ID
  string perf_plan_id = 5; // 计划ID
  string perf_plan_execute_id = 6; // 计划执行ID

  manager.ApiExecutionData perf_case = 11; // 用例数据
  manager.PerfPlanMetaData meta_data = 12; // 计划元数据

  PerfPlanInfo perf_plan_info = 21; // 计划执行信息
}

message PerfSuitePublishInfo {
  string perf_suite_id = 1; // 集合ID
  string perf_suite_execute_id = 2; // 集合执行ID
  string perf_plan_id = 3; // 计划ID
  string perf_plan_execute_id = 4; // 计划执行ID

  manager.ApiExecutionData perf_suite = 11; // 集合数据
  repeated manager.ApiExecutionData perf_cases = 12; // 用例数据
  manager.ExecutionMode case_execution_mode = 13; // 用例执行方式
  manager.PerfPlanMetaData meta_data = 14; // 计划元数据

  PerfPlanInfo perf_plan_info = 21; // 计划执行信息
}

message PerfPlanPublishInfo {
  string perf_plan_id = 1; // 计划ID

  string callback_url = 11; // 回调地址
  int64 callback_timeout = 12; // 回调超时时间

  PerfPlanInfo perf_plan_info = 21; // 计划执行信息
}

message StabilityPlanPublishInfo {
  string stability_plan_id = 1;
}

message UIAgentComponentPublishInfo {
  string component_id = 1; // UI Agent组件ID
  string component_name = 2; // 组件名称
  string application_id = 3; // 应用配置ID
  common.UIAgentMode mode = 4; // 模式（Agent模式、Step模式）
  repeated common.UIAgentComponentStep steps = 5; // 步骤列表
  common.UIAgentComponentExpectation expectation = 6; // 期望结果
  repeated common.GeneralConfigVar variables = 7; // 变量列表
  UIAgentDeviceInfo device = 8; // 设备信息
  bool reinstall = 9; // 是否重新安装
  bool restart = 10; // 是否重启应用
  string reference_id = 11; // 参考配置ID
}
