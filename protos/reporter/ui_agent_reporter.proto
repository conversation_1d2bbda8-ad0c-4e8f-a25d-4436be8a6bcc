syntax = "proto3";

package reporter;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb";

import "common/config.proto";
import "common/enum.proto";
import "common/ui_agent.proto";
import "reporter/common.proto";


message UIAgentComponentRecordItem {
  string task_id = 1; // 任务ID
  string execute_id = 2; // 执行ID
  string parent_execute_id = 3; // 父执行ID

  string project_id = 11; // 项目ID
  string component_id = 12; // 组件ID
  string component_name = 13; // 组件名称
  common.TriggerMode trigger_mode = 14; // 触发模式
  common.ExecuteType execute_type = 15; // 执行类型

  common.ApplicationConfig application_config = 21; // 应用配置
  common.UIAgentMode mode = 22; // 模式（Agent模式、Step模式）
  repeated common.UIAgentComponentStep steps = 23; // 步骤列表
  common.UIAgentComponentExpectation expectation = 24; // 期望结果
  repeated common.GeneralConfigVar variables = 25; // 变量列表
  common.UIAgentDevice device = 26; // 设备信息
  bool reinstall = 27; // 是否重新安装
  bool restart = 28; // 是否重启应用
  string reference_id = 29; // 参考配置ID

  string status = 41; // 执行状态
  string executed_by = 42; // 执行者
  int64 started_at = 43; // 开始时间
  int64 ended_at = 44; // 结束时间
  int64 cost_time = 45; // 执行耗时
  ErrorMessage err_msg = 46; // 错误信息

  bool cleaned = 91; // 是否已被清理
  int64 created_at = 92; // 创建时间
  int64 updated_at = 93; // 更新时间
}
