syntax = "proto3";

package common;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb";

import "common/enum.proto";


message GeneralConfigVar {
  string key = 1;
  string value = 2;
}

message GeneralConfig {
  string project_id = 1;
  string config_id = 2;
  string name = 3;
  string description = 4;
  string base_url = 5;
  bool verify = 6;
  repeated GeneralConfigVar variables = 7;
}

message AccountConfig {
  string project_id = 1;
  string config_id = 2;
  string name = 3;
  string description = 4;
  int64 product_type = 5;
  string product_name = 6;
  string pool_env_table = 7;
  string pool_env_name = 8;
}

message GitConfig {
  string project_id = 1; // 项目ID
  string config_id = 2; // Git配置ID
  string type = 3; // Git类型（GitLab、GitHub、Gitee）
  string name = 4; // Git配置名称
  string description = 5; // Git配置描述
  string url = 6; // Git项目URL（包括访问令牌）
  string access_token = 7; // Git项目访问令牌
  string branch = 8; // Git项目分支名称
}

message ProtobufConfig {
  string project_id = 1; // 项目ID
  string config_id = 2; // Protobuf配置ID
  string name = 3; // Protobuf配置名称
  string description = 4; // Protobuf配置描述
  GitConfig git_config = 5; // Git配置
  string import_path = 6; // 导入路径
  repeated string exclude_paths = 7; // 排除的路径列表
  repeated string exclude_files = 8; // 排除的文件列表
  repeated ProtobufDependenceConfig dependencies = 9; // 依赖列表
}

message ProtobufDependenceConfig {
  string project_id = 1; // 项目ID
  string config_id = 2; // Protobuf配置ID
  string name = 3; // Protobuf配置名称
  string description = 4; // Protobuf配置描述
  GitConfig git_config = 5; // Git配置
  string import_path = 6; // 导入路径
}

message PromptConfig {
  string project_id = 1; // 项目ID
  string config_id = 2; // Prompt配置ID
  common.PromptPurpose purpose = 3; // 用途（1: UI_AGENT）
  common.PromptCategory category = 4; // 分类（1: 背景、2: UI组件、3: 异常处理）

  string name = 11; // Prompt配置名称
  string description = 12; // Prompt配置描述
  string content = 13; // Prompt配置内容
}

message ApplicationConfig {
  string project_id = 1; // 项目ID
  string config_id = 2; // 应用配置ID

  string name = 11; // 应用配置名称
  string description = 12; // 应用配置描述
  common.PlatformType platform_type = 13; // 平台类型（Android、IOS）
  string app_id = 14; // 应用ID（Android：package_name；IOS：bundle_id）
  string app_download_link = 15; // APP下载地址

  repeated PromptConfig prompts = 21; // Prompt配置列表
}
