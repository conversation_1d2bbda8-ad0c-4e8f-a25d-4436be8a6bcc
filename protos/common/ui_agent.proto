syntax = "proto3";

package common;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb";

import "validate/validate.proto";

import "common/enum.proto";


// UIAgentComponentExpectation UIAgent组件的期望结果
message UIAgentComponentExpectation {
  string text = 1; // 文本
  string image = 2 [(validate.rules).string = {ignore_empty: true pattern: "(?:^ui_agent_image_id:.+?)"}]; // 图片
}

// UIAgentComponentStep UIAgent组件的步骤
message UIAgentComponentStep {
  string content = 1 [(validate.rules).string = {min_len: 1}]; // 步骤内容
  UIAgentComponentExpectation expectation = 2 [(validate.rules).message = {required: true}]; // 期望结果
  float waiting_time = 3 [(validate.rules).float = {gte: 0, lte: 300}]; // 步骤完成后的等待时间
}

// UIAgentDevice UIAgent组件的设备信息
message UIAgentDevice {
  message ProjectDevice {
    DeviceType device_type = 1 [(validate.rules).enum = {not_in: [0]}]; // 设备类型
    PlatformType platform_type = 2 [(validate.rules).enum = {not_in: [0]}]; // 平台类型
    string udid = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 设备ID
    string remote_address = 4 [(validate.rules).string = {min_len: 1}]; // 设备远程连接地址
    string token = 5 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 占用设备的令牌
  }
  message UserDevice {
    DeviceType device_type = 1 [(validate.rules).enum = {not_in: [0]}]; // 设备类型
    PlatformType platform_type = 2 [(validate.rules).enum = {not_in: [0]}]; // 平台类型
    string udid = 3 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 设备ID
    string remote_address = 4 [(validate.rules).string = {min_len: 1}]; // 设备远程连接地址
  }

  oneof device {
    ProjectDevice project_device = 1 [(validate.rules).message = {required: true}];
    UserDevice user_device = 2 [(validate.rules).message = {required: true}];
  }
}
