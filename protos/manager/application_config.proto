syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "common/enum.proto";
import "manager/prompt_config.proto";


message ApplicationConfiguration {
  string project_id = 1; // 项目ID
  string config_id = 2; // 应用配置ID

  string name = 11; // 应用配置名称
  string description = 12; // 应用配置描述
  common.PlatformType platform_type = 13; // 平台类型（Android、IOS）
  string app_id = 14; // 应用ID（Android：package_name；IOS：bundle_id）
  string app_download_link = 15; // APP下载地址

  repeated PromptConfiguration prompts = 21; // Prompt配置列表

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64  created_at = 98; // 创建时间
  int64  updated_at = 99; // 更新时间
}

message SearchApplicationConfigurationReferenceItem {
  string project_id = 1; // 项目ID
  string config_id = 2; // 应用配置ID

  string reference_type = 11; // 引用对象类型
  string reference_id = 12; // 引用对象ID
  string name = 13; // 引用对象名称
  string description = 14; // 引用对象描述

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64  created_at = 98; // 创建时间
  int64  updated_at = 99; // 更新时间
}
