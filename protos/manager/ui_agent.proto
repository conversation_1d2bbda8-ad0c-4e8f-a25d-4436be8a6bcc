syntax = "proto3";

package manager;

import "validate/validate.proto";

import "common/enum.proto";
import "common/config.proto";
import "common/ui_agent.proto";
import "manager/base.proto";

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";


// UIAgentComponent `UI Agent`组件
message UIAgentComponent {
  string project_id = 1; // 项目ID
  string category_id = 2; // 所属分类ID
  string component_id = 3; // 组件ID

  string name = 11; // 组件名称
  string description = 12; // 组件描述
  repeated string tags = 13; // 标签
  common.PlatformType platform_type = 14; // 平台类型
  string application_id = 15; // 应用配置ID
  common.UIAgentMode mode = 16; // 模式（Agent模式、Step模式）
  repeated common.UIAgentComponentStep agent_mode_steps = 17; // Agent模式的步骤列表
  repeated common.UIAgentComponentStep step_mode_steps = 18; // Step模式的步骤列表
  common.UIAgentComponentExpectation expectation = 19; // 期望结果
  repeated common.GeneralConfigVar variables = 20; // 变量列表
  string reference_id = 21; // 参考配置ID

  int64 latest_executed_at = 31; // 最近一次执行时间
  common.ExecutedResult latest_result = 32; // 最近一次结果

  CommonState state = 41; // 状态
  string maintained_by = 42; // 维护者

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64 created_at = 98; // 创建时间
  int64 updated_at = 99; // 更新时间
}

message DeleteUnusedUIAgentImageTaskInfo {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
}

message UpdateUIAgentComponentResultTaskInfo {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string component_id = 2 [(validate.rules).string = {pattern: "(?:^ui_agent_component_id:.+?)"}]; // 组件ID

  int64 executed_at = 11; // 执行时间
  common.ExecutedResult result = 12; // 执行结果
}
