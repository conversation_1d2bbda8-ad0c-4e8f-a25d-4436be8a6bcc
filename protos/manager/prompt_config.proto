syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "common/enum.proto";


message PromptConfiguration {
  string project_id = 1; // 项目ID
  string config_id = 2; // Prompt配置ID
  common.PromptPurpose purpose = 3; // 用途（1: UI_AGENT）
  common.PromptCategory category = 4; // 分类（1: 背景、2: UI组件、3: 异常处理）

  string name = 11; // Prompt配置名称
  string description = 12; // Prompt配置描述
  string content = 13; // Prompt配置内容

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64  created_at = 98; // 创建时间
  int64  updated_at = 99; // 更新时间
}

message SearchPromptConfigurationReferenceItem {
  string project_id = 1; // 项目ID
  string config_id = 2; // Prompt配置ID

  string reference_type = 11; // 引用对象类型
  string reference_id = 12; // 引用对象ID
  string name = 13; // 引用对象名称
  string description = 14; // 引用对象描述

  string created_by = 96; // 创建者
  string updated_by = 97; // 更新者
  int64  created_at = 98; // 创建时间
  int64  updated_at = 99; // 更新时间
}
