package discoveryservicelogic

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/electricbubble/gadb"
	"github.com/pkg/errors"
	"github.com/shogo82148/androidbinary/apk"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/set"

	gu2 "gitlab.ttyuyin.com/TestDevelopment/go-uiautomator2"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/pb"
)

var (
	manualConfirmationTexts = []string{"允许安装", "继续安装", "安装", "确定", "完成"} // Deprecated: use `manualConfirmationItems` instead
	manualConfirmationItems = []*manualConfirmationItem{
		{
			action: func(logic *InstallAppLogic, driver *gu2.Driver, serial string) {
				name := "已了解应用的风险检测结果"
				element := driver.FindElementBySelectorOptions(gu2.ByText(name))
				if element == nil {
					return
				} else if ok, err := element.Exist(); err != nil || !ok {
					return
				}

				info, err := element.Info()
				if err != nil {
					return
				}

				if info.Checkable && info.Checked {
					return
				}

				if err = element.Click(gu2.WithTimeout(time.Second)); err != nil {
					return
				}

				logic.Debugf("click the element successfully, serial: %s, text: %s", serial, name)
			},
		},
		{
			action: func(logic *InstallAppLogic, driver *gu2.Driver, serial string) {
				logic.defaultManualConfirmationAction(driver, "允许安装", serial, gu2.ByText("允许安装"))
			},
		},
		{
			action: func(logic *InstallAppLogic, driver *gu2.Driver, serial string) {
				logic.defaultManualConfirmationAction(driver, "继续安装", serial, gu2.ByText("继续安装"))
			},
		},
		{
			action: func(logic *InstallAppLogic, driver *gu2.Driver, serial string) {
				logic.defaultManualConfirmationAction(driver, "安装", serial, gu2.ByText("安装"))
			},
		},
		{
			action: func(logic *InstallAppLogic, driver *gu2.Driver, serial string) {
				logic.defaultManualConfirmationAction(driver, "确定", serial, gu2.ByText("确定"))
			},
		},
		{
			action: func(logic *InstallAppLogic, driver *gu2.Driver, serial string) {
				logic.defaultManualConfirmationAction(driver, "完成", serial, gu2.ByText("完成"))
			},
		},
	}
)

type manualConfirmationItem struct {
	action func(logic *InstallAppLogic, driver *gu2.Driver, serial string)
}

type InstallAppLogic struct {
	*BaseLogic
}

func NewInstallAppLogic(ctx context.Context, svcCtx *svc.ServiceContext) *InstallAppLogic {
	return &InstallAppLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// InstallApp 安装应用
func (l *InstallAppLogic) InstallApp(in *pb.InstallAppReq) (out *pb.InstallAppResp, err error) {
	var (
		platformType  = in.GetPlatformType()
		udid          = in.GetUdid()
		remoteAddress = in.GetRemoteAddress()
		filename      = in.GetFilename()
	)
	l.Infof(
		"install app, req: %s, user: %s", protobuf.MarshalJSONIgnoreError(in), jsonx.MarshalIgnoreError(l.currentUser),
	)

	filePath := filepath.Join(l.svcCtx.Config.LocalPath, dirNameOfDownload, filename)
	if !qetutils.Exists(filePath) {
		return nil, errorx.Errorf(errorx.NotExists, "the app package doesn't exist, filename: %s", filename)
	}

	switch platformType {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		err = l.installApk(udid, remoteAddress, filePath)
	case commonpb.PlatformType_IOS:
		err = l.installIpa(udid, remoteAddress, filePath)
	default:
		err = errorx.Errorf(errorx.DoesNotSupport, "unknown platform type: %s", protobuf.GetEnumStringOf(platformType))
	}
	if err != nil {
		return nil, err
	}

	return &pb.InstallAppResp{}, nil
}

func (l *InstallAppLogic) installApk(serial, remoteAddress, apkPath string) error {
	_, _, device, err := utils.ADBConnect(serial, remoteAddress)
	if err != nil {
		return err
	}

	var (
		needToReinstall, needToInstall bool

		driver *gu2.Driver
		exitCh chan lang.PlaceholderType
	)

	defer func() {
		if exitCh != nil {
			close(exitCh)
		}
		if driver != nil {
			_ = driver.Close()
		}
	}()

	info, err := apk.OpenFile(apkPath)
	if err != nil {
		return errors.Wrapf(err, "failed to open the apk file, serial: %s, file: %s", serial, apkPath)
	}

	packageName := info.PackageName()
	packages, err := l.listPackages(serial, packageName, device)
	if err != nil {
		return err
	}

	var cmd, output string
	if packages.Size() != 0 {
		cmd = fmt.Sprintf(commandOfGetVersion, packageName)
		output, err = device.RunShellCommand(cmd)
		if err != nil {
			return errors.Wrapf(
				err, "failed to get the version of the app, serial: %s, package_name: %s", serial, packageName,
			)
		}

		source := strings.TrimSpace(output)
		source = strings.TrimPrefix(source, "versionName=")
		target := info.Manifest().VersionName

		if !strings.EqualFold(source, target) {
			l.Infof(
				"need to reinstall the app, serial: %s, file: %s, package_name: %s, version: %s => %s",
				serial, apkPath, info.PackageName(), source, target,
			)
			needToInstall = true
			needToReinstall = true
		}
	} else {
		needToInstall = true
	}

	if needToInstall {
		if needToReinstall {
			cmd = fmt.Sprintf(commandOfUninstallApp, packageName)
			if _, err = device.RunShellCommand(cmd); err != nil {
				return errors.Wrapf(
					err, "failed to uninstall the app, serial: %s, package_name: %s", serial, packageName,
				)
			}
		}

		pathOnDevice := filepath.Join(basePathOnAndroid, fmt.Sprintf("%s.apk", info.PackageName()))
		if err = device.PushFile(apkPath, pathOnDevice); err != nil {
			return errors.Wrapf(
				err,
				"failed to push apk file to the device, serial: %s, file: %s, path: %s",
				serial, apkPath, pathOnDevice,
			)
		}

		// if the device is an Android real phone, then try to check whether
		// it requires manual confirmation when installing an app.
		driver, err = gu2.NewDriver(device)
		if err != nil {
			l.Errorf("failed to new uiautomator2 driver, serial: %s, error: %+v", serial, err)
		} else {
			exitCh = make(chan lang.PlaceholderType)
			threading.GoSafeCtx(
				l.ctx, func() {
					l.manualConfirmation(serial, driver, exitCh)
				},
			)
		}

		cmd = fmt.Sprintf(commandOfInstallApp, pathOnDevice)
		output, err = device.RunShellCommand(cmd)
		if err != nil {
			return errors.Wrapf(err, "failed to run the shell command, serial: %s, command: %q", serial, cmd)
		}

		// sleep for 3 seconds to avoid missing the pop-up window after installing the app.
		time.Sleep(3 * time.Second)
		l.Infof(
			"succeed to install the app, serial: %s, file: %s, package_name: %s, version: %s, command: %q, result: %s",
			serial, apkPath, info.PackageName(), info.Manifest().VersionName, cmd, output,
		)
	} else {
		l.Debugf(
			"the app has been installed, serial: %s, file: %s, package_name: %s, version: %s",
			serial, apkPath, info.PackageName(), info.Manifest().VersionName,
		)
	}

	return nil
}

func (l *InstallAppLogic) listPackages(serial, packageName string, device *gadb.Device) (*set.Set[string], error) {
	cmd := commandOfListPackages
	if packageName != "" {
		cmd += " " + packageName
	}

	output, err := device.RunShellCommand(cmd)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to list the packages, serial: %s", serial)
	}

	output = strings.TrimSpace(output)
	lines := strings.Split(output, "\n")
	packages := set.NewHashset(uint64(len(lines)), generic.Equals, generic.HashString)
	for _, line := range lines {
		if line == "" {
			continue
		}

		fields := strings.Split(line, ":")
		if len(fields) < 2 {
			continue
		}

		packages.Put(fields[1])
	}

	return &packages, nil
}

func (l *InstallAppLogic) manualConfirmation(serial string, driver *gu2.Driver, exitCh <-chan lang.PlaceholderType) {
	for {
		select {
		case <-l.ctx.Done():
			l.Debugf("got a done signal while installing the app, serial: %s, error: %+v", serial, l.ctx.Err())
			return
		case <-exitCh:
			l.Debugf("got an exit signal while installing the app, serial: %s", serial)
			return
		default:
			for _, item := range manualConfirmationItems {
				if item == nil || item.action == nil {
					continue
				}

				item.action(l, driver, serial)
			}
			//for _, text := range manualConfirmationTexts {
			//	element := driver.FindElementBySelectorOptions(gu2.ByText(text))
			//	if element == nil {
			//		continue
			//	} else if ok, err := element.Exist(); err != nil || !ok {
			//		continue
			//	} else if err = element.Click(gu2.WithTimeout(time.Second)); err != nil {
			//		continue
			//	}
			//
			//	l.Debugf("click th element successfully, serial: %s, text: %s", serial, text)
			//	break
			//}

			time.Sleep(time.Second)
		}
	}
}

func (l *InstallAppLogic) defaultManualConfirmationAction(
	driver *gu2.Driver, name, serial string, selector gu2.SelectorOption,
) {
	element := driver.FindElementBySelectorOptions(selector)
	if element == nil {
		return
	} else if ok, err := element.Exist(); err != nil || !ok {
		return
	} else if err = element.Click(gu2.WithTimeout(time.Second)); err != nil {
		return
	}

	l.Debugf("click the element successfully, serial: %s, name: %s", serial, name)
}

func (l *InstallAppLogic) installIpa(bundleID, remoteAddress, ipaPath string) error {
	return nil
}
