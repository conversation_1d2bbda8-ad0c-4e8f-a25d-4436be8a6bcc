package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
)

var (
	_                                 ExecutionRecordModel = (*customExecutionRecordModel)(nil)
	executionRecordRowsExpectAutoSet2                      = strings.Join(
		stringx.Remove(
			executionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), ",",
	)
	executionRecordRowsWithPlaceHolder2 = strings.Join(
		stringx.Remove(
			executionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), "=?,",
	) + "=?"
)

type TotalRecord struct {
	Count uint64 `db:"count"`
}

type (
	// ExecutionRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customExecutionRecordModel.
	ExecutionRecordModel interface {
		executionRecordModel
		types.DBModel

		SelectBuilder() squirrel.SelectBuilder
		SelectBuilderWithEmptyContent() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder

		InsertRecordWithDefaultCreatedAt(ctx context.Context, data *ExecutionRecord) (sql.Result, error)
		UpdateRecordWithDefaultUpdatedAt(ctx context.Context, data *ExecutionRecord) error

		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*ExecutionRecord, error)

		FindExecutionRecordByExecuteType(ctx context.Context, ct string) ([]*RedundantRecord, error)
		DeleteRecordByTaskId(ctx context.Context, session sqlx.Session, taskID, projectID, cleanType string) error
		SetRecordHasCleaned(ctx context.Context, session sqlx.Session, taskID, projectID string) error
		DeleteBeforeNDaysRecords(ctx context.Context, session sqlx.Session, days int) error

		FindComponentSelfRecord(
			ctx context.Context, taskId, projectId, ExecuteId, ComponentExecuteId string, times int64,
		) ([]*ComponentSelfRecord, error)
		FindComponentExecuteRecords(
			ctx context.Context, taskId, projectId, ParentComponentExecuteId string, times int64,
		) ([]*ComponentExecuteRecord, error)
	}

	customExecutionRecordModel struct {
		*defaultExecutionRecordModel
	}
)

type (
	ComponentSelfRecord struct {
		ExecutedBy string         `db:"executed_by"` // 执行人
		Status     sql.NullString `db:"status"`      // 执行状态（结果）
		StartedAt  int64          `db:"started_at"`  // 开始执行的时间(戳)
		EndedAt    sql.NullInt64  `db:"ended_at"`    // 结束执行的时间(戳)
		CostTime   int64          `db:"cost_time"`   // 执行耗时(毫秒)
		Times      int64          `db:"times"`       // 第几次执行（兼容循环组件）
	}

	ComponentExecuteRecord struct {
		TaskId                   string         `db:"task_id"`                     // 任务id
		ProjectId                string         `db:"project_id"`                  // 项目id
		ExecuteId                string         `db:"execute_id"`                  // 执行id
		ComponentId              string         `db:"component_id"`                // 组件id
		ComponentExecuteId       string         `db:"component_execute_id"`        // 组件执行id
		ParentComponentId        sql.NullString `db:"parent_component_id"`         // 父组件id
		ParentComponentExecuteId sql.NullString `db:"parent_component_execute_id"` // 父组件执行id
		Version                  string         `db:"version"`                     // 组件版本
		Times                    int64          `db:"times"`                       // 第几次执行（兼容循环组件）
		Status                   sql.NullString `db:"status"`                      // 执行状态（结果）
		StartedAt                int64          `db:"started_at"`                  // 开始执行的时间(戳)
		EndedAt                  sql.NullInt64  `db:"ended_at"`                    // 结束执行的时间(戳)
		CostTime                 int64          `db:"cost_time"`                   // 执行耗时(毫秒)
	}
)

func (m *defaultExecutionRecordModel) Table() string {
	return m.table
}

func (m *defaultExecutionRecordModel) Fields() []string {
	return executionRecordFieldNames
}

func (m *defaultExecutionRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(executionRecordFieldNames...).From(m.table)
}

func (m *defaultExecutionRecordModel) SelectBuilderWithEmptyContent() squirrel.SelectBuilder {
	fieldNames := stringx.Remove(executionRecordFieldNames, "`content`")
	fieldNames = append(fieldNames, "'' as content")
	return squirrel.Select(fieldNames...).From(m.table)
}

func (m *defaultExecutionRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(1)").From(m.table)
}

func (m *defaultExecutionRecordModel) InsertRecordWithDefaultCreatedAt(
	ctx context.Context, data *ExecutionRecord,
) (sql.Result, error) {
	query := fmt.Sprintf(
		"insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		m.table, executionRecordRowsExpectAutoSet2,
	)
	ret, err := m.conn.ExecCtx(
		ctx, query,
		data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig, data.AccountConfig,
		data.ComponentId, data.ComponentName, data.ComponentType, data.ComponentExecuteId, data.ParentComponentId,
		data.ParentComponentExecuteId, data.Version, data.Times, data.Status, data.Content,
		data.IsRoot, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback,
		data.Cleaned, data.MaintainedBy,
	)
	return ret, err
}

func (m *defaultExecutionRecordModel) UpdateRecordWithDefaultUpdatedAt(
	ctx context.Context, data *ExecutionRecord,
) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, executionRecordRowsWithPlaceHolder2)
	_, err := m.conn.ExecCtx(
		ctx, query,
		data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig, data.AccountConfig,
		data.ComponentId, data.ComponentName, data.ComponentType, data.ComponentExecuteId, data.ParentComponentId,
		data.ParentComponentExecuteId, data.Version, data.Times, data.Status, data.Content,
		data.IsRoot, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback,
		data.Cleaned, data.MaintainedBy, data.Id,
	)
	return err
}

func (m *defaultExecutionRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultExecutionRecordModel) FindByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*ExecutionRecord, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ExecutionRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

// NewExecutionRecordModel returns a model for the database table.
func NewExecutionRecordModel(conn sqlx.SqlConn) ExecutionRecordModel {
	return &customExecutionRecordModel{
		defaultExecutionRecordModel: newExecutionRecordModel(conn),
	}
}

func (m *customExecutionRecordModel) FindExecutionRecordByExecuteType(
	ctx context.Context, ct string,
) ([]*RedundantRecord, error) {
	/*
		SQL:
		SELECT `task_id`,
		       `execute_id`,
		       `project_id`,
		       `component_id` as `execute_type_id`,
		       `created_at`
		FROM execution_record
		WHERE execute_type = ?
		  AND is_root = 1
		  AND cleaned = ?
		ORDER BY created_at DESC;
	*/
	fields := []string{
		"`task_id`",
		"`execute_id`",
		"`project_id`",
		"`component_id` as `execute_type_id`",
		"`created_at`",
	}
	var resp []*RedundantRecord
	query, values, err := squirrel.Select(fields...).Where(
		"execute_type = ? AND is_root = 1 AND `cleaned` = ?", ct, common.NotCleaned,
	).OrderBy("created_at DESC").From(m.tableName()).ToSql()
	if err != nil {
		return nil, err
	}

	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customExecutionRecordModel) DeleteRecordByTaskId(
	ctx context.Context, session sqlx.Session, taskID, projectID, cleanType string,
) error {
	var (
		deleteBuilder squirrel.DeleteBuilder

		stmt   string
		values []any
		err    error
		count  int64
	)
	deleteBuilder = squirrel.Delete(m.table).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID)

	switch cleanType {
	case constants.CleanTypeApiPlan, constants.CleanTypeApiSuite, constants.CleanTypeInterfaceDocument:
		// DELETE FROM `execution_record` WHERE `task_id` = ? AND `project_id` = ?
		stmt, values, err = deleteBuilder.ToSql()
	case constants.CleanTypeApiCase, constants.CleanTypeInterfaceCase, constants.CleanTypeApiComponentGroup:
		// DELETE FROM `execution_record` WHERE `task_id` = ? AND `project_id` = ? AND `is_root` = 0
		stmt, values, err = deleteBuilder.Where("`is_root` = 0").ToSql()
	default:
		return errors.Errorf("invalid clean type: %s", cleanType)
	}
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
		return err
	}

	// DELETE FROM `execution_record` WHERE `task_id` = ? AND `project_id` = ? LIMIT 500
	count = common.ConstDefaultMaxDeleteItems
	stmt, values, err = deleteBuilder.Limit(uint64(count)).ToSql()
	if err != nil {
		return err
	}

	for count >= common.ConstDefaultMaxDeleteItems {
		result, err := m.conn.ExecCtx(ctx, stmt, values...)
		if err != nil {
			return err
		}

		count, _ = result.RowsAffected()
	}

	return nil
}

func (m *customExecutionRecordModel) SetRecordHasCleaned(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) error {
	stmt, values, err := squirrel.Update(m.table).
		SetMap(
			squirrel.Eq{
				"`cleaned`":    common.HasCleaned,
				"`updated_at`": squirrel.Expr("`updated_at`"),
			},
		).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}

func (m *customExecutionRecordModel) DeleteBeforeNDaysRecords(
	ctx context.Context, session sqlx.Session, days int,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`cleaned` = ? AND `created_at` < ?", common.HasCleaned, time.Now().AddDate(0, 0, -days)).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}

func (m *customExecutionRecordModel) FindComponentSelfRecord(
	ctx context.Context, taskId, projectId, ExecuteId, ComponentExecuteId string, times int64,
) ([]*ComponentSelfRecord, error) {
	/*
			SQL:
			SELECT `executed_by`,
				   `started_at`,
				   `ended_at`,
				   `cost_time`,
				   `status`,
		           `times`
			FROM `execution_record`
			WHERE `task_id` = ?
			  AND `project_id` = ?
			  AND `execute_id` = ?
			  AND `component_execute_id` = ?
			  AND `times` = ?;
	*/
	fields := []string{
		"`executed_by`",
		"`status`",
		"`started_at`",
		"`ended_at`",
		"`cost_time`",
		"`times`",
	}
	var executionRecords []*ComponentSelfRecord
	query, values, err := squirrel.Select(fields...).From(m.tableName()).Where(
		"`task_id` = ? AND `project_id` = ? AND `execute_id` = ? AND `component_execute_id` = ? AND `times` = ? ",
		taskId, projectId, ExecuteId, ComponentExecuteId, times,
	).ToSql()
	if err != nil {
		return nil, err
	}

	err = m.conn.QueryRowsCtx(ctx, &executionRecords, query, values...)
	switch err {
	case nil:
		return executionRecords, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customExecutionRecordModel) FindComponentExecuteRecords(
	ctx context.Context, taskId, projectId, ParentComponentExecuteId string, times int64,
) ([]*ComponentExecuteRecord, error) {
	/*
		SQL:
		SELECT `task_id`,
			   `project_id`,
			   `execute_id`,
			   `component_id`,
			   `component_execute_id`,
			   `parent_component_id`,
			   `parent_component_execute_id`,
			   `version`,
			   `times`,
			   `started_at`,
			   `ended_at`,
			   `cost_time`,
			   `status`
		FROM `execution_record`
		WHERE `task_id` = ?
		  AND `project_id` = ?
		  AND `parent_component_execute_id` = ?
		  AND `times` = ?
		ORDER BY `started_at`;
	*/
	fields := []string{
		"`task_id`",
		"`project_id`",
		"`execute_id`",
		"`component_id`",
		"`component_execute_id`",
		"`parent_component_id`",
		"`parent_component_execute_id`",
		"`version`",
		"`times`",
		"`status`",
		"`started_at`",
		"`ended_at`",
		"`cost_time`",
	}
	var executionRecords []*ComponentExecuteRecord
	query, values, err := squirrel.Select(fields...).From(m.tableName()).Where(
		"`task_id` = ? AND `project_id` = ? AND `parent_component_execute_id` = ? AND `times` = ? ",
		taskId, projectId, ParentComponentExecuteId, times,
	).OrderBy("started_at").ToSql()
	if err != nil {
		return nil, err
	}

	err = m.conn.QueryRowsCtx(ctx, &executionRecords, query, values...)
	switch err {
	case nil:
		return executionRecords, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}
