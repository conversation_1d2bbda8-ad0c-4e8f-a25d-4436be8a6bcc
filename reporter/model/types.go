package model

import (
	"time"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
)

type RedundantRecord struct {
	TaskId        string    `db:"task_id"`
	ExecuteId     string    `db:"execute_id"`
	ProjectId     string    `db:"project_id"`
	ExecuteTypeId string    `db:"execute_type_id"`
	CreateAt      time.Time `db:"created_at"`
}

type FindRedundantRecordsResp struct {
	Items []*RedundantRecord
}

type BaseSearchReq struct {
	ProjectID  string                 `json:"project_id"`
	Condition  sqlbuilder.Condition   `json:"condition"`
	Pagination sqlbuilder.Pagination  `json:"pagination"`
	Sort       []sqlbuilder.SortField `json:"sort"`
}

type SearchUISuiteExecutionRecordReq struct {
	BaseSearchReq

	TaskID    string `json:"task_id"`
	ExecuteID string `json:"execute_id"` // UI计划执行ID
	UDID      string `json:"udid,omitempty"`
}

type SearchUICaseExecutionRecordReq struct {
	BaseSearchReq

	TaskID    string `json:"task_id"`
	ExecuteID string `json:"execute_id"` // UI集合执行ID
	UDID      string `json:"udid,omitempty"`
}

type SearchPerfPlanExecutionRecordReq struct {
	BaseSearchReq

	PlanID string `json:"plan_id"`
}

type SearchPerfCaseExecutionRecordReq struct {
	BaseSearchReq

	TaskID    string `json:"task_id"`
	ExecuteID string `json:"execute_id"` // 压测计划执行ID
	PlanID    string `json:"plan_id"`
}

type SearchUIDeviceExecutionRecordReq struct {
	BaseSearchReq

	TaskID    string `json:"task_id"`
	ExecuteID string `json:"execute_id"` // UI计划执行ID
}

type SearchStabilityPlanExecutionRecordReq struct {
	BaseSearchReq

	PlanID string `json:"plan_id"`
}

type SearchStabilityDeviceExecutionRecordReq struct {
	BaseSearchReq

	TaskID    string `json:"task_id"`
	ExecuteID string `json:"execute_id"` // 稳定性计划执行ID
}

type SearchUIAgentComponentExecutionRecordReq struct {
	BaseSearchReq

	ComponentID string `json:"component_id"`
}
