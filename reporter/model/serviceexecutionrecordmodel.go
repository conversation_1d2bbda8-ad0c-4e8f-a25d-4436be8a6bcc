package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlc"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
)

var (
	_ ServiceExecutionRecordModel = (*customServiceExecutionRecordModel)(nil)

	serviceExecutionRecordRowsExpectAutoSet2 = strings.Join(
		stringx.Remove(
			serviceExecutionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), ",",
	)
	serviceExecutionRecordRowsWithPlaceHolder2 = strings.Join(
		stringx.Remove(
			serviceExecutionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), "=?,",
	) + "=?"
)

type (
	// ServiceExecutionRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customServiceExecutionRecordModel.
	ServiceExecutionRecordModel interface {
		serviceExecutionRecordModel
		types.DBModel

		withSession(session sqlx.Session) ServiceExecutionRecordModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		UpdateBuilder(data *ServiceExecutionRecord) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder

		SelectBuilderWithEmptyContent() squirrel.SelectBuilder

		InsertRecordWithDefaultCreatedAt(ctx context.Context, data *ServiceExecutionRecord) (sql.Result, error)
		UpdateRecordWithDefaultUpdatedAt(ctx context.Context, data *ServiceExecutionRecord) error

		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*ServiceExecutionRecord, error)
		FindServiceExecutionRecord(ctx context.Context) ([]*RedundantRecord, error)
		DeleteRecordByTaskId(ctx context.Context, session sqlx.Session, taskID, projectID string) error
		SetRecordHasCleaned(ctx context.Context, session sqlx.Session, taskID, projectID string) error
		DeleteBeforeNDaysRecords(ctx context.Context, session sqlx.Session, days int) error
	}

	customServiceExecutionRecordModel struct {
		*defaultServiceExecutionRecordModel
	}
)

// NewServiceExecutionRecordModel returns a model for the database table.
func NewServiceExecutionRecordModel(conn sqlx.SqlConn) ServiceExecutionRecordModel {
	return &customServiceExecutionRecordModel{
		defaultServiceExecutionRecordModel: newServiceExecutionRecordModel(conn),
	}
}

func (m *customServiceExecutionRecordModel) withSession(session sqlx.Session) ServiceExecutionRecordModel {
	return NewServiceExecutionRecordModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customServiceExecutionRecordModel) Table() string {
	return m.table
}

func (m *customServiceExecutionRecordModel) Fields() []string {
	return serviceExecutionRecordFieldNames
}

func (m *customServiceExecutionRecordModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customServiceExecutionRecordModel) UpdateBuilder(data *ServiceExecutionRecord) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customServiceExecutionRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(serviceExecutionRecordFieldNames...).From(m.table)
}

func (m *customServiceExecutionRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(1)").From(m.table)
}

func (m *customServiceExecutionRecordModel) SelectBuilderWithEmptyContent() squirrel.SelectBuilder {
	fieldNames := stringx.Remove(serviceExecutionRecordFieldNames, "`content`")
	fieldNames = append(fieldNames, "'' as content")
	return squirrel.Select(fieldNames...).From(m.table)
}

func (m *customServiceExecutionRecordModel) InsertRecordWithDefaultCreatedAt(
	ctx context.Context, data *ServiceExecutionRecord,
) (sql.Result, error) {
	query := fmt.Sprintf(
		"insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		m.table, serviceExecutionRecordRowsExpectAutoSet2,
	)
	ret, err := m.conn.ExecCtx(
		ctx, query,
		data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig,
		data.AccountConfig, data.ServiceId, data.ServiceExecuteId, data.ServiceName, data.PlanExecuteId,
		data.TotalCase, data.SuccessCase, data.FailureCase, data.Status, data.Content,
		data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback, data.Cleaned,
	)
	return ret, err
}

func (m *customServiceExecutionRecordModel) UpdateRecordWithDefaultUpdatedAt(
	ctx context.Context, data *ServiceExecutionRecord,
) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, serviceExecutionRecordRowsWithPlaceHolder2)
	_, err := m.conn.ExecCtx(
		ctx, query,
		data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig,
		data.AccountConfig, data.ServiceId, data.ServiceExecuteId, data.ServiceName, data.PlanExecuteId,
		data.TotalCase, data.SuccessCase, data.FailureCase, data.Status, data.Content,
		data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback, data.Cleaned,
		data.Id,
	)
	return err
}

func (m *customServiceExecutionRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customServiceExecutionRecordModel) FindByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*ServiceExecutionRecord, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ServiceExecutionRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customServiceExecutionRecordModel) FindServiceExecutionRecord(ctx context.Context) (
	[]*RedundantRecord, error,
) {
	/*
		SQL:
		SELECT `task_id`,
		       `execute_id`,
		       `project_id`,
		       `service_id` as `execute_type_id`,
		       `created_at`
		FROM service_execution_record
		WHERE `execute_type` = 'API_SERVICE'
		  AND `cleaned` = ?
		ORDER BY created_at DESC;
	*/
	fields := []string{
		"`task_id`",
		"`execute_id`",
		"`project_id`",
		"`service_id` as execute_type_id",
		"`created_at`",
	}
	var resp []*RedundantRecord
	query, values, err := squirrel.Select(fields...).Where(
		"`execute_type` = 'API_SERVICE' and `cleaned` = ?", common.NotCleaned,
	).OrderBy("created_at DESC").From(m.tableName()).ToSql()
	if err != nil {
		return nil, err
	}

	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customServiceExecutionRecordModel) DeleteRecordByTaskId(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}

func (m *customServiceExecutionRecordModel) SetRecordHasCleaned(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) error {
	stmt, values, err := squirrel.Update(m.table).
		SetMap(squirrel.Eq{"`cleaned`": common.HasCleaned}).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}

func (m *customServiceExecutionRecordModel) DeleteBeforeNDaysRecords(
	ctx context.Context, session sqlx.Session, days int,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`cleaned` = ? AND `created_at` < ?", common.HasCleaned, time.Now().AddDate(0, 0, -days)).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}
