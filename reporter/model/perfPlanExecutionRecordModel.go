package model

import (
	"context"
	"database/sql"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
)

var (
	_ PerfPlanExecutionRecordModel = (*customPerfPlanExecutionRecordModel)(nil)

	perfPlanExecutionRecordInsertFields = stringx.Remove(
		perfPlanExecutionRecordFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// PerfPlanExecutionRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPerfPlanExecutionRecordModel.
	PerfPlanExecutionRecordModel interface {
		perfPlanExecutionRecordModel
		types.DBModel

		withSession(session sqlx.Session) PerfPlanExecutionRecordModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PerfPlanExecutionRecord) squirrel.InsertBuilder
		UpdateBuilder(data *PerfPlanExecutionRecord) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*PerfPlanExecutionRecord, error,
		)

		FindCountByReq(ctx context.Context, req SearchPerfPlanExecutionRecordReq) (int64, error)
		FindAllByReq(ctx context.Context, req SearchPerfPlanExecutionRecordReq) ([]*PerfPlanExecutionRecord, error)

		GenerateSearchPerfPlanExecutionRecordSqlBuilder(req SearchPerfPlanExecutionRecordReq) (
			searchPerfPlanExecutionRecordSelectBuilder, searchPerfPlanExecutionRecordCountBuilder,
		)
		FindCountPerfPlanExecutionRecords(
			ctx context.Context, countBuilder searchPerfPlanExecutionRecordCountBuilder,
		) (int64, error)
		FindPerfPlanExecutionRecords(ctx context.Context, selectBuilder searchPerfPlanExecutionRecordSelectBuilder) (
			[]*SearchPerfPlanExecutionRecordItem, error,
		)

		FindPlanExecutionRecord(ctx context.Context) ([]*RedundantRecord, error)
		SetRecordHasCleaned(ctx context.Context, session sqlx.Session, taskID, projectID string) error
		DeleteBeforeNDaysRecords(ctx context.Context, session sqlx.Session, days int) error
	}

	customPerfPlanExecutionRecordModel struct {
		*defaultPerfPlanExecutionRecordModel

		conn sqlx.SqlConn
	}
)

// NewPerfPlanExecutionRecordModel returns a model for the database table.
func NewPerfPlanExecutionRecordModel(conn sqlx.SqlConn) PerfPlanExecutionRecordModel {
	return &customPerfPlanExecutionRecordModel{
		defaultPerfPlanExecutionRecordModel: newPerfPlanExecutionRecordModel(conn),
		conn:                                conn,
	}
}

func (m *customPerfPlanExecutionRecordModel) withSession(session sqlx.Session) PerfPlanExecutionRecordModel {
	return NewPerfPlanExecutionRecordModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customPerfPlanExecutionRecordModel) Table() string {
	return m.table
}

func (m *customPerfPlanExecutionRecordModel) Fields() []string {
	return perfPlanExecutionRecordFieldNames
}

func (m *customPerfPlanExecutionRecordModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customPerfPlanExecutionRecordModel) InsertBuilder(data *PerfPlanExecutionRecord) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(perfPlanExecutionRecordInsertFields...).Values(
		data.TaskId, data.ExecuteId, data.ProjectId, data.PlanId, data.PlanName, data.TriggerMode, data.TargetMaxRps,
		data.TargetDuration, data.Protocol, data.TargetEnv, data.Status, data.TaskType, data.ExecutionMode,
		data.CostTime, data.MonitorUrls, data.ExecutedBy, data.StartedAt, data.EndedAt, data.ApiMetrics, data.ErrMsg,
		data.Cleaned, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customPerfPlanExecutionRecordModel) UpdateBuilder(data *PerfPlanExecutionRecord) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`status`":      data.Status,
		"`cost_time`":   data.CostTime,
		"`ended_at`":    data.EndedAt,
		"`api_metrics`": data.ApiMetrics,
		"`err_msg`":     data.ErrMsg,
		"`cleaned`":     data.Cleaned,
		"`deleted`":     data.Deleted,
		"`updated_by`":  data.UpdatedBy,
		"`deleted_by`":  data.DeletedBy,
		"`deleted_at`":  data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPerfPlanExecutionRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(perfPlanExecutionRecordFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customPerfPlanExecutionRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPerfPlanExecutionRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPerfPlanExecutionRecordModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PerfPlanExecutionRecord, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PerfPlanExecutionRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPerfPlanExecutionRecordModel) FindCountByReq(
	ctx context.Context, req SearchPerfPlanExecutionRecordReq,
) (int64, error) {
	return m.FindCount(
		ctx, sqlbuilder.SearchOptions(
			m.SelectCountBuilder().Where("`project_id` = ?", req.ProjectID),
			sqlbuilder.WithCondition(m, req.Condition),
		),
	)
}

func (m *customPerfPlanExecutionRecordModel) FindAllByReq(
	ctx context.Context, req SearchPerfPlanExecutionRecordReq,
) ([]*PerfPlanExecutionRecord, error) {
	return m.FindNoCacheByQuery(
		ctx, sqlbuilder.SearchOptions(
			m.SelectBuilder().Where("`project_id` = ?", req.ProjectID),
			sqlbuilder.WithCondition(m, req.Condition),
			sqlbuilder.WithPagination(m, req.Pagination),
			sqlbuilder.WithSort(m, req.Sort),
		),
	)
}

type SearchPerfPlanExecutionRecordItem struct {
	TaskId         string         `db:"task_id"`         // 任务ID
	ExecuteId      string         `db:"execute_id"`      // 执行ID
	ProjectId      string         `db:"project_id"`      // 项目ID
	PlanId         string         `db:"plan_id"`         // 计划ID
	PlanName       string         `db:"plan_name"`       // 计划名称
	TriggerMode    string         `db:"trigger_mode"`    // 触发模式
	TargetMaxRps   int64          `db:"target_max_rps"`  // 目标最大的RPS
	TargetDuration int64          `db:"target_duration"` // 目标压测持续时长（单位为秒）
	Protocol       string         `db:"protocol"`        // 协议
	TargetEnv      string         `db:"target_env"`      // 目标环境（开发环境、测试环境、预发布环境、灰度环境、生产环境）
	Status         sql.NullString `db:"status"`          // 执行状态（结果）
	TaskType       string         `db:"task_type"`       // 任务类型（执行、调试）
	ExecutionMode  string         `db:"execution_mode"`  // 执行方式（按时长、按次数）
	CostTime       int64          `db:"cost_time"`       // 执行耗时（单位为毫秒）
	HasMonitorUrl  bool           `db:"has_monitor_url"` // 监控面板地址是否非空
	ExecutedBy     string         `db:"executed_by"`     // 执行者的用户ID
	StartedAt      sql.NullTime   `db:"started_at"`      // 开始时间
	EndedAt        sql.NullTime   `db:"ended_at"`        // 结束时间
	ErrMsg         sql.NullString `db:"err_msg"`         // 计划执行错误信息
	Cleaned        int64          `db:"cleaned"`         // 清理标识（未清理、已清理）
	CreatedBy      string         `db:"created_by"`      // 创建者的用户ID
	UpdatedBy      string         `db:"updated_by"`      // 最近一次更新者的用户ID
	CreatedAt      time.Time      `db:"created_at"`      // 创建时间
	UpdatedAt      time.Time      `db:"updated_at"`      // 更新时间
}

type searchPerfPlanExecutionRecordSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchPerfPlanExecutionRecordCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customPerfPlanExecutionRecordModel) GenerateSearchPerfPlanExecutionRecordSqlBuilder(req SearchPerfPlanExecutionRecordReq) (
	searchPerfPlanExecutionRecordSelectBuilder, searchPerfPlanExecutionRecordCountBuilder,
) {
	var sb, scb squirrel.SelectBuilder

	fields := []string{
		"`task_id`",
		"`execute_id`",
		"`project_id`",
		"`plan_id`",
		"`plan_name`",
		"`trigger_mode`",
		"`target_max_rps`",
		"`target_duration`",
		"`protocol`",
		"`target_env`",
		"`status`",
		"`task_type`",
		"`execution_mode`",
		"`cost_time`",
		"`monitor_urls` IS NOT NULL AS `has_monitor_url`",
		"`executed_by`",
		"`started_at`",
		"`ended_at`",
		"`err_msg`",
		"`cleaned`",
		"`created_by`",
		"`updated_by`",
		"`created_at`",
		"`updated_at`",
	}
	sb = sqlbuilder.SearchOptions(
		squirrel.Select(fields...).
			From(m.table).
			Where("`project_id` = ? AND `deleted` = ?", req.ProjectID, constants.NotDeleted),
		sqlbuilder.WithCondition(m, req.Condition),
		sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)
	scb = sqlbuilder.SearchOptions(
		m.SelectCountBuilder().Where("`project_id` = ?", req.ProjectID),
		sqlbuilder.WithCondition(m, req.Condition),
	)

	if req.PlanID != "" {
		sb = sb.Where("`plan_id` = ?", req.PlanID)
		scb = scb.Where("`plan_id` = ?", req.PlanID)
	}

	return searchPerfPlanExecutionRecordSelectBuilder{SelectBuilder: sb}, searchPerfPlanExecutionRecordCountBuilder{SelectBuilder: scb}
}

func (m *customPerfPlanExecutionRecordModel) FindCountPerfPlanExecutionRecords(
	ctx context.Context, countBuilder searchPerfPlanExecutionRecordCountBuilder,
) (int64, error) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customPerfPlanExecutionRecordModel) FindPerfPlanExecutionRecords(
	ctx context.Context, selectBuilder searchPerfPlanExecutionRecordSelectBuilder,
) (
	[]*SearchPerfPlanExecutionRecordItem, error,
) {
	var resp []*SearchPerfPlanExecutionRecordItem

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

func (m *customPerfPlanExecutionRecordModel) FindPlanExecutionRecord(ctx context.Context) ([]*RedundantRecord, error) {
	/*
		SQL:
		SELECT `task_id`,
		       `execute_id`,
		       `project_id`,
		       `plan_id` as `execute_type_id`,
		       `created_at`
		FROM `perf_plan_execution_record`
		WHERE `cleaned` = ?
		ORDER BY `created_at` DESC;
	*/
	var resp []*RedundantRecord

	fields := []string{
		"`task_id`",
		"`execute_id`",
		"`project_id`",
		"`plan_id` AS `execute_type_id`",
		"`created_at`",
	}
	query, values, err := squirrel.Select(fields...).
		From(m.table).
		Where("`cleaned` = ?", common.NotCleaned).
		OrderBy("`created_at` DESC").
		ToSql()
	if err != nil {
		return nil, err
	}

	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch {
	case err == nil:
		return resp, nil
	case errors.Is(err, sqlc.ErrNotFound):
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customPerfPlanExecutionRecordModel) SetRecordHasCleaned(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) error {
	stmt, values, err := squirrel.Update(m.table).
		SetMap(
			squirrel.Eq{
				"`cleaned`":    common.HasCleaned,
				"`updated_at`": squirrel.Expr("`updated_at`"),
			},
		).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}

func (m *customPerfPlanExecutionRecordModel) DeleteBeforeNDaysRecords(
	ctx context.Context, session sqlx.Session, days int,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`cleaned` = ? AND `created_at` < ?", common.HasCleaned, time.Now().AddDate(0, 0, -days)).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}
