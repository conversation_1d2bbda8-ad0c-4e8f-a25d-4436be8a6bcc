package model

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
)

func CheckPerfCaseExecutionRecordByExecuteID(
	ctx context.Context, m PerfCaseExecutionRecordModel, taskID, executeID, projectID string,
) (*PerfCaseExecutionRecord, error) {
	r, err := m.FindOneByTaskIdExecuteIdProjectId(ctx, taskID, executeID, projectID)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find perf case execution record, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
				taskID, executeID, projectID, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the perf case execution record doesn't exist, task_id: %s, execute_id: %s, project_id: %s",
					taskID, executeID, projectID,
				),
			)
		}
	}

	return r, nil
}

func CheckPerfSuiteExecutionRecordByExecuteID(
	ctx context.Context, m PerfSuiteExecutionRecordModel, taskID, executeID, projectID string,
) (*PerfSuiteExecutionRecord, error) {
	r, err := m.FindOneByTaskIdExecuteIdProjectId(ctx, taskID, executeID, projectID)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find perf suite execution record, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
				taskID, executeID, projectID, err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the perf suite execution record doesn't exist, task_id: %s, execute_id: %s, project_id: %s",
					taskID, executeID, projectID,
				),
			)
		}
	}

	return r, nil
}

func CheckPerfPlanExecutionRecordByExecuteID(
	ctx context.Context, m PerfPlanExecutionRecordModel, taskID, executeID, projectID string,
) (*PerfPlanExecutionRecord, error) {
	r, err := m.FindOneByTaskIdExecuteIdProjectId(ctx, taskID, executeID, projectID)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find perf plan execution record, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
				taskID, executeID, projectID, err,
			)
		} else {
			return nil, errorx.Errorf(
				errorx.NotExists,
				"the perf plan execution record doesn't exist, task_id: %s, execute_id: %s, project_id: %s",
				taskID, executeID, projectID,
			)
		}
	}

	return r, nil
}

func CheckUIAgentComponentExecutionRecordByExecuteID(
	ctx context.Context, model UiAgentComponentExecutionRecordModel,
	taskID, executeID, projectID string,
) (*UiAgentComponentExecutionRecord, error) {
	r, err := model.FindOneByTaskIdExecuteIdProjectId(ctx, taskID, executeID, projectID)
	if err != nil {
		if !errors.Is(err, ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find ui agent component execution record, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
				taskID, executeID, projectID, err,
			)
		} else {
			return nil, errorx.Errorf(
				errorx.NotExists,
				"the ui agent component execution record doesn't exist, task_id: %s, execute_id: %s, project_id: %s",
				taskID, executeID, projectID,
			)
		}
	}

	return r, nil
}
