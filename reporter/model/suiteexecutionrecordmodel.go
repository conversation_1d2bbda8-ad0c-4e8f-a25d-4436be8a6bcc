package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
)

var (
	_                                      SuiteExecutionRecordModel = (*customSuiteExecutionRecordModel)(nil)
	suiteExecutionRecordRowsExpectAutoSet2                           = strings.Join(
		stringx.Remove(
			suiteExecutionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), ",",
	)
	suiteExecutionRecordRowsWithPlaceHolder2 = strings.Join(
		stringx.Remove(
			suiteExecutionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), "=?,",
	) + "=?"
)

type (
	// SuiteExecutionRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customSuiteExecutionRecordModel.
	SuiteExecutionRecordModel interface {
		suiteExecutionRecordModel
		types.DBModel

		SelectBuilder() squirrel.SelectBuilder
		SelectBuilderWithEmptyContent() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder

		InsertRecordWithDefaultCreatedAt(ctx context.Context, data *SuiteExecutionRecord) (sql.Result, error)
		UpdateRecordWithDefaultUpdatedAt(ctx context.Context, data *SuiteExecutionRecord) error

		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*SuiteExecutionRecord, error)
		FindSuiteExecutionRecord(ctx context.Context) ([]*RedundantRecord, error)
		DeleteRecordByTaskId(ctx context.Context, session sqlx.Session, taskID, projectID string) error
		SetRecordHasCleaned(ctx context.Context, session sqlx.Session, taskID, projectID string) error
		DeleteBeforeNDaysRecords(ctx context.Context, session sqlx.Session, days int) error
	}

	customSuiteExecutionRecordModel struct {
		*defaultSuiteExecutionRecordModel
	}
)

func (m *defaultSuiteExecutionRecordModel) Table() string {
	return m.table
}

func (m *defaultSuiteExecutionRecordModel) Fields() []string {
	return suiteExecutionRecordFieldNames
}

func (m *defaultSuiteExecutionRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(suiteExecutionRecordFieldNames...).From(m.table)
}

func (m *defaultSuiteExecutionRecordModel) SelectBuilderWithEmptyContent() squirrel.SelectBuilder {
	fieldNames := stringx.Remove(suiteExecutionRecordFieldNames, "`content`")
	fieldNames = append(fieldNames, "'' as content")
	return squirrel.Select(fieldNames...).From(m.table)
}

func (m *defaultSuiteExecutionRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(1)").From(m.table)
}

func (m *defaultSuiteExecutionRecordModel) InsertRecordWithDefaultCreatedAt(
	ctx context.Context, data *SuiteExecutionRecord,
) (sql.Result, error) {
	query := fmt.Sprintf(
		"insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		m.table, suiteExecutionRecordRowsExpectAutoSet2,
	)
	ret, err := m.conn.ExecCtx(
		ctx, query,
		data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig,
		data.AccountConfig, data.SuiteId, data.SuiteExecuteId, data.SuiteName, data.PlanExecuteId,
		data.TotalCase, data.SuccessCase, data.FailureCase, data.Status, data.Content,
		data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback, data.Cleaned,
	)
	return ret, err
}

func (m *defaultSuiteExecutionRecordModel) UpdateRecordWithDefaultUpdatedAt(
	ctx context.Context, data *SuiteExecutionRecord,
) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, suiteExecutionRecordRowsWithPlaceHolder2)
	_, err := m.conn.ExecCtx(
		ctx, query,
		data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig,
		data.AccountConfig, data.SuiteId, data.SuiteExecuteId, data.SuiteName, data.PlanExecuteId,
		data.TotalCase, data.SuccessCase, data.FailureCase, data.Status, data.Content,
		data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback, data.Cleaned,
		data.Id,
	)
	return err
}

func (m *defaultSuiteExecutionRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultSuiteExecutionRecordModel) FindByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*SuiteExecutionRecord, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*SuiteExecutionRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

// NewSuiteExecutionRecordModel returns a model for the database table.
func NewSuiteExecutionRecordModel(conn sqlx.SqlConn) SuiteExecutionRecordModel {
	return &customSuiteExecutionRecordModel{
		defaultSuiteExecutionRecordModel: newSuiteExecutionRecordModel(conn),
	}
}

func (m *customSuiteExecutionRecordModel) FindSuiteExecutionRecord(ctx context.Context) ([]*RedundantRecord, error) {
	/*
		SQL:
		SELECT `task_id`,
		       `execute_id`,
		       `project_id`,
		       `suite_id` AS `execute_type_id`,
		       `created_at`
		FROM `suite_execution_record`
		WHERE `execute_type` = 'API_SUITE'
		  AND `cleaned` = ?
		ORDER BY `created_at` DESC;
	*/
	fields := []string{
		"`task_id`",
		"`execute_id`",
		"`project_id`",
		"`suite_id` AS execute_type_id",
		"`created_at`",
	}
	var resp []*RedundantRecord
	query, values, err := squirrel.Select(fields...).
		From(m.table).
		Where("`execute_type` = 'API_SUITE' AND `cleaned` = ?", common.NotCleaned).
		OrderBy("`created_at` DESC").
		ToSql()
	if err != nil {
		return nil, err
	}

	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch {
	case err == nil:
		return resp, nil
	case errors.Is(err, sqlc.ErrNotFound):
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customSuiteExecutionRecordModel) DeleteRecordByTaskId(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}

func (m *customSuiteExecutionRecordModel) SetRecordHasCleaned(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) error {
	stmt, values, err := squirrel.Update(m.table).
		SetMap(
			squirrel.Eq{
				"`cleaned`":    common.HasCleaned,
				"`updated_at`": squirrel.Expr("`updated_at`"),
			},
		).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}

func (m *customSuiteExecutionRecordModel) DeleteBeforeNDaysRecords(
	ctx context.Context, session sqlx.Session, days int,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`cleaned` = ? AND `created_at` < ?", common.HasCleaned, time.Now().AddDate(0, 0, -days)).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}
