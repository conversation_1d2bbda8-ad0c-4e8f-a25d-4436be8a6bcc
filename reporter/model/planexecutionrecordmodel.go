package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
)

var (
	_                                     PlanExecutionRecordModel = (*customPlanExecutionRecordModel)(nil)
	planExecutionRecordRowsExpectAutoSet2                          = strings.Join(
		stringx.Remove(
			planExecutionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), ",",
	)
	planExecutionRecordRowsWithPlaceHolder2 = strings.Join(
		stringx.Remove(
			planExecutionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), "=?,",
	) + "=?"
)

type (
	// PlanExecutionRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPlanExecutionRecordModel.
	PlanExecutionRecordModel interface {
		planExecutionRecordModel
		types.DBModel

		SelectBuilder() squirrel.SelectBuilder
		SelectBuilderWithEmptyContent() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder

		InsertRecordWithDefaultCreatedAt(ctx context.Context, data *PlanExecutionRecord) (sql.Result, error)
		UpdateRecordWithDefaultUpdatedAt(ctx context.Context, data *PlanExecutionRecord) error

		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*PlanExecutionRecord, error)
		FindOneByTaskIdProjectId(ctx context.Context, taskId, projectId string) (*PlanExecutionRecord, error)

		FindPlanExecutionRecord(ctx context.Context) ([]*RedundantRecord, error)
		SetRecordHasCleaned(ctx context.Context, session sqlx.Session, taskID, projectID string) error
		DeleteBeforeNDaysRecords(ctx context.Context, session sqlx.Session, days int) error
	}

	customPlanExecutionRecordModel struct {
		*defaultPlanExecutionRecordModel
	}
)

func (m *defaultPlanExecutionRecordModel) Table() string {
	return m.table
}

func (m *defaultPlanExecutionRecordModel) Fields() []string {
	return planExecutionRecordFieldNames
}

func (m *defaultPlanExecutionRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(planExecutionRecordFieldNames...).From(m.table)
}

func (m *defaultPlanExecutionRecordModel) SelectBuilderWithEmptyContent() squirrel.SelectBuilder {
	fieldNames := stringx.Remove(planExecutionRecordFieldNames, "`content`")
	fieldNames = append(fieldNames, "'' as content")
	return squirrel.Select(fieldNames...).From(m.table)
}

func (m *defaultPlanExecutionRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(1)").From(m.table)
}

func (m *defaultPlanExecutionRecordModel) InsertRecordWithDefaultCreatedAt(
	ctx context.Context, data *PlanExecutionRecord,
) (sql.Result, error) {
	query := fmt.Sprintf(
		"insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		m.table, planExecutionRecordRowsExpectAutoSet2,
	)
	ret, err := m.conn.ExecCtx(
		ctx, query,
		data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig, data.AccountConfig,
		data.PlanId, data.PlanExecuteId, data.PlanName, data.TriggerMode, data.PlanPurpose, data.TotalSuite,
		data.SuccessSuite, data.FailureSuite, data.TotalCase, data.SuccessCase, data.FailureCase, data.Status,
		data.Content, data.ServiceCasesContent, data.ExecutedBy, data.ApprovedBy, data.StartedAt, data.EndedAt,
		data.CostTime, data.Cleaned,
	)
	return ret, err
}

func (m *defaultPlanExecutionRecordModel) UpdateRecordWithDefaultUpdatedAt(
	ctx context.Context, data *PlanExecutionRecord,
) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, planExecutionRecordRowsWithPlaceHolder2)
	_, err := m.conn.ExecCtx(
		ctx, query,
		data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig, data.AccountConfig,
		data.PlanId, data.PlanExecuteId, data.PlanName, data.TriggerMode, data.PlanPurpose, data.TotalSuite,
		data.SuccessSuite, data.FailureSuite, data.TotalCase, data.SuccessCase, data.FailureCase, data.Status,
		data.Content, data.ServiceCasesContent, data.ExecutedBy, data.ApprovedBy, data.StartedAt, data.EndedAt,
		data.CostTime, data.Cleaned, data.Id,
	)
	return err
}

func (m *defaultPlanExecutionRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultPlanExecutionRecordModel) FindByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*PlanExecutionRecord, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PlanExecutionRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultPlanExecutionRecordModel) FindOneByTaskIdProjectId(
	ctx context.Context, taskId, projectId string,
) (*PlanExecutionRecord, error) {
	var resp PlanExecutionRecord
	query := fmt.Sprintf(
		"select %s from %s where `task_id` = ? and `project_id` = ?  limit 1", planExecutionRecordRows, m.table,
	)
	err := m.conn.QueryRowCtx(ctx, &resp, query, taskId, projectId)
	switch {
	case err == nil:
		return &resp, nil
	case errors.Is(err, sqlc.ErrNotFound):
		return nil, nil
	default:
		return nil, err
	}
}

// NewPlanExecutionRecordModel returns a model for the database table.
func NewPlanExecutionRecordModel(conn sqlx.SqlConn) PlanExecutionRecordModel {
	return &customPlanExecutionRecordModel{
		defaultPlanExecutionRecordModel: newPlanExecutionRecordModel(conn),
	}
}

func (m *customPlanExecutionRecordModel) FindPlanExecutionRecord(ctx context.Context) ([]*RedundantRecord, error) {
	/*
		SQL:
		SELECT `task_id`,
		       `execute_id`,
		       `project_id`,
		       `plan_id` AS `execute_type_id`,
		       `created_at`
		FROM `plan_execution_record`
		WHERE `cleaned` = ?
		ORDER BY `created_at` DESC;
	*/
	fields := []string{
		"`task_id`",
		"`execute_id`",
		"`project_id`",
		"`plan_id` AS execute_type_id",
		"`created_at`",
	}
	var resp []*RedundantRecord
	query, values, err := squirrel.Select(fields...).
		From(m.table).
		Where("`cleaned` = ?", common.NotCleaned).
		OrderBy("`created_at` DESC").
		ToSql()
	if err != nil {
		return nil, err
	}

	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch {
	case err == nil:
		return resp, nil
	case errors.Is(err, sqlc.ErrNotFound):
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customPlanExecutionRecordModel) SetRecordHasCleaned(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) error {
	stmt, values, err := squirrel.Update(m.table).
		SetMap(
			squirrel.Eq{
				"`cleaned`":    common.HasCleaned,
				"`updated_at`": squirrel.Expr("`updated_at`"),
			},
		).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}

func (m *customPlanExecutionRecordModel) DeleteBeforeNDaysRecords(
	ctx context.Context, session sqlx.Session, days int,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`cleaned` = ? AND `created_at` < ?", common.HasCleaned, time.Now().AddDate(0, 0, -days)).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}
