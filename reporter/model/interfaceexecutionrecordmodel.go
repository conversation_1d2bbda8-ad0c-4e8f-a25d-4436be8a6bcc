package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
)

var (
	_                                          InterfaceExecutionRecordModel = (*customInterfaceExecutionRecordModel)(nil)
	interfaceExecutionRecordRowsExpectAutoSet2                               = strings.Join(
		stringx.Remove(
			interfaceExecutionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), ",",
	)
	interfaceExecutionRecordRowsWithPlaceHolder2 = strings.Join(
		stringx.Remove(
			interfaceExecutionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), "=?,",
	) + "=?"
)

type (
	// InterfaceExecutionRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customInterfaceExecutionRecordModel.
	InterfaceExecutionRecordModel interface {
		interfaceExecutionRecordModel
		types.DBModel

		SelectBuilder() squirrel.SelectBuilder
		SelectBuilderWithEmptyContent() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder

		InsertRecordWithDefaultCreatedAt(ctx context.Context, data *InterfaceExecutionRecord) (sql.Result, error)
		UpdateRecordWithDefaultUpdatedAt(ctx context.Context, data *InterfaceExecutionRecord) error

		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*InterfaceExecutionRecord, error)

		FindInterFaceDocumentExecutionRecord(ctx context.Context) ([]*RedundantRecord, error)
		DeleteRecordByTaskId(ctx context.Context, session sqlx.Session, taskID, projectID string) error
		SetRecordHasCleaned(ctx context.Context, session sqlx.Session, taskID, projectID string) error
		DeleteBeforeNDaysRecords(ctx context.Context, session sqlx.Session, days int) error
	}

	customInterfaceExecutionRecordModel struct {
		*defaultInterfaceExecutionRecordModel
	}
)

func (m *defaultInterfaceExecutionRecordModel) Table() string {
	return m.table
}

func (m *defaultInterfaceExecutionRecordModel) Fields() []string {
	return interfaceExecutionRecordFieldNames
}

func (m *defaultInterfaceExecutionRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(interfaceExecutionRecordFieldNames...).From(m.table)
}

func (m *defaultInterfaceExecutionRecordModel) SelectBuilderWithEmptyContent() squirrel.SelectBuilder {
	fieldNames := stringx.Remove(interfaceExecutionRecordFieldNames, "`content`")
	fieldNames = append(fieldNames, "'' as content")
	return squirrel.Select(fieldNames...).From(m.table)
}

func (m *defaultInterfaceExecutionRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(1)").From(m.table)
}

func (m *defaultInterfaceExecutionRecordModel) InsertRecordWithDefaultCreatedAt(
	ctx context.Context, data *InterfaceExecutionRecord,
) (sql.Result, error) {
	query := fmt.Sprintf(
		"insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		m.table, interfaceExecutionRecordRowsExpectAutoSet2,
	)
	ret, err := m.conn.ExecCtx(
		ctx, query,
		data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig,
		data.AccountConfig, data.InterfaceId, data.InterfaceExecuteId, data.InterfaceName, data.PlanExecuteId,
		data.TotalCase, data.SuccessCase, data.FailureCase, data.Status, data.Content,
		data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback, data.Cleaned,
	)
	return ret, err
}

func (m *defaultInterfaceExecutionRecordModel) UpdateRecordWithDefaultUpdatedAt(
	ctx context.Context, data *InterfaceExecutionRecord,
) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, interfaceExecutionRecordRowsWithPlaceHolder2)
	_, err := m.conn.ExecCtx(
		ctx, query,
		data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig,
		data.AccountConfig, data.InterfaceId, data.InterfaceExecuteId, data.InterfaceName, data.PlanExecuteId,
		data.TotalCase, data.SuccessCase, data.FailureCase, data.Status, data.Content,
		data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback, data.Cleaned,
		data.Id,
	)
	return err
}

func (m *defaultInterfaceExecutionRecordModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultInterfaceExecutionRecordModel) FindByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*InterfaceExecutionRecord, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*InterfaceExecutionRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

// NewInterfaceExecutionRecordModel returns a model for the database table.
func NewInterfaceExecutionRecordModel(conn sqlx.SqlConn) InterfaceExecutionRecordModel {
	return &customInterfaceExecutionRecordModel{
		defaultInterfaceExecutionRecordModel: newInterfaceExecutionRecordModel(conn),
	}
}

func (m *customInterfaceExecutionRecordModel) FindInterFaceDocumentExecutionRecord(ctx context.Context) (
	[]*RedundantRecord, error,
) {
	/*
		SQL:
		SELECT `task_id`,
		       `execute_id`,
		       `project_id`,
		       `interface_id` as `execute_type_id`,
		       `created_at`
		FROM interface_execution_record
		WHERE `execute_type` = 'INTERFACE_DOCUMENT'
		  AND `cleaned` = ?
		ORDER BY created_at DESC;
	*/
	fields := []string{
		"`task_id`",
		"`execute_id`",
		"`project_id`",
		"`interface_id` as execute_type_id",
		"`created_at`",
	}
	var resp []*RedundantRecord
	query, values, err := squirrel.Select(fields...).Where(
		"`execute_type` = 'INTERFACE_DOCUMENT' AND `cleaned` = ?", common.NotCleaned,
	).OrderBy("created_at DESC").From(m.tableName()).ToSql()
	if err != nil {
		return nil, err
	}

	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customInterfaceExecutionRecordModel) DeleteRecordByTaskId(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}

func (m *customInterfaceExecutionRecordModel) SetRecordHasCleaned(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) error {
	stmt, values, err := squirrel.Update(m.table).
		SetMap(
			squirrel.Eq{
				"`cleaned`":    common.HasCleaned,
				"`updated_at`": squirrel.Expr("`updated_at`"),
			},
		).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}

func (m *customInterfaceExecutionRecordModel) DeleteBeforeNDaysRecords(
	ctx context.Context, session sqlx.Session, days int,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`cleaned` = ? AND `created_at` < ?", common.HasCleaned, time.Now().AddDate(0, 0, -days)).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}
