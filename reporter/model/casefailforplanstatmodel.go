package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ CaseFailForPlanStatModel = (*customCaseFailForPlanStatModel)(nil)

	caseFailForPlanStatInsertFields = stringx.Remove(
		caseFailForPlanStatFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)
)

type (
	// CaseFailForPlanStatModel is an interface to be customized, add more methods here,
	// and implement the added methods in customCaseFailForPlanStatModel.
	CaseFailForPlanStatModel interface {
		caseFailForPlanStatModel
		types.DBModel

		withSession(session sqlx.Session) CaseFailForPlanStatModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *CaseFailForPlanStat) squirrel.InsertBuilder
		UpdateBuilder(data *CaseFailForPlanStat) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*CaseFailForPlanStat, error)

		InsertTX(ctx context.Context, session sqlx.Session, data *CaseFailForPlanStat) (sql.Result, error)

		FindJoinPlanNoCacheByQuery(
			ctx context.Context, selectBuilder squirrel.SelectBuilder,
		) ([]*CaseFailForPlanStatMergePlan, error)
		GenerateListFailCaseForPlanRecordQuery(req SearchCaseFailForPlanStatReq) (
			listFailCaseForPlanRecordSelectBuilder, listFailCaseForPlanRecordCountBuilder,
		)
		DeleteByProjectIdCaseIdCaseType(
			ctx context.Context, session sqlx.Session, projectId, caseId, caseType string,
		) error

		FindFailedCaseRecords(ctx context.Context) ([]*RedundantRecord, error)
		DeleteBeforeNDaysRecords(ctx context.Context, session sqlx.Session, days int) error
		CountInLastNDaysRecords(ctx context.Context, projectID, caseID, caseType string, days int) (*CountResult, error)
	}

	customCaseFailForPlanStatModel struct {
		*defaultCaseFailForPlanStatModel
	}

	CaseFailForPlanStatMergePlan struct {
		ProjectId     string         `db:"project_id"`      // 项目ID
		TaskId        string         `db:"task_id"`         // 任务ID
		PlanId        string         `db:"plan_id"`         // 计划ID
		CaseId        string         `db:"case_id"`         // 用例ID
		ExecuteId     string         `db:"execute_id"`      // 执行ID
		CaseType      string         `db:"case_type"`       // 用例类型[API_CASE，INTERFACE_CASE]
		FailReasonUrl sql.NullString `db:"fail_reason_url"` // 失败原因-path，上传文件服务器后的地址
		CreatedAt     time.Time      `db:"created_at"`      // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`      // 更新时间

		/*merge*/
		ExecuteType         string         `db:"execute_type"`          // 执行类型
		GeneralConfig       sql.NullString `db:"general_config"`        // 通用配置
		AccountConfig       sql.NullString `db:"account_config"`        // 池账号配置
		PlanExecuteId       string         `db:"plan_execute_id"`       // 计划执行id, 作为接口执行表或集合执行表的parent_component_execute_id
		PlanName            string         `db:"plan_name"`             // 计划名称
		TriggerMode         string         `db:"trigger_mode"`          // 触发模式
		PurposeType         string         `db:"purpose_type"`          // 计划用途
		TotalSuite          int64          `db:"total_suite"`           // 计划总集合（或接口）个数
		SuccessSuite        int64          `db:"success_suite"`         // 执行成功集合（或接口）个数
		FailureSuite        int64          `db:"failure_suite"`         // 执行失败集合（或接口）个数
		TotalCase           int64          `db:"total_case"`            // 测试用例总数
		SuccessCase         int64          `db:"success_case"`          // 执行成功的测试用例数
		FailureCase         int64          `db:"failure_case"`          // 执行完成的测试用例数
		Status              sql.NullString `db:"status"`                // 执行状态（结果）
		Content             sql.NullString `db:"content"`               // 执行数据详情
		ServiceCasesContent sql.NullString `db:"service_cases_content"` // 保存每个服务与测试用例关系，用于精准测试
		ExecutedBy          string         `db:"executed_by"`           // 执行人
		StartedAt           int64          `db:"started_at"`            // 开始执行的时间(戳)
		EndedAt             sql.NullInt64  `db:"ended_at"`              // 结束执行的时间(戳)
		CostTime            int64          `db:"cost_time"`             // 执行耗时(毫秒)
		Cleaned             int64          `db:"cleaned"`               // 是否已清理过记录
	}

	SearchCaseFailForPlanStatReq struct {
		ProjectId  string                `json:"project_id"`
		CaseId     string                `json:"case_id"`
		CaseType   string                `json:"case_type"`
		Pagination sqlbuilder.Pagination `json:"pagination"`
	}

	listFailCaseForPlanRecordSelectBuilder struct {
		squirrel.SelectBuilder
	}

	listFailCaseForPlanRecordCountBuilder struct {
		squirrel.SelectBuilder
	}

	CountResult struct {
		Count         int64        `db:"count"`
		LastUpdatedAt sql.NullTime `db:"last_updated_at"`
	}
)

// NewCaseFailForPlanStatModel returns a model for the database table.
func NewCaseFailForPlanStatModel(conn sqlx.SqlConn) CaseFailForPlanStatModel {
	return &customCaseFailForPlanStatModel{
		defaultCaseFailForPlanStatModel: newCaseFailForPlanStatModel(conn),
	}
}

func (m *customCaseFailForPlanStatModel) withSession(session sqlx.Session) CaseFailForPlanStatModel {
	return NewCaseFailForPlanStatModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customCaseFailForPlanStatModel) Table() string {
	return m.table
}

func (m *customCaseFailForPlanStatModel) Fields() []string {
	return caseFailForPlanStatFieldNames
}

func (m *customCaseFailForPlanStatModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customCaseFailForPlanStatModel) InsertBuilder(data *CaseFailForPlanStat) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(caseFailForPlanStatInsertFields...).Values(
		data.ProjectId, data.TaskId, data.PlanId, data.CaseId, data.ExecuteId, data.CaseType, data.FailReasonUrl,
		data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customCaseFailForPlanStatModel) UpdateBuilder(data *CaseFailForPlanStat) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customCaseFailForPlanStatModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(caseFailForPlanStatFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customCaseFailForPlanStatModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customCaseFailForPlanStatModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customCaseFailForPlanStatModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*CaseFailForPlanStat, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*CaseFailForPlanStat
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customCaseFailForPlanStatModel) InsertTX(
	ctx context.Context, session sqlx.Session, data *CaseFailForPlanStat,
) (sql.Result, error) {
	query, values, err := m.InsertBuilder(data).ToSql()
	if err != nil {
		return nil, err
	}
	if session == nil {
		return m.conn.ExecCtx(ctx, query, values...)
	}

	return session.ExecCtx(ctx, query, values...)
}

func (m *customCaseFailForPlanStatModel) FindJoinPlanNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*CaseFailForPlanStatMergePlan, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*CaseFailForPlanStatMergePlan
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	case sql.ErrNoRows:
		return nil, nil
	default:
		return nil, err
	}
}

func (m *customCaseFailForPlanStatModel) GenerateListFailCaseForPlanRecordQuery(req SearchCaseFailForPlanStatReq) (
	listFailCaseForPlanRecordSelectBuilder, listFailCaseForPlanRecordCountBuilder,
) {
	/*
		SQL:
		SELECT cffps.*,
		       per.*
		FROM `case_fail_for_plan_stat` AS cffps
		    LEFT JOIN `plan_execution_record` AS per ON
		        cffps.`project_id` = per.`project_id` AND
		        cffps.`plan_id` = per.`plan_id` AND
		        cffps.`task_id` = per.`task_id`
		WHERE cffps.`case_id` = 'case_id:PAOmlFhD-f1_z8cdrRxoN'
		  AND cffps.`case_type` = 'API_CASE'
		  AND cffps.`project_id` = 'project_id:Kqllt5-9fA-I5UOdhjA5d'
		  AND cffps.`deleted` = 0
		  AND cffps.`created_at` > DATE_SUB(CURDATE(), INTERVAL 7 DAY)
		ORDER BY cffps.`created_at` DESC
		LIMIT 10 OFFSET 0;

		SELECT COUNT(1)
		FROM `case_fail_for_plan_stat` AS cffps
		    LEFT JOIN `plan_execution_record` AS per ON
		        cffps.`project_id` = per.`project_id` AND
		        cffps.`plan_id` = per.`plan_id` AND
		        cffps.`task_id` = per.`task_id`
		WHERE cffps.`case_id` = 'case_id:PAOmlFhD-f1_z8cdrRxoN'
		  AND cffps.`case_type` = 'API_CASE'
		  AND cffps.`project_id` = 'project_id:Kqllt5-9fA-I5UOdhjA5d'
		  AND cffps.`created_at` > DATE_SUB(CURDATE(), INTERVAL 7 DAY)
	*/

	var sb, scb squirrel.SelectBuilder
	caseFailForPlanStatAs := "cffps"
	planExecutionRecordTableAs := "per"
	selectFields := []string{
		"cffps.`project_id`",
		"cffps.`task_id`",
		"cffps.`plan_id`",
		"cffps.`case_id`",
		"cffps.`execute_id`",
		"cffps.`case_type`",
		"cffps.`fail_reason_url`",
		"cffps.`created_at`",
		"cffps.`updated_at`",
		"per.`execute_type`",
		"per.`general_config`",
		"per.`account_config`",
		"per.`plan_execute_id`",
		"per.`plan_name`",
		"per.`trigger_mode`",
		"per.`plan_purpose`",
		"per.`total_suite`",
		"per.`success_suite`",
		"per.`failure_suite`",
		"per.`total_case`",
		"per.`success_case`",
		"per.`failure_case`",
		"per.`status`",
		"per.`content`",
		"per.`service_cases_content`",
		"per.`executed_by`",
		"per.`started_at`",
		"per.`ended_at`",
		"per.`cost_time`",
		"per.`cleaned`",
		"per.`plan_purpose` as purpose_type",
	}

	sqlTmp := squirrel.Select(
		selectFields...,
	).From(m.Table()+" AS "+caseFailForPlanStatAs).LeftJoin(
		fmt.Sprintf(
			"%s AS %s ON cffps.`project_id` = per.`project_id` AND cffps.`plan_id` = per.`plan_id` AND cffps.`task_id` = per.`task_id`",
			planExecutionRecordTableName, planExecutionRecordTableAs,
		),
	).Where(
		"cffps.`project_id` = ? AND cffps.`case_type` = ? AND cffps.`case_id` = ? AND cffps.`deleted` = ? AND cffps.created_at > DATE_SUB(CURDATE(), INTERVAL 7 DAY)",
		req.ProjectId, req.CaseType, req.CaseId, constants.NotDeleted,
	).OrderBy("cffps.`created_at` DESC")

	sb = sqlbuilder.SearchOptions(
		sqlTmp,
		sqlbuilder.WithPagination(m, req.Pagination),
	)

	scb = squirrel.Select("COUNT(*)").From(m.Table()+" AS "+caseFailForPlanStatAs).LeftJoin(
		fmt.Sprintf(
			"%s AS %s ON cffps.`project_id` = per.`project_id` AND cffps.`plan_id` = per.`plan_id` and cffps.`task_id` = per.`task_id`",
			planExecutionRecordTableName, planExecutionRecordTableAs,
		),
	).Where(
		"cffps.`project_id` = ? AND cffps.`case_type` = ? AND cffps.`case_id` = ? AND cffps.`deleted` = ? AND cffps.created_at > DATE_SUB(CURDATE(), INTERVAL 7 DAY)",
		req.ProjectId, req.CaseType, req.CaseId, constants.NotDeleted,
	)

	return listFailCaseForPlanRecordSelectBuilder{SelectBuilder: sb}, listFailCaseForPlanRecordCountBuilder{SelectBuilder: scb}
}

func (m *customCaseFailForPlanStatModel) DeleteByProjectIdCaseIdCaseType(
	ctx context.Context, session sqlx.Session, projectId, caseId, caseType string,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`project_id` = ? AND `case_id` = ? AND `case_type` = ?", projectId, caseId, caseType).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
		return err
	}

	_, err = m.conn.ExecCtx(ctx, stmt, values...)
	return err
}

func (m *customCaseFailForPlanStatModel) FindFailedCaseRecords(ctx context.Context) ([]*RedundantRecord, error) {
	var resp []*RedundantRecord

	fields := []string{
		"`task_id`",
		"`execute_id`",
		"`project_id`",
		"`case_id` AS `execute_type_id`",
		"`created_at`",
	}
	query, values, err := squirrel.Select(fields...).From(m.table).OrderBy("`created_at` DESC").ToSql()
	if err != nil {
		return nil, err
	}

	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch {
	case err == nil:
		return resp, nil
	case errors.Is(err, sqlc.ErrNotFound):
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customCaseFailForPlanStatModel) DeleteBeforeNDaysRecords(
	ctx context.Context, session sqlx.Session, days int,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`created_at` < ?", time.Now().AddDate(0, 0, -days)).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
		return err
	}

	_, err = m.conn.ExecCtx(ctx, stmt, values...)
	return err
}

func (m *customCaseFailForPlanStatModel) CountInLastNDaysRecords(
	ctx context.Context, projectID, caseID, caseType string, days int,
) (*CountResult, error) {
	query, values, err := squirrel.Select("COUNT(*) AS `count`", "MAX(`updated_at`) AS `last_updated_at`").
		From(m.table).
		Where(
			"`project_id` = ? AND `case_id` = ? AND `case_type` = ? AND `updated_at` >= ? AND `deleted` = ?",
			projectID, caseID, caseType, time.Now().AddDate(0, 0, -days), constants.NotDeleted,
		).
		ToSql()
	if err != nil {
		return nil, err
	}

	var resp CountResult
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch {
	case err == nil:
		return &resp, nil
	case errors.Is(err, sqlc.ErrNotFound):
		return nil, ErrNotFound
	default:
		return nil, err
	}
}
