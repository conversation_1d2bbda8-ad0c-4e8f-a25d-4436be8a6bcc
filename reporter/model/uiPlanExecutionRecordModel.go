package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
)

var (
	_                                       UiPlanExecutionRecordModel = (*customUiPlanExecutionRecordModel)(nil)
	uiPlanExecutionRecordRowsExpectAutoSet2                            = strings.Join(
		stringx.Remove(
			uiPlanExecutionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), ",",
	)
	uiPlanExecutionRecordRowsWithPlaceHolder2 = strings.Join(
		stringx.Remove(
			uiPlanExecutionRecordFieldNames, "`id`", "`created_at`", "`updated_at`",
		), "=?,",
	) + "=?"
)

type (
	// UiPlanExecutionRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUiPlanExecutionRecordModel.
	UiPlanExecutionRecordModel interface {
		uiPlanExecutionRecordModel
		types.DBModel

		SelectBuilder() squirrel.SelectBuilder
		SelectBuilderWithEmptyContent() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder

		InsertRecordWithDefaultCreatedAt(ctx context.Context, data *UiPlanExecutionRecord) (sql.Result, error)
		UpdateRecordWithDefaultUpdatedAt(ctx context.Context, data *UiPlanExecutionRecord) error

		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindByQuery(ctx context.Context, builder squirrel.SelectBuilder) ([]*UiPlanExecutionRecord, error)

		FindPlanExecutionRecord(ctx context.Context) ([]*RedundantRecord, error)
		FindOneByTaskIDExecuteIDProjectID(
			ctx context.Context, taskID, executeID, projectID string,
		) (*UiPlanExecutionRecord, error)
		SetRecordHasCleaned(ctx context.Context, session sqlx.Session, taskID, projectID string) error
		DeleteBeforeNDaysRecords(ctx context.Context, session sqlx.Session, days int) error
	}

	customUiPlanExecutionRecordModel struct {
		*defaultUiPlanExecutionRecordModel
	}
)

func (m *defaultUiPlanExecutionRecordModel) Table() string {
	return m.table
}

func (m *defaultUiPlanExecutionRecordModel) Fields() []string {
	return uiPlanExecutionRecordFieldNames
}

func (m *defaultUiPlanExecutionRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(uiPlanExecutionRecordFieldNames...).From(m.table)
}

func (m *defaultUiPlanExecutionRecordModel) SelectBuilderWithEmptyContent() squirrel.SelectBuilder {
	// fieldNames := stringx.Remove(uiPlanExecutionRecordFieldNames, "`content`")

	// fieldNames = append(fieldNames, "'' as content")
	return squirrel.Select(uiPlanExecutionRecordFieldNames...).From(m.table)
}

func (m *defaultUiPlanExecutionRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(1)").From(m.table)
}

func (m *defaultUiPlanExecutionRecordModel) InsertRecordWithDefaultCreatedAt(
	ctx context.Context, data *UiPlanExecutionRecord,
) (sql.Result, error) {
	query := fmt.Sprintf(
		"insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		m.table, uiPlanExecutionRecordRowsExpectAutoSet2,
	)
	ret, err := m.conn.ExecCtx(
		ctx, query,
		data.ProjectId, data.PlanId, data.PlanName, data.TriggerMode, data.TaskId, data.ExecuteId,
		data.Status, data.CostTime, data.ExecutedBy, data.StartedAt, data.EndedAt,
		data.TotalSuite, data.FinishedSuite, data.SuccessSuite, data.TotalCase, data.FinishedCase, data.SuccessCase,
		data.Content, data.Finished, data.ExecuteData, data.Cleaned, data.PriorityType, data.ExecuteStatus,
		data.ExecutedResult, data.WaitTime, data.UpdateAt,
	)
	return ret, err
}

func (m *defaultUiPlanExecutionRecordModel) UpdateRecordWithDefaultUpdatedAt(
	ctx context.Context, data *UiPlanExecutionRecord,
) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, uiPlanExecutionRecordRowsWithPlaceHolder2)
	_, err := m.conn.ExecCtx(
		ctx, query,
		data.ProjectId, data.PlanId, data.PlanName, data.TriggerMode, data.TaskId, data.ExecuteId,
		data.Status, data.CostTime, data.ExecutedBy, data.StartedAt, data.EndedAt,
		data.TotalSuite, data.FinishedSuite, data.SuccessSuite, data.TotalCase, data.FinishedCase, data.SuccessCase,
		data.Content, data.Finished, data.ExecuteData, data.Cleaned, data.PriorityType, data.ExecuteStatus,
		data.ExecutedResult, data.WaitTime, data.UpdateAt, data.Id,
	)
	return err
}

func (m *defaultUiPlanExecutionRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (
	int64, error,
) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultUiPlanExecutionRecordModel) FindByQuery(
	ctx context.Context, builder squirrel.SelectBuilder,
) ([]*UiPlanExecutionRecord, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiPlanExecutionRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func NewUiPlanExecutionRecordModel(conn sqlx.SqlConn) UiPlanExecutionRecordModel {
	return &customUiPlanExecutionRecordModel{
		defaultUiPlanExecutionRecordModel: newUiPlanExecutionRecordModel(conn),
	}
}

func (m *customUiPlanExecutionRecordModel) FindPlanExecutionRecord(ctx context.Context) ([]*RedundantRecord, error) {
	/*
		SQL:
		SELECT `task_id`,
		       `execute_id`,
		       `project_id`,
		       `plan_id` AS `execute_type_id`,
		       `created_at`
		FROM `ui_plan_execution_record`
		WHERE `cleaned` = ?
		ORDER BY created_at DESC;
	*/
	fields := []string{
		"`task_id`",
		"`execute_id`",
		"`project_id`",
		"`plan_id` AS `execute_type_id`",
		"`created_at`",
	}
	var resp []*RedundantRecord
	query, values, err := squirrel.Select(fields...).Where(
		"`cleaned` = ?", common.NotCleaned,
	).OrderBy("`created_at` DESC").From(m.tableName()).ToSql()
	if err != nil {
		return nil, err
	}

	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customUiPlanExecutionRecordModel) FindOneByTaskIDExecuteIDProjectID(
	ctx context.Context, taskID, executeID, projectID string,
) (*UiPlanExecutionRecord, error) {
	sb := m.SelectBuilder().Where(
		"`task_id` = ? AND `execute_id` = ? AND `project_id` = ?", taskID, executeID, projectID,
	).OrderBy("`updated_at` DESC").Limit(1)
	resp, err := m.FindByQuery(ctx, sb)
	if err != nil {
		return nil, err
	} else if len(resp) == 0 {
		return nil, ErrNotFound
	}
	return resp[0], nil
}

func (m *customUiPlanExecutionRecordModel) SetRecordHasCleaned(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) (err error) {
	stmt, values, err := squirrel.Update(m.table).
		SetMap(
			squirrel.Eq{
				"`cleaned`":    common.HasCleaned,
				"`updated_at`": squirrel.Expr("`updated_at`"),
			},
		).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}

func (m *customUiPlanExecutionRecordModel) DeleteBeforeNDaysRecords(
	ctx context.Context, session sqlx.Session, days int,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`cleaned` = ? AND `created_at` < ?", common.HasCleaned, time.Now().AddDate(0, 0, -days)).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}
