package perfReporter

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetPerfPlanRecordLogic struct {
	*BaseLogic
}

func NewGetPerfPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPerfPlanRecordLogic {
	return &GetPerfPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetPerfPlanRecordLogic) GetPerfPlanRecord(req *types.GetPerfPlanRecordReq) (
	resp *types.GetPerfPlanRecordResp, err error,
) {
	out, err := l.svcCtx.PerfReporterRPC.GetPerfPlanRecord(
		l.ctx, &pb.GetPerfPlanRecordReq{
			TaskId:    req.TaskId,
			ExecuteId: req.ExecuteId,
			ProjectId: req.ProjectId,
		},
	)
	if err != nil {
		return nil, err
	}

	// 注：由于此接口一开始设计有误，而且前端已经按该接口实现了，因此这里需要做额外的转换逻辑
	record := out.GetRecord()
	resp = &types.GetPerfPlanRecordResp{
		CurrentPage: 1,
		PageSize:    0,
		TotalCount:  0,
		TotalPage:   1,
		Items:       make([]*types.PerfPlanApiMetricItem, 0, len(record.GetApiMetrics())),
	}
	for _, apiMetric := range record.GetApiMetrics() {
		_apiMetric := &types.APIMetric{RespSuites: []*types.ResponseResult{}}
		if err = utils.Copy(_apiMetric, apiMetric, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy data to response, data: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(apiMetric), err,
			)
		}

		resp.Items = append(
			resp.Items, &types.PerfPlanApiMetricItem{
				TaskId:    record.GetTaskId(),
				ExecuteId: record.GetExecuteId(),
				ProjectId: record.GetProjectId(),
				PlanId:    record.GetPlanId(),
				PlanName:  record.GetPlanName(),
				ApiMetric: _apiMetric,
			},
		)
		resp.TotalCount += 1
	}
	resp.PageSize = uint64(len(resp.Items))

	return resp, nil
}
