package perfReporter

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetPerfPlanRecordV2Logic struct {
	*BaseLogic
}

func NewGetPerfPlanRecordV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPerfPlanRecordV2Logic {
	return &GetPerfPlanRecordV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetPerfPlanRecordV2Logic) GetPerfPlanRecordV2(req *types.GetPerfPlanRecordV2Req) (
	resp *types.GetPerfPlanRecordV2Resp, err error,
) {
	out, err := l.svcCtx.PerfReporterRPC.GetPerfPlanRecord(
		l.ctx, &pb.GetPerfPlanRecordReq{
			TaskId:    req.TaskId,
			ExecuteId: req.ExecuteId,
			ProjectId: req.ProjectId,
		},
	)
	if err != nil {
		return nil, err
	}

	resp = &types.GetPerfPlanRecordV2Resp{PerfPlanRecord: &types.PerfPlanRecord{}}
	if err = utils.Copy(resp.PerfPlanRecord, out.GetRecord(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
