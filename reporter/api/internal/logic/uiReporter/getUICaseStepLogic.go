package uiReporter

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetUICaseStepLogic struct {
	*BaseLogic
}

func NewGetUICaseStepLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUICaseStepLogic {
	return &GetUICaseStepLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetUICaseStepLogic) GetUICaseStep(req *types.GetUICaseStepReq) (resp *types.GetUICaseStepResp, err error) {
	out, err := l.svcCtx.UIReporterRPC.GetUICaseStep(
		l.ctx, &pb.GetUICaseStepReq{
			StepId: req.StepId,
		},
	)
	if err != nil {
		return nil, err
	}

	resp = &types.GetUICaseStepResp{UICaseStep: &types.UICaseStep{}}
	if err = utils.Copy(resp.UICaseStep, out.GetStep(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
