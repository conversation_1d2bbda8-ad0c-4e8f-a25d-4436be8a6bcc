package uiReporter

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetUICaseRecordLogic struct {
	*BaseLogic
}

func NewGetUICaseRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUICaseRecordLogic {
	return &GetUICaseRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetUICaseRecordLogic) GetUICaseRecord(req *types.GetUICaseRecordReq) (
	resp *types.GetUICaseRecordResp, err error,
) {
	out, err := l.svcCtx.UIReporterRPC.GetUICaseRecord(
		l.ctx, &pb.GetUICaseRecordReq{
			TaskId:    req.TaskId,
			ExecuteId: req.ExecuteId,
			ProjectId: req.ProjectId,
		},
	)
	if err != nil {
		return nil, err
	}

	resp = &types.GetUICaseRecordResp{UICaseRecord: &types.UICaseRecord{}}
	if err = utils.Copy(resp.UICaseRecord, out.GetRecord(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
