package uiReporter

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetUIPlanRecordLogic struct {
	*BaseLogic
}

func NewGetUIPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUIPlanRecordLogic {
	return &GetUIPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetUIPlanRecordLogic) GetUIPlanRecord(req *types.GetUIPlanRecordReq) (
	resp *types.GetUIPlanRecordResp, err error,
) {
	out, err := l.svcCtx.UIReporterRPC.GetUIPlanRecord(
		l.ctx, &pb.GetUIPlanRecordReq{
			TaskId:    req.TaskId,
			ExecuteId: req.ExecuteId,
			ProjectId: req.ProjectId,
		},
	)
	if err != nil {
		return nil, err
	}

	resp = &types.GetUIPlanRecordResp{UIPlanRecord: &types.UIPlanRecord{}}
	if err = utils.Copy(resp.UIPlanRecord, out.GetRecord(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
