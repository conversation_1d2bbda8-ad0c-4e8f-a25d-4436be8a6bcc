package uiReporter

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ListUICaseStepListLogic struct {
	*BaseLogic
}

func NewListUICaseStepListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListUICaseStepListLogic {
	return &ListUICaseStepListLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ListUICaseStepListLogic) ListUICaseStepList(req *types.ListUICaseStepReq) (
	resp *types.ListUICaseStepResp, err error,
) {
	out, err := l.svcCtx.UIReporterRPC.ListUICaseStep(
		l.ctx, &pb.ListUICaseStepReq{
			TaskId:      req.TaskId,
			ExecuteId:   req.ExecuteId,
			ProjectId:   req.ProjectId,
			WithContent: req.WithContent,
		},
	)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.ListUICaseStepResp{Items: make([]*types.UICaseStep, 0, out.GetTotalCount())}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
