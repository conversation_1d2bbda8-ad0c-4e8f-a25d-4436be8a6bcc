package uiReporter

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetUIDevicePerfDataLogic struct {
	*BaseLogic
}

func NewGetUIDevicePerfDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUIDevicePerfDataLogic {
	return &GetUIDevicePerfDataLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetUIDevicePerfDataLogic) GetUIDevicePerfData(req *types.GetUIDevicePerfDataReq) (
	resp *types.GetUIDevicePerfDataResp, err error,
) {
	in := &pb.GetUIDevicePerfDataReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}
	out, err := l.svcCtx.UIReporterRPC.GetUIDevicePerfData(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.GetUIDevicePerfDataResp{
		DevicePerfData: &types.DevicePerfData{
			X:      []string{},
			Series: []*types.DevicePerfDataSeries{},
		},
	}
	if err = utils.Copy(resp.DevicePerfData, out.GetData(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
