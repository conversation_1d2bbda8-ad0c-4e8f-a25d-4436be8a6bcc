package uiReporter

import (
	"context"

	"github.com/pkg/errors"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type SearchUISuiteRecordLogic struct {
	*BaseLogic
}

func NewSearchUISuiteRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchUISuiteRecordLogic {
	return &SearchUISuiteRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchUISuiteRecordLogic) SearchUISuiteRecord(req *types.SearchUISuiteRecordReq) (
	resp *types.SearchUISuiteRecordResp, err error,
) {
	in := &pb.SearchUISuiteRecordReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.UIReporterRPC.SearchUISuiteRecord(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchUISuiteRecordResp{Items: make([]*types.UISuiteRecord, 0, len(out.GetItems()))}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
