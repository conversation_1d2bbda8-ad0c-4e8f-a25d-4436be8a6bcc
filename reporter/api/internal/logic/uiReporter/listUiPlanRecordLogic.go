package uiReporter

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/user"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ListUiPlanRecordLogic struct {
	*BaseLogic
}

func NewListUiPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListUiPlanRecordLogic {
	return &ListUiPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ListUiPlanRecordLogic) ListUiPlanRecord(req *types.ListUIPlanRecordReq) (
	resp *types.ListUIPlanRecordResp, err error,
) {
	in := &pb.ListUIPlanRecordRequest{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.UIReporterRPC.ListUIPlanRecord(l.ctx, in)
	if err != nil {
		return nil, err
	}

	resp = &types.ListUIPlanRecordResp{Items: make([]*types.UIPlanRecordItem, 0, len(out.GetItems()))}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	items := out.GetItems()

	// 用户映射
	cache := make(user.Cache)

	for i := range resp.Items {
		executor, userErr := user.GetUser(l.ctx, l.svcCtx.UserRPC, items[i].ExecutedBy, cache)
		if userErr != nil {
			err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
			return nil, err
		}

		resp.Items[i].ExecutedBy = executor
	}

	return resp, nil
}
