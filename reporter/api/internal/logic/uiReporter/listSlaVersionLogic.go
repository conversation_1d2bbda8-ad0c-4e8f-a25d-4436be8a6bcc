package uiReporter

import (
	"context"
	"sort"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/sla"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type ListSlaVersionLogic struct {
	*BaseLogic
}

// 获取指定分支的SLA版本列表
func NewListSlaVersionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListSlaVersionLogic {
	return &ListSlaVersionLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ListSlaVersionLogic) ListSlaVersion(req *types.ListSlaVersionReq) (resp *types.ListSlaVersionResp, err error) {
	platform := platformToString(req.Platform)

	items, err := l.svcCtx.SLAClient.GetVersionInfo(&sla.GetVersionInfoReq{
		Platform: platform,
		Branch:   req.Branch,
	})
	if err != nil {
		return resp, err
	}

	resp2, err := l.svcCtx.SLAClient.GetDefaultBaseVersion(&sla.GetDefaultBaseVersionReq{
		Platform: platform,
		Branch:   req.Branch,
	})
	if err != nil {
		return resp, err
	}

	resp = &types.ListSlaVersionResp{Items: make([]*types.ListSlaVersionRespItem, 0, len(items))}

	// 创建一个 map 来跟踪唯一版本
	uniqueVersions := make(map[string]lang.PlaceholderType)

	for _, item := range items {
		rspt := &types.ListSlaVersionRespItem{}
		if err = utils.Copy(rspt, item, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy data to response, data: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(item), err,
			)
		}

		// 如果这个版本已经添加过，则跳过
		if _, exists := uniqueVersions[rspt.Version]; exists {
			continue
		}

		uniqueVersions[rspt.Version] = lang.Placeholder

		if rspt.Version == resp2.Data.BaseVersion {
			rspt.Default = true
		}
		resp.Items = append(resp.Items, rspt)
	}

	// 按版本号排序，高版本在前
	sort.Slice(resp.Items, func(i, j int) bool {
		return compareVersion(resp.Items[i].Version, resp.Items[j].Version) > 0
	})

	return resp, nil
}

// compareVersion 比较两个版本号，返回 1 表示 v1 > v2，-1 表示 v1 < v2，0 表示相等
func compareVersion(v1, v2 string) int {
	parts1 := strings.Fields(v1) // 分割 "6.66.5 18791" -> ["6.66.5", "18791"]
	parts2 := strings.Fields(v2)

	if len(parts1) < 2 || len(parts2) < 2 {
		return strings.Compare(v1, v2)
	}

	// 比较主版本号 "6.66.5"
	mainVer1 := strings.Split(parts1[0], ".")
	mainVer2 := strings.Split(parts2[0], ".")

	maxLen := len(mainVer1)
	if len(mainVer2) > maxLen {
		maxLen = len(mainVer2)
	}

	for i := 0; i < maxLen; i++ {
		var num1, num2 int
		if i < len(mainVer1) {
			num1, _ = strconv.Atoi(mainVer1[i])
		}
		if i < len(mainVer2) {
			num2, _ = strconv.Atoi(mainVer2[i])
		}

		if num1 > num2 {
			return 1
		} else if num1 < num2 {
			return -1
		}
	}

	// 主版本号相同，比较构建号
	build1, _ := strconv.Atoi(parts1[1])
	build2, _ := strconv.Atoi(parts2[1])

	if build1 > build2 {
		return 1
	} else if build1 < build2 {
		return -1
	}

	return 0
}
