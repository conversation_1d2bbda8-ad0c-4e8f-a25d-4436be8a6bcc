package uiReporter

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	usercommon "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/common"

	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		converters: []qetutils.TypeConverter{
			commonpb.PlatformTypeToString(),
			commonpb.TriggerModeToString(),
			commonpb.StringToPerfDataType(),
			commonpb.PerfDataTypeToString(),
			usercommon.StringToUserInfo(ctx, svcCtx.UserRPC, nil),
			devicehubpb.DeviceStateToString(),
		},
	}
}
