package stabilityReporter

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetStabilityDeviceActivityLogic struct {
	*BaseLogic
}

func NewGetStabilityDeviceActivityLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *GetStabilityDeviceActivityLogic {
	return &GetStabilityDeviceActivityLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetStabilityDeviceActivityLogic) GetStabilityDeviceActivity(req *types.GetStabilityDeviceActivityReq) (
	resp *types.GetStabilityDeviceActivityResp, err error,
) {
	in := &pb.GetStabilityDeviceActivityReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}
	out, err := l.svcCtx.StabilityReporterRPC.GetStabilityDeviceActivity(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.GetStabilityDeviceActivityResp{Activities: make([]*types.DeviceActivity, 0, len(out.Activities))}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
