package uiAgentReporter

import (
	"context"
	"strings"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
)

const notFoundLogs = "not found"

type GetUIAgentComponentLogsLogic struct {
	*BaseLogic
}

func NewGetUIAgentComponentLogsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUIAgentComponentLogsLogic {
	return &GetUIAgentComponentLogsLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetUIAgentComponentLogsLogic) GetUIAgentComponentLogs(req *types.GetUIAgentComponentLogsReq) (
	resp *types.GetUIAgentComponentLogsResp, err error,
) {
	// 检查执行记录是否存在
	_, err = model.CheckUIAgentComponentExecutionRecordByExecuteID(
		l.ctx, l.svcCtx.UIAgentComponentExecutionRecordModel, req.TaskId, req.ExecuteId, req.ProjectId,
	)
	if err != nil {
		return nil, err
	}

	// 调用`ClickPilot`获取任务日志
	logContent, err := l.svcCtx.ClickPilotClient.GetTaskLog(req.ExecuteId) // 执行ID作为Agent的任务ID
	if err != nil {
		if !strings.Contains(err.Error(), notFoundLogs) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CallExternalAPIFailure, err.Error()),
				"failed to get task log from ClickPilot, task_id: %s, execute_id: %s, error: %+v",
				req.TaskId, req.ExecuteId, err,
			)
		}
	}

	return &types.GetUIAgentComponentLogsResp{
		UIAgentComponentLogItem: &types.UIAgentComponentLogItem{
			Content: logContent,
		},
	}, nil
}
