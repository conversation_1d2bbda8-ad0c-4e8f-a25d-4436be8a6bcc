package uiAgentReporter

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
)

type GetUIAgentComponentStepsLogic struct {
	*BaseLogic
}

func NewGetUIAgentComponentStepsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUIAgentComponentStepsLogic {
	return &GetUIAgentComponentStepsLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetUIAgentComponentStepsLogic) GetUIAgentComponentSteps(req *types.GetUIAgentComponentStepsReq) (
	resp *types.GetUIAgentComponentStepsResp, err error,
) {
	// 检查执行记录是否存在
	_, err = model.CheckUIAgentComponentExecutionRecordByExecuteID(
		l.ctx, l.svcCtx.UIAgentComponentExecutionRecordModel, req.TaskId, req.ExecuteId, req.ProjectId,
	)
	if err != nil {
		return nil, err
	}

	// 调用`ClickPilot`获取任务执行步骤记录
	stepRecords, err := l.svcCtx.ClickPilotClient.GetTaskRecord(req.ExecuteId) // 执行ID作为Agent的任务ID
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CallExternalAPIFailure, err.Error()),
			"failed to get task record from ClickPilot, task_id: %s, execute_id: %s, error: %+v",
			req.TaskId, req.ExecuteId, err,
		)
	}

	// 转换`ClickPilot`的`StepRecord`到`API`响应格式
	steps := make([]*commontypes.UIAgentComponentStepRecord, 0, len(stepRecords))
	for _, stepRecord := range stepRecords {
		steps = append(
			steps, &commontypes.UIAgentComponentStepRecord{
				StepID:    stepRecord.StepID,
				Index:     stepRecord.Index,
				Name:      stepRecord.Name,
				Thought:   stepRecord.Thought,
				Action:    stepRecord.Action,
				Status:    string(stepRecord.Status),
				Image:     stepRecord.Image,
				StartedAt: stepRecord.StartedAt,
				EndedAt:   stepRecord.EndedAt,
				CostTime:  stepRecord.CostTime,
			},
		)
	}

	return &types.GetUIAgentComponentStepsResp{
		Steps: steps,
	}, nil
}
