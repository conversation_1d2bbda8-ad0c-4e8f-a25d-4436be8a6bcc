package uiAgentReporter

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetUIAgentComponentRecordLogic struct {
	*BaseLogic
}

func NewGetUIAgentComponentRecordLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *GetUIAgentComponentRecordLogic {
	return &GetUIAgentComponentRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetUIAgentComponentRecordLogic) GetUIAgentComponentRecord(req *types.GetUIAgentComponentRecordReq) (
	resp *types.GetUIAgentComponentRecordResp, err error,
) {
	out, err := l.svcCtx.UIAgentReporterRPC.GetUIAgentComponentRecord(
		l.ctx, &pb.GetUIAgentComponentRecordReq{
			TaskId:    req.TaskId,
			ExecuteId: req.ExecuteId,
			ProjectId: req.ProjectId,
		},
	)
	if err != nil {
		return nil, err
	}

	resp = &types.GetUIAgentComponentRecordResp{UIAgentComponentRecordItem: &types.UIAgentComponentRecordItem{}}
	if err = utils.Copy(resp.UIAgentComponentRecordItem, out.GetRecord(), l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
