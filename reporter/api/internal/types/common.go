package types

type DevicePerfDataSeries struct {
	Name string   `json:"name"` // 指标名称
	Y    []string `json:"y"`    // Y轴数据
}

type DevicePerfData struct {
	DataType string                  `json:"data_type"` // 数据类型
	Unit     string                  `json:"unit"`      // 单位
	X        []string                `json:"x"`         // X轴数据
	Series   []*DevicePerfDataSeries `json:"series"`    // 数据
}

type DeviceActivity struct {
	Name    string `json:"name"`    // 活动名称
	Covered bool   `json:"covered"` // 是否覆盖
}

type ErrorMessage struct {
	Code      uint32 `json:"code"`       // 错误码
	MessageEn string `json:"message_en"` // 英文错误信息
	MessageZh string `json:"message_zh"` // 中文错误信息
}
