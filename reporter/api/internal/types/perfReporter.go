package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type PerfPlanRecord struct {
	TaskId         string                       `json:"task_id"`         // 任务ID
	ExecuteId      string                       `json:"execute_id"`      // 压测计划执行ID
	ProjectId      string                       `json:"project_id"`      // 项目ID
	PlanId         string                       `json:"plan_id"`         // 计划ID
	PlanName       string                       `json:"plan_name"`       // 计划名称
	TriggerMode    string                       `json:"trigger_mode"`    // 触发方式
	TargetMaxRps   uint32                       `json:"target_max_rps"`  // 目标最大的RPS
	TargetDuration uint32                       `json:"target_duration"` // 目标压测持续时长（单位为秒）
	Protocol       string                       `json:"protocol"`        // 协议
	TargetEnv      string                       `json:"target_env"`      // 目标环境
	Status         string                       `json:"status"`          // 执行状态（结果）
	TaskType       string                       `json:"task_type"`       // 任务类型（执行、调试）
	ExecutionMode  string                       `json:"execution_mode"`  // 执行方式（按时长、按次数）
	Services       []*types.PerfServiceMetaData `json:"services"`        // 服务的元数据
	CostTime       int64                        `json:"cost_time"`       // 执行耗时（单位为毫秒）
	MonitorUrls    []*MonitorUrl                `json:"monitor_urls"`    // 监控面板地址
	ExecutedBy     *userinfo.FullUserInfo       `json:"executed_by"`     // 执行者
	StartedAt      int64                        `json:"started_at"`      // 开始时间
	EndedAt        int64                        `json:"ended_at"`        // 结束时间
	ApiMetrics     []*APIMetric                 `json:"api_metrics"`     // 接口指标
	ErrMsg         *ErrorMessage                `json:"err_msg"`         // 错误信息
	CreatedAt      int64                        `json:"created_at"`      // 创建时间
	UpdatedAt      int64                        `json:"updated_at"`      // 更新时间
}

type MonitorUrl struct {
	Name string `json:"name"` // 名称
	Type string `json:"type"` // 类型
	Url  string `json:"url"`  // 跳转地址
}

type PerfPlanApiMetricItem struct {
	TaskId    string     `json:"task_id"`    // 任务ID
	ExecuteId string     `json:"execute_id"` // 压测计划执行ID
	ProjectId string     `json:"project_id"` // 项目ID
	PlanId    string     `json:"plan_id"`    // 计划ID
	PlanName  string     `json:"plan_name"`  // 计划名称
	ApiMetric *APIMetric `json:"api_metric"` // 接口指标
}

type APIMetric struct {
	ApiName       string            `json:"api_name"`       // 接口名称
	ReqSuccessful int64             `json:"req_successful"` // 请求成功次数
	ReqFailed     int64             `json:"req_failed"`     // 请求失败次数
	RespSuites    []*ResponseResult `json:"resp_suites"`    // 响应结果集
}

type ResponseResult struct {
	Result    string                `json:"result"`    // 响应结果
	Count     uint64                `json:"count"`     // 响应总数（样本总数）
	Sum       float64               `json:"sum"`       // 总响应耗时（单位为毫秒）（样本值的大小总和）
	Quantiles []*types.KeyValuePair `json:"quantiles"` // 响应耗时百分位统计数据
	Buckets   []*types.KeyValuePair `json:"buckets"`   // 响应耗时分布统计数据
}

type SearchPerfPlanRecordItem struct {
	TaskId         string                 `json:"task_id"`         // 任务ID
	ExecuteId      string                 `json:"execute_id"`      // 压测计划执行ID
	ProjectId      string                 `json:"project_id"`      // 项目ID
	PlanId         string                 `json:"plan_id"`         // 计划ID
	PlanName       string                 `json:"plan_name"`       // 计划名称
	TriggerMode    string                 `json:"trigger_mode"`    // 触发方式
	TargetMaxRps   uint32                 `json:"target_max_rps"`  // 目标最大的RPS
	TargetDuration uint32                 `json:"target_duration"` // 目标压测持续时长（单位为秒）
	Protocol       string                 `json:"protocol"`        // 协议
	TargetEnv      string                 `json:"target_env"`      // 目标环境
	Status         string                 `json:"status"`          // 执行状态（结果）
	TaskType       string                 `json:"task_type"`       // 任务类型（执行、调试）
	ExecutionMode  string                 `json:"execution_mode"`  // 执行方式（按时长、按次数）
	CostTime       int64                  `json:"cost_time"`       // 执行耗时（单位为毫秒）
	HasMonitorUrl  bool                   `json:"has_monitor_url"` // 监控面板地址是否非空
	ExecutedBy     *userinfo.FullUserInfo `json:"executed_by"`     // 执行者
	StartedAt      int64                  `json:"started_at"`      // 开始时间
	EndedAt        int64                  `json:"ended_at"`        // 结束时间
	ErrMsg         *ErrorMessage          `json:"err_msg"`         // 错误信息
	CreatedAt      int64                  `json:"created_at"`      // 创建时间
	UpdatedAt      int64                  `json:"updated_at"`      // 更新时间
}

type PerfCaseRecord struct {
	TaskId         string                 `json:"task_id"`          // 任务ID
	ExecuteId      string                 `json:"execute_id"`       // 压测用例执行ID
	SuiteExecuteId string                 `json:"suite_execute_id"` // 压测集合执行ID
	PlanExecuteId  string                 `json:"plan_execute_id"`  // 压测计划执行ID
	ProjectId      string                 `json:"project_id"`       // 项目ID
	PlanId         string                 `json:"plan_id"`          // 计划ID
	PlanName       string                 `json:"plan_name"`        // 计划名称
	SuiteId        string                 `json:"suite_id"`         // 集合ID
	SuiteName      string                 `json:"suite_name"`       // 集合名称
	CaseId         string                 `json:"case_id"`          // 用例ID
	CaseName       string                 `json:"case_name"`        // 用例名称
	Steps          []*PerfCaseStepInfo    `json:"steps"`            // 用例步骤
	PerfData       *PerfDataInfo          `json:"perf_data"`        // 压测数据
	LoadGenerator  *types.LoadGenerator   `json:"load_generator"`   // 施压机资源
	Status         string                 `json:"status"`           // 执行状态（结果）
	CostTime       int64                  `json:"cost_time"`        // 执行耗时（单位为毫秒）
	ExecutedBy     *userinfo.FullUserInfo `json:"executed_by"`      // 执行者
	StartedAt      int64                  `json:"started_at"`       // 开始时间
	EndedAt        int64                  `json:"ended_at"`         // 结束时间
	ApiMetrics     []*APIMetric           `json:"api_metrics"`      // 接口指标
	ErrMsg         *ErrorMessage          `json:"err_msg"`          // 错误信息
	CreatedAt      int64                  `json:"created_at"`       // 创建时间
	UpdatedAt      int64                  `json:"updated_at"`       // 更新时间
}

type PerfCaseStepInfo struct {
	Name       string               `json:"name"`        // 步骤名称
	Type       string               `json:"type"`        // 步骤类型
	RateLimits []*types.RateLimitV2 `json:"rate_limits"` // 限流配置
	ApiName    string               `json:"api_name"`    // 接口名称
}

type PerfDataInfo struct {
	ProjectId  string `json:"project_id"`   // 项目ID
	DataId     string `json:"data_id"`      // 压测数据ID
	Name       string `json:"name"`         // 压测数据名称
	NumberOfVu uint32 `json:"number_of_vu"` // 虚拟用户数
}

type SearchPerfPlanRecordReq struct {
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	PlanId     string           `json:"plan_id,omitempty,optional" zh:"计划ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchPerfPlanRecordResp struct {
	CurrentPage uint64                      `json:"current_page"`
	PageSize    uint64                      `json:"page_size"`
	TotalCount  uint64                      `json:"total_count"`
	TotalPage   uint64                      `json:"total_page"`
	Items       []*SearchPerfPlanRecordItem `json:"items"`
}

type GetPerfPlanRecordReq struct {
	TaskId    string `form:"task_id" validate:"required" zh:"任务ID"`
	ExecuteId string `form:"execute_id" validate:"required" zh:"压测计划执行ID"`
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
}

type GetPerfPlanRecordResp struct {
	CurrentPage uint64                   `json:"current_page"`
	PageSize    uint64                   `json:"page_size"`
	TotalCount  uint64                   `json:"total_count"`
	TotalPage   uint64                   `json:"total_page"`
	Items       []*PerfPlanApiMetricItem `json:"items"`
}

type GetPerfPlanRecordV2Req struct {
	TaskId    string `form:"task_id" validate:"required" zh:"任务ID"`
	ExecuteId string `form:"execute_id" validate:"required" zh:"压测计划执行ID"`
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
}

type GetPerfPlanRecordV2Resp struct {
	*PerfPlanRecord
}

type SearchPerfCaseRecordReq struct {
	TaskId     string           `json:"task_id" validate:"required" zh:"任务ID"`
	ExecuteId  string           `json:"execute_id" validate:"required" zh:"压测计划执行ID"`
	ProjectId  string           `json:"project_id" validate:"required" zh:"项目ID"`
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchPerfCaseRecordResp struct {
	CurrentPage uint64            `json:"current_page"`
	PageSize    uint64            `json:"page_size"`
	TotalCount  uint64            `json:"total_count"`
	TotalPage   uint64            `json:"total_page"`
	Items       []*PerfCaseRecord `json:"items"`
}

type GetPerfCaseLogReq struct {
	TaskId    string `form:"task_id" validate:"required" zh:"任务ID"`
	ExecuteId string `form:"execute_id" validate:"required" zh:"压测用例执行ID"`
	ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
}

type GetPerfCaseLogResp struct {
	Content string `json:"content"`
}

type PerfPlanRecordItem struct {
	TaskId         string                 `json:"task_id"`         // 任务ID
	ExecuteId      string                 `json:"execute_id"`      // 压测计划执行ID
	ProjectId      string                 `json:"project_id"`      // 项目ID
	PlanId         string                 `json:"plan_id"`         // 计划ID
	PlanName       string                 `json:"plan_name"`       // 计划名称
	TriggerMode    string                 `json:"trigger_mode"`    // 触发方式
	TargetMaxRps   uint32                 `json:"target_max_rps"`  // 目标最大的RPS
	TargetDuration uint32                 `json:"target_duration"` // 目标压测持续时长（单位为秒）
	Protocol       string                 `json:"protocol"`        // 协议
	TargetEnv      string                 `json:"target_env"`      // 目标环境
	Status         string                 `json:"status"`          // 执行状态（结果）
	TaskType       string                 `json:"task_type"`       // 任务类型（执行、调试）
	ExecutionMode  string                 `json:"execution_mode"`  // 执行方式（按时长、按次数）
	CostTime       int64                  `json:"cost_time"`       // 执行耗时（单位为毫秒）
	HasMonitorUrl  bool                   `json:"has_monitor_url"` // 监控面板地址是否非空
	ExecutedBy     *userinfo.FullUserInfo `json:"executed_by"`     // 执行者
	StartedAt      int64                  `json:"started_at"`      // 开始时间
	EndedAt        int64                  `json:"ended_at"`        // 结束时间
	ErrMsg         *ErrorMessage          `json:"err_msg"`         // 错误信息
	CreatedAt      int64                  `json:"created_at"`      // 创建时间
	UpdatedAt      int64                  `json:"updated_at"`      // 更新时间
}

type ListPerfPlanRecordReq struct {
	ProjectId  string           `json:"project_id" zh:"项目ID"`
	PlanId     string           `json:"plan_id" zh:"计划ID"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type ListPerfPlanRecordResp struct {
	CurrentPage uint64                `json:"current_page"`
	PageSize    uint64                `json:"page_size"`
	TotalCount  uint64                `json:"total_count"`
	TotalPage   uint64                `json:"total_page"`
	Items       []*PerfPlanRecordItem `json:"items"`
}
