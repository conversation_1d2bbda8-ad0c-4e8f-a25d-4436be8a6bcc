syntax = "v1"

import (
	"ui_agent_types.api"
)

@server (
	prefix: reporter/v1
	group: uiAgentReporter
)

service reporter {
    @handler SearchUIAgentComponentRecord
    post /record/ui_agent_component/search (SearchUIAgentComponentRecordReq) returns (SearchUIAgentComponentRecordResp)

    @handler GetUIAgentComponentRecord
    get /record/ui_agent_component/get (GetUIAgentComponentRecordReq) returns (GetUIAgentComponentRecordResp)

    @handler GetUIAgentComponentSteps
    get /record/ui_agent_component/step/get (GetUIAgentComponentStepsReq) returns (GetUIAgentComponentStepsResp)

    @handler GetUIAgentComponentLogs
    get /record/ui_agent_component/log/get (GetUIAgentComponentLogsReq) returns (GetUIAgentComponentLogsResp)
}
