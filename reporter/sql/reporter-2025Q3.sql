-- UI Agent
CREATE TABLE IF NOT EXISTS `ui_agent_component_execution_record`
(
    `id`                 INT         NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `task_id`            VARCHAR(64) NOT NULL COMMENT '任务ID',
    `execute_id`         VARCHAR(64) NOT NULL COMMENT '执行ID',
    `parent_execute_id`  VARCHAR(64) NULL COMMENT '父执行ID',
    `project_id`         VARCHAR(64) NOT NULL COMMENT '项目ID',
    `component_id`       VARCHAR(64) NOT NULL COMMENT '组件ID',
    `component_name`     VARCHAR(64) NOT NULL COMMENT '组件名称',
    `trigger_mode`       VARCHAR(64) NOT NULL COMMENT '触发模式（手动、定时、接口）',
    `execute_type`       TINYINT     NOT NULL COMMENT '执行类型（执行、调试）',
    `application_config` JSON        NOT NULL COMMENT '应用配置',
    `mode`               TINYINT     NOT NULL DEFAULT 1 COMMENT '模式（Agent模式、Step模式）',
    `steps`              JSON        NOT NULL COMMENT '步骤',
    `expectation`        JSON        NOT NULL COMMENT '期望结果',
    `variables`          JSON        NOT NULL COMMENT '变量列表',
    `device`             JSON        NOT NULL COMMENT '设备信息',
    `reinstall`          TINYINT     NOT NULL DEFAULT 0 COMMENT '是否重新安装',
    `restart`            TINYINT     NOT NULL DEFAULT 0 COMMENT '是否重启应用',
    `reference_id`       VARCHAR(64) NULL COMMENT '参考配置ID',
    `status`             VARCHAR(64) NULL COMMENT '执行状态（结果）',
    `executed_by`        VARCHAR(64) NOT NULL COMMENT '执行者的用户ID',
    `started_at`         TIMESTAMP   NULL COMMENT '开始时间',
    `ended_at`           TIMESTAMP   NULL COMMENT '结束时间',
    `cost_time`          BIGINT      NULL     DEFAULT 0 COMMENT '执行耗时（单位为毫秒）',
    `err_msg`            JSON        NULL COMMENT '计划执行错误信息',
    `cleaned`            TINYINT     NOT NULL DEFAULT 0 COMMENT '清理标识（未清理、已清理）',
    `deleted`            TINYINT     NOT NULL DEFAULT 0 COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`         VARCHAR(64) NOT NULL COMMENT '创建者的用户ID',
    `updated_by`         VARCHAR(64) NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`         VARCHAR(64) NULL COMMENT '删除者的用户ID',
    `created_at`         TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`         TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`         TIMESTAMP   NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uix_uacer_task_id_execute_id_project_id` (`task_id`, `execute_id`, `project_id`),
    KEY `ix_uacer_task_id_parent_execute_id` (`task_id`, `parent_execute_id`),
    KEY `ix_uacer_project_id_component_id` (`project_id`, `component_id`),
    KEY `ix_uacer_project_id_component_id_trigger_mode` (`project_id`, `component_id`, `trigger_mode`),
    KEY `ix_uacer_project_id_component_id_execute_type` (`project_id`, `component_id`, `execute_type`),
    KEY `ix_uacer_project_id_status` (`project_id`, `status`),
    KEY `ix_uacer_project_id_updated_at` (`project_id`, `updated_at`),
    KEY `ix_uacer_project_id_steps_image` (`project_id`, (CAST(`steps`->'$[*].expectation.image' AS CHAR(64) ARRAY))),
    KEY `ix_uacer_project_id_expectation_image` (`project_id`, (CAST(`expectation`->'$.image' AS CHAR(64) ARRAY))),
    KEY `ix_uacer_parent_execute_id_execute_type_cleaned_created_at` (`parent_execute_id`, `execute_type`, `cleaned`, `created_at`),
    KEY `ix_uacer_cleaned_created_at` (`cleaned`, `created_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'UI Agent组件执行记录表';
