// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: reporter/common.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.PerfDataType(0)
)

// Validate checks the field values on PerfData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PerfData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PerfDataMultiError, or nil
// if none found.
func (m *PerfData) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DataType

	// no validation rules for Unit

	for idx, item := range m.GetSeries() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PerfDataValidationError{
						field:  fmt.Sprintf("Series[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PerfDataValidationError{
						field:  fmt.Sprintf("Series[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PerfDataValidationError{
					field:  fmt.Sprintf("Series[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PerfDataMultiError(errors)
	}

	return nil
}

// PerfDataMultiError is an error wrapping multiple validation errors returned
// by PerfData.ValidateAll() if the designated constraints aren't met.
type PerfDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfDataMultiError) AllErrors() []error { return m }

// PerfDataValidationError is the validation error returned by
// PerfData.Validate if the designated constraints aren't met.
type PerfDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfDataValidationError) ErrorName() string { return "PerfDataValidationError" }

// Error satisfies the builtin error interface
func (e PerfDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfDataValidationError{}

// Validate checks the field values on ErrorMessage with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ErrorMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ErrorMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ErrorMessageMultiError, or
// nil if none found.
func (m *ErrorMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *ErrorMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for MessageEn

	// no validation rules for MessageZh

	if len(errors) > 0 {
		return ErrorMessageMultiError(errors)
	}

	return nil
}

// ErrorMessageMultiError is an error wrapping multiple validation errors
// returned by ErrorMessage.ValidateAll() if the designated constraints aren't met.
type ErrorMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ErrorMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ErrorMessageMultiError) AllErrors() []error { return m }

// ErrorMessageValidationError is the validation error returned by
// ErrorMessage.Validate if the designated constraints aren't met.
type ErrorMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ErrorMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ErrorMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ErrorMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ErrorMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ErrorMessageValidationError) ErrorName() string { return "ErrorMessageValidationError" }

// Error satisfies the builtin error interface
func (e ErrorMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sErrorMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ErrorMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ErrorMessageValidationError{}

// Validate checks the field values on DeviceActivity with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeviceActivity) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceActivity with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeviceActivityMultiError,
// or nil if none found.
func (m *DeviceActivity) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceActivity) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Covered

	if len(errors) > 0 {
		return DeviceActivityMultiError(errors)
	}

	return nil
}

// DeviceActivityMultiError is an error wrapping multiple validation errors
// returned by DeviceActivity.ValidateAll() if the designated constraints
// aren't met.
type DeviceActivityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceActivityMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceActivityMultiError) AllErrors() []error { return m }

// DeviceActivityValidationError is the validation error returned by
// DeviceActivity.Validate if the designated constraints aren't met.
type DeviceActivityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceActivityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceActivityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceActivityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceActivityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceActivityValidationError) ErrorName() string { return "DeviceActivityValidationError" }

// Error satisfies the builtin error interface
func (e DeviceActivityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceActivity.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceActivityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceActivityValidationError{}

// Validate checks the field values on PerfData_Series with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PerfData_Series) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PerfData_Series with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PerfData_SeriesMultiError, or nil if none found.
func (m *PerfData_Series) ValidateAll() error {
	return m.validate(true)
}

func (m *PerfData_Series) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return PerfData_SeriesMultiError(errors)
	}

	return nil
}

// PerfData_SeriesMultiError is an error wrapping multiple validation errors
// returned by PerfData_Series.ValidateAll() if the designated constraints
// aren't met.
type PerfData_SeriesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PerfData_SeriesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PerfData_SeriesMultiError) AllErrors() []error { return m }

// PerfData_SeriesValidationError is the validation error returned by
// PerfData_Series.Validate if the designated constraints aren't met.
type PerfData_SeriesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PerfData_SeriesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PerfData_SeriesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PerfData_SeriesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PerfData_SeriesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PerfData_SeriesValidationError) ErrorName() string { return "PerfData_SeriesValidationError" }

// Error satisfies the builtin error interface
func (e PerfData_SeriesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPerfData_Series.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PerfData_SeriesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PerfData_SeriesValidationError{}
