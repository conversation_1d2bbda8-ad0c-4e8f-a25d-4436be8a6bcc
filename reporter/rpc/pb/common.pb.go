// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: reporter/common.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PerfData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DataType      pb.PerfDataType        `protobuf:"varint,1,opt,name=data_type,json=dataType,proto3,enum=common.PerfDataType" json:"data_type,omitempty"` // 数据类型
	Unit          string                 `protobuf:"bytes,2,opt,name=unit,proto3" json:"unit,omitempty"`                                                   // 单位
	X             []string               `protobuf:"bytes,3,rep,name=x,proto3" json:"x,omitempty"`                                                         // X轴数据
	Series        []*PerfData_Series     `protobuf:"bytes,4,rep,name=series,proto3" json:"series,omitempty"`                                               // 数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfData) Reset() {
	*x = PerfData{}
	mi := &file_reporter_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfData) ProtoMessage() {}

func (x *PerfData) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfData.ProtoReflect.Descriptor instead.
func (*PerfData) Descriptor() ([]byte, []int) {
	return file_reporter_common_proto_rawDescGZIP(), []int{0}
}

func (x *PerfData) GetDataType() pb.PerfDataType {
	if x != nil {
		return x.DataType
	}
	return pb.PerfDataType(0)
}

func (x *PerfData) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *PerfData) GetX() []string {
	if x != nil {
		return x.X
	}
	return nil
}

func (x *PerfData) GetSeries() []*PerfData_Series {
	if x != nil {
		return x.Series
	}
	return nil
}

type ErrorMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`                           // 错误码
	MessageEn     string                 `protobuf:"bytes,2,opt,name=message_en,json=messageEn,proto3" json:"message_en,omitempty"` // 英文错误信息
	MessageZh     string                 `protobuf:"bytes,3,opt,name=message_zh,json=messageZh,proto3" json:"message_zh,omitempty"` // 中文错误信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorMessage) Reset() {
	*x = ErrorMessage{}
	mi := &file_reporter_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorMessage) ProtoMessage() {}

func (x *ErrorMessage) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorMessage.ProtoReflect.Descriptor instead.
func (*ErrorMessage) Descriptor() ([]byte, []int) {
	return file_reporter_common_proto_rawDescGZIP(), []int{1}
}

func (x *ErrorMessage) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ErrorMessage) GetMessageEn() string {
	if x != nil {
		return x.MessageEn
	}
	return ""
}

func (x *ErrorMessage) GetMessageZh() string {
	if x != nil {
		return x.MessageZh
	}
	return ""
}

type DeviceActivity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`        // 活动名称
	Covered       bool                   `protobuf:"varint,2,opt,name=covered,proto3" json:"covered,omitempty"` // 是否覆盖
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceActivity) Reset() {
	*x = DeviceActivity{}
	mi := &file_reporter_common_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceActivity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceActivity) ProtoMessage() {}

func (x *DeviceActivity) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_common_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceActivity.ProtoReflect.Descriptor instead.
func (*DeviceActivity) Descriptor() ([]byte, []int) {
	return file_reporter_common_proto_rawDescGZIP(), []int{2}
}

func (x *DeviceActivity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeviceActivity) GetCovered() bool {
	if x != nil {
		return x.Covered
	}
	return false
}

type PerfData_Series struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // 指标名称
	Y             []string               `protobuf:"bytes,2,rep,name=y,proto3" json:"y,omitempty"`       // Y轴数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfData_Series) Reset() {
	*x = PerfData_Series{}
	mi := &file_reporter_common_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfData_Series) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfData_Series) ProtoMessage() {}

func (x *PerfData_Series) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_common_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfData_Series.ProtoReflect.Descriptor instead.
func (*PerfData_Series) Descriptor() ([]byte, []int) {
	return file_reporter_common_proto_rawDescGZIP(), []int{0, 0}
}

func (x *PerfData_Series) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfData_Series) GetY() []string {
	if x != nil {
		return x.Y
	}
	return nil
}

var File_reporter_common_proto protoreflect.FileDescriptor

var file_reporter_common_proto_rawDesc = []byte{
	0x0a, 0x15, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x72, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbe, 0x01, 0x0a, 0x08, 0x50, 0x65, 0x72, 0x66, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x31, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65,
	0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x01, 0x78, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x1a, 0x2a, 0x0a, 0x06, 0x53, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x01, 0x79, 0x22, 0x60, 0x0a, 0x0c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x5f, 0x7a, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x5a, 0x68, 0x22, 0x3e, 0x0a, 0x0e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x65, 0x64, 0x42, 0x42, 0x5a, 0x40, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65,
	0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72,
	0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_reporter_common_proto_rawDescOnce sync.Once
	file_reporter_common_proto_rawDescData = file_reporter_common_proto_rawDesc
)

func file_reporter_common_proto_rawDescGZIP() []byte {
	file_reporter_common_proto_rawDescOnce.Do(func() {
		file_reporter_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_reporter_common_proto_rawDescData)
	})
	return file_reporter_common_proto_rawDescData
}

var file_reporter_common_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_reporter_common_proto_goTypes = []any{
	(*PerfData)(nil),        // 0: reporter.PerfData
	(*ErrorMessage)(nil),    // 1: reporter.ErrorMessage
	(*DeviceActivity)(nil),  // 2: reporter.DeviceActivity
	(*PerfData_Series)(nil), // 3: reporter.PerfData.Series
	(pb.PerfDataType)(0),    // 4: common.PerfDataType
}
var file_reporter_common_proto_depIdxs = []int32{
	4, // 0: reporter.PerfData.data_type:type_name -> common.PerfDataType
	3, // 1: reporter.PerfData.series:type_name -> reporter.PerfData.Series
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_reporter_common_proto_init() }
func file_reporter_common_proto_init() {
	if File_reporter_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_reporter_common_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_reporter_common_proto_goTypes,
		DependencyIndexes: file_reporter_common_proto_depIdxs,
		MessageInfos:      file_reporter_common_proto_msgTypes,
	}.Build()
	File_reporter_common_proto = out.File
	file_reporter_common_proto_rawDesc = nil
	file_reporter_common_proto_goTypes = nil
	file_reporter_common_proto_depIdxs = nil
}
