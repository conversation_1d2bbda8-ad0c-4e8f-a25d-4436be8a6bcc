package internal

import (
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/serverinterceptors"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/tasks"
)

var noNeedUserMethods = []string{
	"/reporter.reporter/createRecord",
	"/reporter.reporter/modifyRecord",
	"/reporter.reporter/getExecuteRecord",
	"/reporter.reporter/getParentRecord",
	"/reporter.reporter/getChildrenRecord",
	"/reporter.reporter/createInterfaceRecord",
	"/reporter.reporter/modifyInterfaceRecord",
	"/reporter.reporter/getCaseLatestRecord",
	"/reporter.reporter/listInterfaceRecord",
	"/reporter.reporter/getInterfaceRecord",
	"/reporter.reporter/createSuiteRecord",
	"/reporter.reporter/modifySuiteRecord",
	"/reporter.reporter/listSuiteRecord",
	"/reporter.reporter/getSuiteRecord",
	"/reporter.reporter/createPlanRecord",
	"/reporter.reporter/modifyPlanRecord",
	"/reporter.reporter/listPlanRecord",
	"/reporter.reporter/getPlanRecord",
	"/reporter.reporter/getPlanTimeScale",
	"/reporter.UIReporter/createUIPlanRecord",
	"/reporter.UIReporter/modifyUIPlanRecord",
	"/reporter.UIReporter/listUIPlanRecord",
}

func HandleSetupOperations(svcCtx *svc.ServiceContext) error {
	// register no need user full methods for user info server interceptor
	registerNoNeedUserFullMethod()

	if err := registerTasks(svcCtx); err != nil {
		return errors.Wrap(err, "failed to register tasks")
	}

	return nil
}

func registerNoNeedUserFullMethod() {
	for _, fullMethod := range noNeedUserMethods {
		serverinterceptors.NoNeedUserForFullMethod(fullMethod)
	}
}

func registerTasks(svcCtx *svc.ServiceContext) error {
	return svcCtx.Consumer.RegisterHandlers(
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeReporterHandleUIAgentRecordTask, tasks.NewHandleUIAgentRecordTaskProcessor(svcCtx),
		),
	)
}
