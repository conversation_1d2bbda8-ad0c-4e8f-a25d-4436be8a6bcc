// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: reporter.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	uiagentreporterlogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/logic/uiagentreporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type UIAgentReporterServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedUIAgentReporterServer
}

func NewUIAgentReporterServer(svcCtx *svc.ServiceContext) *UIAgentReporterServer {
	return &UIAgentReporterServer{
		svcCtx: svcCtx,
	}
}

// CreateUIAgentComponentRecord 创建`UI Agent`组件执行记录
func (s *UIAgentReporterServer) CreateUIAgentComponentRecord(ctx context.Context, in *pb.CreateUIAgentComponentRecordReq) (*pb.CreateUIAgentComponentRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiagentreporterlogic.NewCreateUIAgentComponentRecordLogic(ctx, s.svcCtx)

	return l.CreateUIAgentComponentRecord(in)
}

// ModifyUIAgentComponentRecord 修改`UI Agent`组件执行记录
func (s *UIAgentReporterServer) ModifyUIAgentComponentRecord(ctx context.Context, in *pb.ModifyUIAgentComponentRecordReq) (*pb.ModifyUIAgentComponentRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiagentreporterlogic.NewModifyUIAgentComponentRecordLogic(ctx, s.svcCtx)

	return l.ModifyUIAgentComponentRecord(in)
}

// SearchUIAgentComponentRecord 搜索`UI Agent`组件执行记录
func (s *UIAgentReporterServer) SearchUIAgentComponentRecord(ctx context.Context, in *pb.SearchUIAgentComponentRecordReq) (*pb.SearchUIAgentComponentRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiagentreporterlogic.NewSearchUIAgentComponentRecordLogic(ctx, s.svcCtx)

	return l.SearchUIAgentComponentRecord(in)
}

// GetUIAgentComponentRecord 获取`UI Agent`组件执行记录
func (s *UIAgentReporterServer) GetUIAgentComponentRecord(ctx context.Context, in *pb.GetUIAgentComponentRecordReq) (*pb.GetUIAgentComponentRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiagentreporterlogic.NewGetUIAgentComponentRecordLogic(ctx, s.svcCtx)

	return l.GetUIAgentComponentRecord(in)
}

// GetCountOfUIAgentComponentRecordsByImage 获取涉及指定图片的`UI Agent`组件执行记录数量
func (s *UIAgentReporterServer) GetCountOfUIAgentComponentRecordsByImage(ctx context.Context, in *pb.GetCountOfUIAgentComponentRecordsByImageReq) (*pb.GetCountOfUIAgentComponentRecordsByImageResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uiagentreporterlogic.NewGetCountOfUIAgentComponentRecordsByImageLogic(ctx, s.svcCtx)

	return l.GetCountOfUIAgentComponentRecordsByImage(in)
}
