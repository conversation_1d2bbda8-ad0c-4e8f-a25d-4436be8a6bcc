// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: reporter.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	perfreporterlogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/logic/perfreporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type PerfReporterServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedPerfReporterServer
}

func NewPerfReporterServer(svcCtx *svc.ServiceContext) *PerfReporterServer {
	return &PerfReporterServer{
		svcCtx: svcCtx,
	}
}

// CreatePerfCaseRecord 创建压测用例执行记录
func (s *PerfReporterServer) CreatePerfCaseRecord(ctx context.Context, in *pb.CreatePerfCaseRecordReq) (*pb.CreatePerfCaseRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfreporterlogic.NewCreatePerfCaseRecordLogic(ctx, s.svcCtx)

	return l.CreatePerfCaseRecord(in)
}

// ModifyPerfCaseRecord 修改压测用例执行记录
func (s *PerfReporterServer) ModifyPerfCaseRecord(ctx context.Context, in *pb.ModifyPerfCaseRecordReq) (*pb.ModifyPerfCaseRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfreporterlogic.NewModifyPerfCaseRecordLogic(ctx, s.svcCtx)

	return l.ModifyPerfCaseRecord(in)
}

// GetPerfCaseRecord 获取压测用例执行记录
func (s *PerfReporterServer) GetPerfCaseRecord(ctx context.Context, in *pb.GetPerfCaseRecordReq) (*pb.GetPerfCaseRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfreporterlogic.NewGetPerfCaseRecordLogic(ctx, s.svcCtx)

	return l.GetPerfCaseRecord(in)
}

// CreatePerfSuiteRecord 创建压测集合执行记录
func (s *PerfReporterServer) CreatePerfSuiteRecord(ctx context.Context, in *pb.CreatePerfSuiteRecordReq) (*pb.CreatePerfSuiteRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfreporterlogic.NewCreatePerfSuiteRecordLogic(ctx, s.svcCtx)

	return l.CreatePerfSuiteRecord(in)
}

// ModifyPerfSuiteRecord 修改压测集合执行记录
func (s *PerfReporterServer) ModifyPerfSuiteRecord(ctx context.Context, in *pb.ModifyPerfSuiteRecordReq) (*pb.ModifyPerfSuiteRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfreporterlogic.NewModifyPerfSuiteRecordLogic(ctx, s.svcCtx)

	return l.ModifyPerfSuiteRecord(in)
}

// GetPerfSuiteRecord 获取压测集合执行记录
func (s *PerfReporterServer) GetPerfSuiteRecord(ctx context.Context, in *pb.GetPerfSuiteRecordReq) (*pb.GetPerfSuiteRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfreporterlogic.NewGetPerfSuiteRecordLogic(ctx, s.svcCtx)

	return l.GetPerfSuiteRecord(in)
}

// CreatePerfPlanRecord 创建压测计划执行记录
func (s *PerfReporterServer) CreatePerfPlanRecord(ctx context.Context, in *pb.CreatePerfPlanRecordReq) (*pb.CreatePerfPlanRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfreporterlogic.NewCreatePerfPlanRecordLogic(ctx, s.svcCtx)

	return l.CreatePerfPlanRecord(in)
}

// ModifyPerfPlanRecord 修改压测计划执行记录
func (s *PerfReporterServer) ModifyPerfPlanRecord(ctx context.Context, in *pb.ModifyPerfPlanRecordReq) (*pb.ModifyPerfPlanRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfreporterlogic.NewModifyPerfPlanRecordLogic(ctx, s.svcCtx)

	return l.ModifyPerfPlanRecord(in)
}

// SearchPerfPlanRecord 搜索压测计划执行记录
func (s *PerfReporterServer) SearchPerfPlanRecord(ctx context.Context, in *pb.SearchPerfPlanRecordReq) (*pb.SearchPerfPlanRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfreporterlogic.NewSearchPerfPlanRecordLogic(ctx, s.svcCtx)

	return l.SearchPerfPlanRecord(in)
}

// GetPerfPlanRecord 获取压测计划执行记录
func (s *PerfReporterServer) GetPerfPlanRecord(ctx context.Context, in *pb.GetPerfPlanRecordReq) (*pb.GetPerfPlanRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfreporterlogic.NewGetPerfPlanRecordLogic(ctx, s.svcCtx)

	return l.GetPerfPlanRecord(in)
}

// SearchPerfCaseRecord 搜索压测计划执行记录下的压测用例执行记录
func (s *PerfReporterServer) SearchPerfCaseRecord(ctx context.Context, in *pb.SearchPerfCaseRecordReq) (*pb.SearchPerfCaseRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfreporterlogic.NewSearchPerfCaseRecordLogic(ctx, s.svcCtx)

	return l.SearchPerfCaseRecord(in)
}

// UpdateMonitorURLOfPerfPlanRecord 更新压测计划执行记录的`metric_url`字段
func (s *PerfReporterServer) UpdateMonitorURLOfPerfPlanRecord(ctx context.Context, in *pb.UpdateMonitorURLOfPerfPlanRecordReq) (*pb.UpdateMonitorURLOfPerfPlanRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := perfreporterlogic.NewUpdateMonitorURLOfPerfPlanRecordLogic(ctx, s.svcCtx)

	return l.UpdateMonitorURLOfPerfPlanRecord(in)
}
