// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: reporter.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	uireporterlogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/logic/uireporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type UIReporterServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedUIReporterServer
}

func NewUIReporterServer(svcCtx *svc.ServiceContext) *UIReporterServer {
	return &UIReporterServer{
		svcCtx: svcCtx,
	}
}

// CreateUICaseRecord 创建UI用例执行记录
func (s *UIReporterServer) CreateUICaseRecord(ctx context.Context, in *pb.PutUICaseRecordRequest) (*pb.CreateUICaseRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewCreateUICaseRecordLogic(ctx, s.svcCtx)

	return l.CreateUICaseRecord(in)
}

// ModifyUICaseRecord 修改UI用例执行记录
func (s *UIReporterServer) ModifyUICaseRecord(ctx context.Context, in *pb.PutUICaseRecordRequest) (*pb.ModifyUICaseRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewModifyUICaseRecordLogic(ctx, s.svcCtx)

	return l.ModifyUICaseRecord(in)
}

// CreateUISuiteRecord 创建UI集合执行记录
func (s *UIReporterServer) CreateUISuiteRecord(ctx context.Context, in *pb.PutUISuiteRecordRequest) (*pb.CreateUISuiteRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewCreateUISuiteRecordLogic(ctx, s.svcCtx)

	return l.CreateUISuiteRecord(in)
}

// ModifyUISuiteRecord 修改UI集合执行记录
func (s *UIReporterServer) ModifyUISuiteRecord(ctx context.Context, in *pb.PutUISuiteRecordRequest) (*pb.ModifyUISuiteRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewModifyUISuiteRecordLogic(ctx, s.svcCtx)

	return l.ModifyUISuiteRecord(in)
}

// CreateUIPlanRecord 创建UI计划执行记录
func (s *UIReporterServer) CreateUIPlanRecord(ctx context.Context, in *pb.PutUIPlanRecordRequest) (*pb.CreateUIPlanRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewCreateUIPlanRecordLogic(ctx, s.svcCtx)

	return l.CreateUIPlanRecord(in)
}

// ModifyUIPlanRecord 修改UI计划执行记录
func (s *UIReporterServer) ModifyUIPlanRecord(ctx context.Context, in *pb.PutUIPlanRecordRequest) (*pb.ModifyUIPlanRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewModifyUIPlanRecordLogic(ctx, s.svcCtx)

	return l.ModifyUIPlanRecord(in)
}

// ViewUIPlanRecord 查看UI计划执行记录
func (s *UIReporterServer) ViewUIPlanRecord(ctx context.Context, in *pb.ViewUIPlanRecordRequest) (*pb.ViewUIPlanRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewViewUIPlanRecordLogic(ctx, s.svcCtx)

	return l.ViewUIPlanRecord(in)
}

// ListUIPlanRecord UI计划执行记录列表
func (s *UIReporterServer) ListUIPlanRecord(ctx context.Context, in *pb.ListUIPlanRecordRequest) (*pb.ListUIPlanRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewListUIPlanRecordLogic(ctx, s.svcCtx)

	return l.ListUIPlanRecord(in)
}

// GetUIPlanCasesInfo 获取UI计划关联用例信息
func (s *UIReporterServer) GetUIPlanCasesInfo(ctx context.Context, in *pb.GetUIPlanCasesInfoRequest) (*pb.GetUIPlanCasesInfoResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewGetUIPlanCasesInfoLogic(ctx, s.svcCtx)

	return l.GetUIPlanCasesInfo(in)
}

// GetUIPlanRecord 获取UI计划执行记录
func (s *UIReporterServer) GetUIPlanRecord(ctx context.Context, in *pb.GetUIPlanRecordReq) (*pb.GetUIPlanRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewGetUIPlanRecordLogic(ctx, s.svcCtx)

	return l.GetUIPlanRecord(in)
}

// SearchUISuiteRecord 搜索UI计划执行记录下的UI集合执行记录
func (s *UIReporterServer) SearchUISuiteRecord(ctx context.Context, in *pb.SearchUISuiteRecordReq) (*pb.SearchUISuiteRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewSearchUISuiteRecordLogic(ctx, s.svcCtx)

	return l.SearchUISuiteRecord(in)
}

// SearchUICaseRecord 搜索UI集合执行记录下的UI用例执行记录
func (s *UIReporterServer) SearchUICaseRecord(ctx context.Context, in *pb.SearchUICaseRecordReq) (*pb.SearchUICaseRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewSearchUICaseRecordLogic(ctx, s.svcCtx)

	return l.SearchUICaseRecord(in)
}

// GetUICaseRecord 获取UI用例执行记录
func (s *UIReporterServer) GetUICaseRecord(ctx context.Context, in *pb.GetUICaseRecordReq) (*pb.GetUICaseRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewGetUICaseRecordLogic(ctx, s.svcCtx)

	return l.GetUICaseRecord(in)
}

// ListUICaseStep 获取UI用例执行步骤列表
func (s *UIReporterServer) ListUICaseStep(ctx context.Context, in *pb.ListUICaseStepReq) (*pb.ListUICaseStepResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewListUICaseStepLogic(ctx, s.svcCtx)

	return l.ListUICaseStep(in)
}

// GetUICaseStep 获取UI用例执行步骤
func (s *UIReporterServer) GetUICaseStep(ctx context.Context, in *pb.GetUICaseStepReq) (*pb.GetUICaseStepResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewGetUICaseStepLogic(ctx, s.svcCtx)

	return l.GetUICaseStep(in)
}

// SearchUIDeviceRecord 搜索UI计划执行记录下的设备记录
func (s *UIReporterServer) SearchUIDeviceRecord(ctx context.Context, in *pb.SearchUIDeviceRecordReq) (*pb.SearchUIDeviceRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewSearchUIDeviceRecordLogic(ctx, s.svcCtx)

	return l.SearchUIDeviceRecord(in)
}

// SaveUIDevicePerfData 保存UI测试设备性能数据
func (s *UIReporterServer) SaveUIDevicePerfData(ctx context.Context, in *pb.SaveUIDevicePerfDataReq) (*pb.SaveUIDevicePerfDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewSaveUIDevicePerfDataLogic(ctx, s.svcCtx)

	return l.SaveUIDevicePerfData(in)
}

// GetUIDevicePerfData 获取UI测试设备性能数据
func (s *UIReporterServer) GetUIDevicePerfData(ctx context.Context, in *pb.GetUIDevicePerfDataReq) (*pb.GetUIDevicePerfDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := uireporterlogic.NewGetUIDevicePerfDataLogic(ctx, s.svcCtx)

	return l.GetUIDevicePerfData(in)
}
