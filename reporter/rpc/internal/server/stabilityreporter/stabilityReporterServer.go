// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: reporter.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	stabilityreporterlogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/logic/stabilityreporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type StabilityReporterServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedStabilityReporterServer
}

func NewStabilityReporterServer(svcCtx *svc.ServiceContext) *StabilityReporterServer {
	return &StabilityReporterServer{
		svcCtx: svcCtx,
	}
}

// ListStabilityPlanRecord 获取稳测计划的执行记录
func (s *StabilityReporterServer) ListStabilityPlanRecord(ctx context.Context, in *pb.ListStabilityPlanRecordReq) (*pb.ListStabilityPlanRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityreporterlogic.NewListStabilityPlanRecordLogic(ctx, s.svcCtx)

	return l.ListStabilityPlanRecord(in)
}

// SearchStabilityPlanRecord 搜索稳测的执行记录
func (s *StabilityReporterServer) SearchStabilityPlanRecord(ctx context.Context, in *pb.SearchStabilityPlanRecordReq) (*pb.SearchStabilityPlanRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityreporterlogic.NewSearchStabilityPlanRecordLogic(ctx, s.svcCtx)

	return l.SearchStabilityPlanRecord(in)
}

// GetStabilityPlanRecord 获取稳测执行报告的计划信息
func (s *StabilityReporterServer) GetStabilityPlanRecord(ctx context.Context, in *pb.GetStabilityPlanRecordReq) (*pb.GetStabilityPlanRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityreporterlogic.NewGetStabilityPlanRecordLogic(ctx, s.svcCtx)

	return l.GetStabilityPlanRecord(in)
}

// CreateStabilityPlanRecord 创建稳测的执行记录
func (s *StabilityReporterServer) CreateStabilityPlanRecord(ctx context.Context, in *pb.PutStabilityPlanRecordReq) (*pb.CreateStabilityPlanRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityreporterlogic.NewCreateStabilityPlanRecordLogic(ctx, s.svcCtx)

	return l.CreateStabilityPlanRecord(in)
}

// ModifyStabilityPlanRecord 修改稳测执行记录
func (s *StabilityReporterServer) ModifyStabilityPlanRecord(ctx context.Context, in *pb.PutStabilityPlanRecordReq) (*pb.ModifyStabilityPlanRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityreporterlogic.NewModifyStabilityPlanRecordLogic(ctx, s.svcCtx)

	return l.ModifyStabilityPlanRecord(in)
}

// SearchStabilityDeviceRecord 搜索稳测执行报告的总览设备
func (s *StabilityReporterServer) SearchStabilityDeviceRecord(ctx context.Context, in *pb.SearchStabilityDeviceRecordReq) (*pb.SearchStabilityDeviceRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityreporterlogic.NewSearchStabilityDeviceRecordLogic(ctx, s.svcCtx)

	return l.SearchStabilityDeviceRecord(in)
}

// CreateStabilityDeviceRecord 创建稳测设备的执行记录
func (s *StabilityReporterServer) CreateStabilityDeviceRecord(ctx context.Context, in *pb.PutStabilityDeviceRecordReq) (*pb.CreateStabilityDeviceRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityreporterlogic.NewCreateStabilityDeviceRecordLogic(ctx, s.svcCtx)

	return l.CreateStabilityDeviceRecord(in)
}

// ModifyStabilityDeviceRecord 修改稳测设备的执行记录
func (s *StabilityReporterServer) ModifyStabilityDeviceRecord(ctx context.Context, in *pb.PutStabilityDeviceRecordReq) (*pb.ModifyStabilityDeviceRecordResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityreporterlogic.NewModifyStabilityDeviceRecordLogic(ctx, s.svcCtx)

	return l.ModifyStabilityDeviceRecord(in)
}

// ListStabilityDeviceStep 获取稳测执行报告的设备步骤日志
func (s *StabilityReporterServer) ListStabilityDeviceStep(ctx context.Context, in *pb.ListStabilityDeviceStepReq) (*pb.ListStabilityDeviceStepResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityreporterlogic.NewListStabilityDeviceStepLogic(ctx, s.svcCtx)

	return l.ListStabilityDeviceStep(in)
}

// GetStabilityDevicePerfData 获取稳测执行报告的设备性能数据
func (s *StabilityReporterServer) GetStabilityDevicePerfData(ctx context.Context, in *pb.GetStabilityDevicePerfDataReq) (*pb.GetStabilityDevicePerfDataResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityreporterlogic.NewGetStabilityDevicePerfDataLogic(ctx, s.svcCtx)

	return l.GetStabilityDevicePerfData(in)
}

// GetStabilityDeviceActivity 获取稳测执行报告的设备Activity统计
func (s *StabilityReporterServer) GetStabilityDeviceActivity(ctx context.Context, in *pb.GetStabilityDeviceActivityReq) (*pb.GetStabilityDeviceActivityResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := stabilityreporterlogic.NewGetStabilityDeviceActivityLogic(ctx, s.svcCtx)

	return l.GetStabilityDeviceActivity(in)
}
