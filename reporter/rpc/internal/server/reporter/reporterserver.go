// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: reporter.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	reporterlogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/logic/reporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ReporterServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedReporterServer
}

func NewReporterServer(svcCtx *svc.ServiceContext) *ReporterServer {
	return &ReporterServer{
		svcCtx: svcCtx,
	}
}

// 组件组及用例记录 RPC接口
func (s *ReporterServer) CreateRecord(ctx context.Context, in *pb.PutRecordRequest) (*pb.CreateRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewCreateRecordLogic(ctx, s.svcCtx)

	return l.CreateRecord(in)
}

func (s *ReporterServer) ModifyRecord(ctx context.Context, in *pb.PutRecordRequest) (*pb.ModifyRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewModifyRecordLogic(ctx, s.svcCtx)

	return l.ModifyRecord(in)
}

// 查询自身的RPC接口
func (s *ReporterServer) GetExecuteRecord(ctx context.Context, in *pb.GetExecuteRecordRequest) (*pb.GetExecuteRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewGetExecuteRecordLogic(ctx, s.svcCtx)

	return l.GetExecuteRecord(in)
}

// 查询Parent的RPC接口
func (s *ReporterServer) GetParentRecord(ctx context.Context, in *pb.GetParentRecordRequest) (*pb.GetParentRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewGetParentRecordLogic(ctx, s.svcCtx)

	return l.GetParentRecord(in)
}

// 查询Children的RPC接口
func (s *ReporterServer) GetChildrenRecord(ctx context.Context, in *pb.GetChildrenRecordRequest) (*pb.GetChildrenRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewGetChildrenRecordLogic(ctx, s.svcCtx)

	return l.GetChildrenRecord(in)
}

// `接口`
func (s *ReporterServer) CreateInterfaceRecord(ctx context.Context, in *pb.PutInterfaceRecordRequest) (*pb.CreateInterfaceRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewCreateInterfaceRecordLogic(ctx, s.svcCtx)

	return l.CreateInterfaceRecord(in)
}

// 修改接口记录
func (s *ReporterServer) ModifyInterfaceRecord(ctx context.Context, in *pb.PutInterfaceRecordRequest) (*pb.ModifyInterfaceRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewModifyInterfaceRecordLogic(ctx, s.svcCtx)

	return l.ModifyInterfaceRecord(in)
}

// 获取接口中用例最新一次执行记录
func (s *ReporterServer) GetCaseLatestRecord(ctx context.Context, in *pb.GetCaseLatestRecordRequest) (*pb.GetCaseLatestRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewGetCaseLatestRecordLogic(ctx, s.svcCtx)

	return l.GetCaseLatestRecord(in)
}

// 接口`调试`记录列表
func (s *ReporterServer) ListInterfaceRecord(ctx context.Context, in *pb.ListInterfaceRecordRequest) (*pb.ListInterfaceRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewListInterfaceRecordLogic(ctx, s.svcCtx)

	return l.ListInterfaceRecord(in)
}

// 接口执行记录详情
func (s *ReporterServer) GetInterfaceRecord(ctx context.Context, in *pb.GetInterfaceRecordRequest) (*pb.GetInterfaceRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewGetInterfaceRecordLogic(ctx, s.svcCtx)

	return l.GetInterfaceRecord(in)
}

// `集合`
func (s *ReporterServer) CreateSuiteRecord(ctx context.Context, in *pb.PutSuiteRecordRequest) (*pb.CreateSuiteRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewCreateSuiteRecordLogic(ctx, s.svcCtx)

	return l.CreateSuiteRecord(in)
}

// 修改集合记录
func (s *ReporterServer) ModifySuiteRecord(ctx context.Context, in *pb.PutSuiteRecordRequest) (*pb.ModifySuiteRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewModifySuiteRecordLogic(ctx, s.svcCtx)

	return l.ModifySuiteRecord(in)
}

// 集合`调试`记录列表
func (s *ReporterServer) ListSuiteRecord(ctx context.Context, in *pb.ListSuiteRecordRequest) (*pb.ListSuiteRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewListSuiteRecordLogic(ctx, s.svcCtx)

	return l.ListSuiteRecord(in)
}

// 集合执行记录详情
func (s *ReporterServer) GetSuiteRecord(ctx context.Context, in *pb.GetSuiteRecordRequest) (*pb.GetSuiteRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewGetSuiteRecordLogic(ctx, s.svcCtx)

	return l.GetSuiteRecord(in)
}

// `精准测试服务`
func (s *ReporterServer) CreateServiceRecord(ctx context.Context, in *pb.PutServiceRecordRequest) (*pb.CreateServiceRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewCreateServiceRecordLogic(ctx, s.svcCtx)

	return l.CreateServiceRecord(in)
}

// 修改精准测试服务记录
func (s *ReporterServer) ModifyServiceRecord(ctx context.Context, in *pb.PutServiceRecordRequest) (*pb.ModifyServiceRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewModifyServiceRecordLogic(ctx, s.svcCtx)

	return l.ModifyServiceRecord(in)
}

// 精准测试服务`调试`记录列表
func (s *ReporterServer) ListServiceRecord(ctx context.Context, in *pb.ListServiceRecordRequest) (*pb.ListServiceRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewListServiceRecordLogic(ctx, s.svcCtx)

	return l.ListServiceRecord(in)
}

// 精准测试服务执行记录详情
func (s *ReporterServer) GetServiceRecord(ctx context.Context, in *pb.GetServiceRecordRequest) (*pb.GetServiceRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewGetServiceRecordLogic(ctx, s.svcCtx)

	return l.GetServiceRecord(in)
}

// `计划`
func (s *ReporterServer) CreatePlanRecord(ctx context.Context, in *pb.PutPlanRecordRequest) (*pb.CreatePlanRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewCreatePlanRecordLogic(ctx, s.svcCtx)

	return l.CreatePlanRecord(in)
}

// 修改计划记录
func (s *ReporterServer) ModifyPlanRecord(ctx context.Context, in *pb.PutPlanRecordRequest) (*pb.ModifyPlanRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewModifyPlanRecordLogic(ctx, s.svcCtx)

	return l.ModifyPlanRecord(in)
}

// 计划执行记录列表
func (s *ReporterServer) ListPlanRecord(ctx context.Context, in *pb.ListPlanRecordRequest) (*pb.ListPlanRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewListPlanRecordLogic(ctx, s.svcCtx)

	return l.ListPlanRecord(in)
}

// 计划执行记录详情
func (s *ReporterServer) GetPlanRecord(ctx context.Context, in *pb.GetPlanRecordRequest) (*pb.GetPlanRecordResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewGetPlanRecordLogic(ctx, s.svcCtx)

	return l.GetPlanRecord(in)
}

// 计划执行详情查看其下各集合用例执行时间刻度信息
func (s *ReporterServer) GetPlanTimeScale(ctx context.Context, in *pb.GetPlanTimeScaleRequest) (*pb.GetPlanTimeScaleResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewGetPlanTimeScaleLogic(ctx, s.svcCtx)

	return l.GetPlanTimeScale(in)
}

// 获取测试计划执行报告（ci/cd专用）
func (s *ReporterServer) GetPlanSummary(ctx context.Context, in *pb.GetPlanSummaryRequest) (*pb.GetPlanSummaryResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewGetPlanSummaryLogic(ctx, s.svcCtx)

	return l.GetPlanSummary(in)
}

// 获取API计划关联用例信息
func (s *ReporterServer) GetPlanCasesInfo(ctx context.Context, in *pb.GetPlanCasesInfoRequest) (*pb.GetPlanCasesInfoResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewGetPlanCasesInfoLogic(ctx, s.svcCtx)

	return l.GetPlanCasesInfo(in)
}

// `用例`
func (s *ReporterServer) ListFailCaseForPlanRecord(ctx context.Context, in *pb.ListFailCaseRecordForPlanRequest) (*pb.ListFailCaseRecordForPlanResponse, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewListFailCaseForPlanRecordLogic(ctx, s.svcCtx)

	return l.ListFailCaseForPlanRecord(in)
}

func (s *ReporterServer) DelCaseFailStatForPlan(ctx context.Context, in *pb.DelCaseFailStatForPlanReq) (*pb.DelCaseFailStatForPlanResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewDelCaseFailStatForPlanLogic(ctx, s.svcCtx)

	return l.DelCaseFailStatForPlan(in)
}

// CountFailedCaseInLastNDays 统计最近N天指定用例的失败数
func (s *ReporterServer) CountFailedCaseInLastNDays(ctx context.Context, in *pb.CountFailedCaseInLastNDaysReq) (*pb.CountFailedCaseInLastNDaysResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := reporterlogic.NewCountFailedCaseInLastNDaysLogic(ctx, s.svcCtx)

	return l.CountFailedCaseInLastNDays(in)
}
