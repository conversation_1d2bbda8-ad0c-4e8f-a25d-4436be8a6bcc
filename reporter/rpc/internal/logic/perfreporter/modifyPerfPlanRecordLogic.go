package perfreporterlogic

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ModifyPerfPlanRecordLogic struct {
	*BaseLogic
}

func NewModifyPerfPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyPerfPlanRecordLogic {
	return &ModifyPerfPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyPerfPlanRecord 修改压测计划执行记录
func (l *ModifyPerfPlanRecordLogic) ModifyPerfPlanRecord(in *pb.ModifyPerfPlanRecordReq) (
	out *pb.ModifyPerfPlanRecordResp, err error,
) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()
	)

	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockPerfPlanRecordTaskIDExecuteIDProjectIDPrefix, taskID, executeID, projectID,
	)
	fn := func() error {
		origin, err := model.CheckPerfPlanExecutionRecordByExecuteID(
			l.ctx, l.svcCtx.PerfPlanExecutionRecordModel, taskID, executeID, projectID,
		)
		if err != nil {
			return err
		}

		var (
			status     = origin.Status
			costTime   = origin.CostTime
			endedAt    = origin.EndedAt
			apiMetrics = origin.ApiMetrics
			errMsg     = origin.ErrMsg

			now = time.Now()
		)

		if in.GetStatus() != "" {
			status.String = in.GetStatus()
			status.Valid = true
		}

		if in.GetEndedAt() != nil && in.GetEndedAt().IsValid() && in.GetEndedAt().GetSeconds() > 0 {
			if endTime := in.GetEndedAt().AsTime(); !endTime.IsZero() {
				costTime = endTime.Sub(origin.StartedAt.Time).Milliseconds()
				endedAt.Time = endTime
				endedAt.Valid = true
			}
		}

		if len(in.GetApiMetrics()) != 0 {
			s, err := protobuf.MarshalJSONWithMessagesToString(in.GetApiMetrics())
			if err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.SerializationError, err.Error()),
					"failed to unmarshal the api metrics of perf plan execution record, task_id: %s, execute_id: %s, project_id: %s, api_metrics: %+v, error: %+v",
					taskID, executeID, projectID, in.GetApiMetrics(), err,
				)
			}

			apiMetrics.String = s
			apiMetrics.Valid = s != ""
		}

		if in.GetErrMsg() != nil {
			s, err := protobuf.MarshalJSONToString(in.GetErrMsg())
			if err != nil {
				l.Errorf(
					"failed to unmarshal the error message of perf plan execution record, task_id: %s, execute_id: %s, project_id: %s, error_message: %+v, error: %+v",
					taskID, executeID, projectID, in.GetErrMsg(), err,
				)
			} else {
				errMsg.String = s
				errMsg.Valid = s != ""
			}
		}

		if in.GetErrMsg() == nil && status.String == dispatcherpb.ComponentState_Failure.String() {
			caseRecords, err := l.svcCtx.PerfCaseExecutionRecordModel.FindNoCacheByQuery(
				l.ctx,
				l.svcCtx.PerfCaseExecutionRecordModel.SelectBuilder().Where(
					"task_id = ? and project_id = ?", taskID, projectID,
				),
			)
			if err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to find perf case execution record by task_id: %s, project_id: %s, error: %+v",
					taskID, projectID, err,
				)
			}

			totalCases := len(caseRecords)
			successCases := 0
			failureCases := 0
			for _, caseRecord := range caseRecords {
				switch caseRecord.Status.String {
				case dispatcherpb.ComponentState_Success.String():
					successCases++
				case dispatcherpb.ComponentState_Failure.String():
					failureCases++
				}
			}

			e := &pb.ErrorMessage{
				MessageZh: fmt.Sprintf(
					"用例数: %d, 成功: %d, 失败: %d", totalCases, successCases, failureCases,
				),
			}
			s, err := protobuf.MarshalJSONToString(e)
			if err != nil {
				l.Errorf(
					"failed to unmarshal the error message of failure statistics, task_id: %s, execute_id: %s, project_id: %s, error_message: %+v, error: %+v",
					taskID, executeID, projectID, e, err,
				)
			} else {
				errMsg.String = s
				errMsg.Valid = s != ""
			}
		}

		record := &model.PerfPlanExecutionRecord{
			Id:             origin.Id,
			TaskId:         origin.TaskId,
			ExecuteId:      origin.ExecuteId,
			ProjectId:      origin.ProjectId,
			PlanId:         origin.PlanId,
			PlanName:       origin.PlanName,
			TriggerMode:    origin.TriggerMode,
			TargetMaxRps:   origin.TargetMaxRps,
			TargetDuration: origin.TargetDuration,
			Protocol:       origin.Protocol,
			TargetEnv:      origin.TargetEnv,
			Status:         status,
			TaskType:       origin.TaskType,
			ExecutionMode:  origin.ExecutionMode,
			Services:       origin.Services,
			CostTime:       costTime,
			MonitorUrls:    origin.MonitorUrls,
			ExecutedBy:     origin.ExecutedBy,
			StartedAt:      origin.StartedAt,
			EndedAt:        endedAt,
			ApiMetrics:     apiMetrics,
			ErrMsg:         errMsg,
			Cleaned:        origin.Cleaned,
			Deleted:        origin.Deleted,
			CreatedBy:      origin.CreatedBy,
			UpdatedBy:      l.currentUser.Account,
			CreatedAt:      origin.CreatedAt,
			UpdatedAt:      now,
		}

		return l.svcCtx.PerfPlanExecutionRecordModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				if _, err = l.svcCtx.PerfPlanExecutionRecordModel.Update(context, session, record); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to modify perf plan execution record, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s, error: %+v",
						in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(), in.GetPlanId(), err,
					)
				}

				return nil
			},
		)
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeout)); err != nil {
		return nil, err
	}

	return &pb.ModifyPerfPlanRecordResp{}, nil
}
