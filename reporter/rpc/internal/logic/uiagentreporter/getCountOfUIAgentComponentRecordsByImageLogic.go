package uiagentreporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetCountOfUIAgentComponentRecordsByImageLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetCountOfUIAgentComponentRecordsByImageLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *GetCountOfUIAgentComponentRecordsByImageLogic {
	return &GetCountOfUIAgentComponentRecordsByImageLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// GetCountOfUIAgentComponentRecordsByImage 获取涉及指定图片的`UI Agent`组件执行记录数量
func (l *GetCountOfUIAgentComponentRecordsByImageLogic) GetCountOfUIAgentComponentRecordsByImage(in *pb.GetCountOfUIAgentComponentRecordsByImageReq) (
	out *pb.GetCountOfUIAgentComponentRecordsByImageResp, err error,
) {
	var (
		projectID = in.GetProjectId()
		imageID   = in.GetImageId()
	)

	count, err := l.svcCtx.UiAgentComponentExecutionRecordModel.FindCountByImage(l.ctx, projectID, imageID)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to get count of ui agent component records by image, project_id: %s, image_id: %s, error: %+v",
			projectID, imageID, err,
		)
	}

	return &pb.GetCountOfUIAgentComponentRecordsByImageResp{
		Count: uint64(count),
	}, nil
}
