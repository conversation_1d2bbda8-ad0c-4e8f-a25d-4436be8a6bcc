package uiagentreporterlogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type SearchUIAgentComponentRecordLogic struct {
	*BaseLogic
}

func NewSearchUIAgentComponentRecordLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchUIAgentComponentRecordLogic {
	return &SearchUIAgentComponentRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchUIAgentComponentRecord 搜索`UI Agent`组件执行记录
func (l *SearchUIAgentComponentRecordLogic) SearchUIAgentComponentRecord(in *pb.SearchUIAgentComponentRecordReq) (
	out *pb.SearchUIAgentComponentRecordResp, err error,
) {
	var (
		projectID   = in.GetProjectId()
		componentID = in.GetComponentId()
		pagination  = in.GetPagination()
	)

	out = &pb.SearchUIAgentComponentRecordResp{}

	req := model.SearchUIAgentComponentExecutionRecordReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  projectID,
			Condition:  in.GetCondition(),
			Pagination: pagination,
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
		ComponentID: componentID,
	}
	count, err := l.svcCtx.UiAgentComponentExecutionRecordModel.FindCountByReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count ui agent component execution records, project_id: %s, component_id: %s, error: %+v",
			projectID, componentID, err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	records, err := l.svcCtx.UiAgentComponentExecutionRecordModel.FindAllByReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find ui agent component execution records, project_id: %s, component_id: %s, error: %+v",
			projectID, componentID, err,
		)
	}

	out.Items = make([]*pb.UIAgentComponentRecordItem, 0, len(records))
	for _, record := range records {
		item := &pb.UIAgentComponentRecordItem{}
		if err = utils.Copy(item, record, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy ui agent component execution record to response, record: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(record), err,
			)
		}

		// no need to return `application_config`, `steps`, `expectation`, `variables` and `device`
		// while searching ui agent component records
		item.ApplicationConfig = nil
		item.Steps = nil
		item.Expectation = nil
		item.Variables = nil
		item.Device = nil
		out.Items = append(out.Items, item)
	}

	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
