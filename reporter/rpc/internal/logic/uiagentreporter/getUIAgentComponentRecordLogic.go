package uiagentreporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetUIAgentComponentRecordLogic struct {
	*BaseLogic
}

func NewGetUIAgentComponentRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUIAgentComponentRecordLogic {
	return &GetUIAgentComponentRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetUIAgentComponentRecord 获取`UI Agent`组件执行记录
func (l *GetUIAgentComponentRecordLogic) GetUIAgentComponentRecord(in *pb.GetUIAgentComponentRecordReq) (out *pb.GetUIAgentComponentRecordResp, err error) {
	record, err := model.CheckUIAgentComponentExecutionRecordByExecuteID(
		l.ctx, l.svcCtx.UiAgentComponentExecutionRecordModel, in.GetTaskId(), in.GetExecuteId(), in.GetProjectId(),
	)
	if err != nil {
		return nil, err
	}

	out = &pb.GetUIAgentComponentRecordResp{Record: &pb.UIAgentComponentRecordItem{}}
	if err = utils.Copy(out.Record, record, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy ui agent component execution record to response, record: %s, error: %+v",
			jsonx.MarshalIgnoreError(record), err,
		)
	}

	return out, nil
}
