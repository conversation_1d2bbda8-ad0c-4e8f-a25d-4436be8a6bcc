package mock

import (
	"context"

	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"go.uber.org/zap"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	commonredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/zrpc"
)

func MockMysql() (string, cache.CacheConf) {
	// return "root:66243766@tcp(***************:3306)/test?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai", []cache.NodeConf{
	return "probe:Quwan@2020_TTinternation@tcp(************:3306)/artificial_intelligence?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai", []cache.NodeConf{
		{
			RedisConf: MockRedisConfV2(),
			Weight:    10,
		},
	}
}
func MockRedisConf() redis.RedisConf {
	return redis.RedisConf{
		Host: "*************:6379",
		Type: "node",
		Pass: "Quwan@2020",
		DB:   4,
	}
}
func MockRedisConfV2() redis.RedisConf {
	return redis.RedisConf{
		Host: "************:6379",
		Type: "node",
		DB:   19,
	}
}

func MockRedis() red.UniversalClient {
	return commonredis.NewClient(MockRedisConf())
}

func MockUserRpc() *zrpc.UserRPC {
	return &zrpc.UserRPC{
		Name: "rpc.user.prod",
	}
}

func MockLog() {
	logconf := logx.LogConf{
		ServiceName: "rpc.ai",
		Encoding:    "plain",
		Mode:        "console",
	}
	w := log.NewZapWriter(logconf, zap.AddCaller(), zap.Development())
	log.SetWriter(w)
}

func MockContext() context.Context {
	return context.Background()
}
