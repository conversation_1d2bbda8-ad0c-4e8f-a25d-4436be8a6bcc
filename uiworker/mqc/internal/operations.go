package internal

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	commonconsts "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	devicehubcommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/mqc/internal/tasks"
)

func InitOperation(svcCtx *svc.ServiceContext, needToLaunch bool) error {
	// register tasks
	if err := registerTasks(svcCtx); err != nil {
		return errors.Wrap(err, "failed to register tasks")
	}
	if err := registerScheduledTasks(svcCtx); err != nil {
		return errors.Wrap(err, "failed to register scheduled tasks")
	}

	if needToLaunch {
		// start consumer
		launchConsumer(svcCtx.UIWorkerConsumer)
	}
	launchConsumer(svcCtx.UIAndroidRealPhoneWorkerConsumer)
	launchConsumer(svcCtx.UIAndroidCloudPhoneWorkerConsumer)
	launchConsumer(svcCtx.UIIOSRealPhoneWorkerConsumer)
	launchConsumer(svcCtx.UIIOSCloudPhoneWorkerConsumer)

	monitorDynamicConsumers(svcCtx)

	listenIdleDevices(svcCtx)
	return nil
}

func registerTasks(svcCtx *svc.ServiceContext) error {
	err := svcCtx.UIWorkerConsumer.RegisterHandlers(
		consumer.NewTaskHandlerOjb(
			commonconsts.MQTaskTypeUIWorkerExecuteUiTest, tasks.NewProcessorSpiltByPlatform(svcCtx),
		),
		consumer.NewTaskHandlerOjb(
			commonconsts.MQTaskTypeUIWorkerGenerateReport, tasks.NewProcessorGenerateReport(svcCtx),
		),
	)
	if err != nil {
		return err
	}

	err = svcCtx.UIAndroidRealPhoneWorkerConsumer.RegisterHandlers(
		consumer.NewTaskHandlerOjb(
			commonconsts.MQTaskTypeUIWorkerExecuteAndroidUiTest, tasks.NewProcessorExecuteUiTest(svcCtx),
		),
	)
	if err != nil {
		return err
	}

	err = svcCtx.UIAndroidCloudPhoneWorkerConsumer.RegisterHandlers(
		consumer.NewTaskHandlerOjb(
			commonconsts.MQTaskTypeUIWorkerExecuteAndroidUiTest, tasks.NewProcessorExecuteUiTest(svcCtx),
		),
	)
	if err != nil {
		return err
	}

	err = svcCtx.UIIOSRealPhoneWorkerConsumer.RegisterHandlers(
		consumer.NewTaskHandlerOjb(
			commonconsts.MQTaskTypeUIWorkerExecuteIOSUiTest, tasks.NewProcessorExecuteUiTest(svcCtx),
		),
	)
	if err != nil {
		return err
	}

	err = svcCtx.UIIOSCloudPhoneWorkerConsumer.RegisterHandlers(
		consumer.NewTaskHandlerOjb(
			commonconsts.MQTaskTypeUIWorkerExecuteIOSUiTest, tasks.NewProcessorExecuteUiTest(svcCtx),
		),
	)
	if err != nil {
		return err
	}

	return nil
}

func registerScheduledTasks(svcCtx *svc.ServiceContext) error {
	if err := svcCtx.Scheduler.RegisterTasks(
		map[string]func(){
			svcCtx.Config.WaitingTaskCleaner.CronExpression: func() {
				if err := newCleaner(svcCtx).clean(svcCtx.Config.WaitingTaskCleaner.KeepDays); err != nil {
					logx.Errorf("failed to clear waiting tasks, error: %+v", err)
				}
			},
		},
	); err != nil {
		return err
	}

	svcCtx.Scheduler.Start()
	proc.AddShutdownListener(svcCtx.Scheduler.Stop)

	return nil
}

func launchConsumer(consumer *consumer.Consumer) {
	threading.GoSafe(consumer.Start)

	proc.AddShutdownListener(
		func() {
			logx.Info("stopping the consumer")
			consumer.Stop()
		},
	)
}

func listenIdleDevices(svcCtx *svc.ServiceContext) {
	threading.GoSafe(newListener(svcCtx).listen)
}

func monitorDynamicConsumers(svcCtx *svc.ServiceContext) {
	threading.GoSafe(svcCtx.ConsumerManager.Monitor)
}

// Deprecated: use `Listener.listen` instead.
func launchDeviceListener(svcCtx *svc.ServiceContext) {
	ctx, cancel := context.WithCancel(context.Background())
	proc.AddShutdownListener(
		func() {
			logx.Infof("stopping the devices listener")
			cancel()
		},
	)

	threading.GoSafe(
		func() {
			logx.Infof("starting the devices listener")
			devicesListener(ctx, svcCtx)
		},
	)
}

func devicesListener(ctx context.Context, svcCtx *svc.ServiceContext) {
	ps := svcCtx.RedisNode.Subscribe(ctx, devicehubcommon.ConstChannelNameOfIdleDevicesNotify)
	defer func(ps *redis.PubSub) {
		err := ps.Close()
		if err != nil {
			logx.Errorf("failed to close PubSub, error: %+v", err)
		}
	}(ps)

	ticker := timewheel.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		var info *pb.IdleDevicesNotifyInfo

		select {
		case msg, ok := <-ps.Channel():
			logx.Infof("got a message from the channel of PubSub, message: %s, ok: %t", msg.String(), ok)
			if !ok {
				logx.Error("the channel of PubSub has been closed")
				return
			}

			info = &pb.IdleDevicesNotifyInfo{}
			err := protobuf.UnmarshalJSONFromString(msg.Payload, info)
			if err != nil {
				logx.Errorf("failed to unmarshal the message: %s", msg.String())
				continue
			}
			logx.Debugf("success to unmarshal the message: %s", msg.String())
		case <-ticker.C:
			resp, err := svcCtx.DeviceHubRPC.SearchDevices(
				ctx, &pb.SearchDeviceReq{
					Condition: &rpc.Condition{
						Single: &rpc.SingleCondition{
							Field:   "state",
							Compare: constants.EQ,
							Other: &rpc.Other{
								Value: pb.DeviceState_IDLE.String(),
							},
						},
					},
				},
			)
			if err != nil {
				logx.Errorf("failed to search devices, error: %+v", err)
				continue
			}
			logx.Debugf("search devices successfully, total: %d", resp.GetTotalCount())

			info = &pb.IdleDevicesNotifyInfo{}
			for _, device := range resp.GetItems() {
				if device.GetType() == commonpb.DeviceType_REAL_PHONE && device.GetPlatform() == commonpb.PlatformType_ANDROID {
					info.RealPhonesOfAndroid += 1
				} else if device.GetType() == commonpb.DeviceType_REAL_PHONE && device.GetPlatform() == commonpb.PlatformType_IOS {
					info.RealPhonesOfIos += 1
				} else if device.GetType() == commonpb.DeviceType_CLOUD_PHONE && device.GetPlatform() == commonpb.PlatformType_ANDROID {
					info.CloudPhonesOfAndroid += 1
				} else if device.GetType() == commonpb.DeviceType_CLOUD_PHONE && device.GetPlatform() == commonpb.PlatformType_IOS {
					info.CloudPhonesOfIos += 1
				}
			}
		case <-ctx.Done():
			logx.Debugf("got a signal to break the loop, error: %+v", ctx.Err())
			return
		}

		notifyToSubscriber(ctx, svcCtx, info)
	}
}

func notifyToSubscriber(ctx context.Context, svcCtx *svc.ServiceContext, info *pb.IdleDevicesNotifyInfo) {
	if info == nil {
		return
	}

	_ = mr.MapReduceVoid[condition, *model.WaitingTasks](
		func(source chan<- condition) {
			for _, c := range []condition{
				{
					deviceType:   commonpb.DeviceType_REAL_PHONE,
					platformType: commonpb.PlatformType_ANDROID,
					count:        info.RealPhonesOfAndroid,
				},
				{
					deviceType:   commonpb.DeviceType_CLOUD_PHONE,
					platformType: commonpb.PlatformType_ANDROID,
					count:        info.CloudPhonesOfAndroid,
				},
				{
					deviceType:   commonpb.DeviceType_REAL_PHONE,
					platformType: commonpb.PlatformType_IOS,
					count:        info.RealPhonesOfIos,
				},
				{
					deviceType:   commonpb.DeviceType_CLOUD_PHONE,
					platformType: commonpb.PlatformType_IOS,
					count:        info.CloudPhonesOfIos,
				},
			} {
				if c.count > 0 {
					source <- c
				}
			}
		}, func(item condition, writer mr.Writer[*model.WaitingTasks], cancel func(error)) {
			_tasks, err := svcCtx.WaitingTasksModel.FindTasksByPlatformDeviceType(
				ctx, int64(item.platformType.Number()), int64(item.deviceType.Number()), int64(item.count),
			)
			if err != nil {
				logx.Errorf(
					"failed to find waiting task, type: %s, platform: %s, err: %+v",
					item.deviceType.String(), item.platformType.String(), err,
				)
				return
			}

			for _, task := range _tasks {
				writer.Write(task)
			}
		}, func(pipe <-chan *model.WaitingTasks, cancel func(error)) {
			for t := range pipe {
				publishTokenToChannel(ctx, svcCtx, t.Token)
			}
		},
	)
}

func publishTokenToChannel(ctx context.Context, svcCtx *svc.ServiceContext, token string) {
	if val, err := svcCtx.RedisNode.Publish(
		ctx, common.ConstChannelNameOfIdleDevicesNotifyByToken, token,
	).Result(); err != nil {
		logx.Errorf(
			"failed to send the info of idle devices notify by token to the channel[%s], info: %s, error: %+v",
			common.ConstChannelNameOfIdleDevicesNotifyByToken, token, err,
		)
	} else {
		logx.Infof(
			"succeeded to send the info of idle devices notify to token to the channel[%s], info: %s, subscribers: %d",
			common.ConstChannelNameOfIdleDevicesNotifyByToken, token, val,
		)
	}
}

// Deprecated: use `Cleaner.clean` instead.
func clearWaitingTasks(svcCtx *svc.ServiceContext, days int) error {
	var (
		ctx    context.Context
		cancel context.CancelFunc
	)

	ctx, cancel = context.WithTimeout(context.Background(), common.ConstExpireOfClearWaitingTasksTask)
	defer cancel()

	logger := logx.WithContext(ctx)

	fn := func() error {
		return svcCtx.WaitingTasksModel.DeleteBeforeNDaysRecords(ctx, nil, days)
	}
	if err := caller.LockWithOptionDo(
		svcCtx.Redis, common.ConstLockClearWaitingTasks, fn,
		redislock.WithExpire(common.ConstExpireOfClearWaitingTasksTask),
	); err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			logger.Errorf("failed to clear waiting tasks, key: %s, error: %+v", common.ConstLockClearWaitingTasks, err)
			return err
		}
	} else {
		logger.Infof("finished to clear waiting tasks, key: %s, days: %d", common.ConstLockClearWaitingTasks, days)
	}

	return nil
}
