package zrpc

import (
	"context"
	"flag"

	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/client/userservice"
)

type UserRPC struct {
	conf zrpc.RpcClientConf

	Name string
	Cli  userservice.UserService
}

func NewUserRPC(conf zrpc.RpcClientConf) *UserRPC {
	return &UserRPC{
		conf: conf,
		Name: conf.Etcd.Key,
		Cli:  userservice.NewUserService(zrpc.MustNewClient(conf, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (cli *UserRPC) GetUser(ctx context.Context, req *userservice.GetUserReq) (*userservice.GetUserResp, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_userRpc_GetUser(req)
	}

	return cli.Cli.GetUser(ctx, req)
}

func (cli *UserRPC) UserList(ctx context.Context, req *userservice.UserListReq) (*userservice.UserListResp, error) {
	return cli.Cli.UserList(ctx, req)
}

func (cli *UserRPC) GetUserLeader(
	ctx context.Context, req *userservice.GetUserLeaderReq,
) (*userservice.GetUserLeaderResp, error) {
	return cli.Cli.GetUserLeader(ctx, req)
}
