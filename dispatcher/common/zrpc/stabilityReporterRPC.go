package zrpc

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/stabilityreporter"
)

type StabilityReporterRPC struct {
	conf zrpc.RpcClientConf

	Name string
	Cli  stabilityreporter.StabilityReporter
}

func NewStabilityReporterRPC(conf zrpc.RpcClientConf) *StabilityReporterRPC {
	return &StabilityReporterRPC{
		conf: conf,
		Name: conf.Etcd.Key,
		Cli: stabilityreporter.NewStabilityReporter(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}

func (cli *StabilityReporterRPC) CreateStabilityPlanRecord(
	ctx context.Context, req *stabilityreporter.PutStabilityPlanRecordReq,
) (*stabilityreporter.CreateStabilityPlanRecordResp, error) {
	return cli.Cli.CreateStabilityPlanRecord(ctx, req)
}

func (cli *StabilityReporterRPC) ModifyStabilityPlanRecord(
	ctx context.Context, req *stabilityreporter.PutStabilityPlanRecordReq,
) (*stabilityreporter.ModifyStabilityPlanRecordResp, error) {
	return cli.Cli.ModifyStabilityPlanRecord(ctx, req)
}

func (cli *StabilityReporterRPC) SearchStabilityPlanRecord(
	ctx context.Context, req *stabilityreporter.SearchStabilityPlanRecordReq,
) (*stabilityreporter.SearchStabilityPlanRecordResp, error) {
	return cli.Cli.SearchStabilityPlanRecord(ctx, req)
}

func (cli *StabilityReporterRPC) GetStabilityPlanRecord(
	ctx context.Context, req *stabilityreporter.GetStabilityPlanRecordReq,
) (*stabilityreporter.GetStabilityPlanRecordResp, error) {
	return cli.Cli.GetStabilityPlanRecord(ctx, req)
}

func (cli *StabilityReporterRPC) SearchStabilityDeviceRecord(
	ctx context.Context, req *stabilityreporter.SearchStabilityDeviceRecordReq,
) (*stabilityreporter.SearchStabilityDeviceRecordResp, error) {
	return cli.Cli.SearchStabilityDeviceRecord(ctx, req)
}
