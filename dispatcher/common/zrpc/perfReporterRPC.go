package zrpc

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/perfreporter"
)

type PerfReporterRPC struct {
	conf zrpc.RpcClientConf

	Name string
	Cli  perfreporter.PerfReporter
}

func NewPerfReporterRPC(conf zrpc.RpcClientConf) *PerfReporterRPC {
	return &PerfReporterRPC{
		conf: conf,
		Name: conf.Etcd.Key,
		Cli:  perfreporter.NewPerfReporter(zrpc.MustNewClient(conf, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (cli *PerfReporterRPC) CreatePerfCaseRecord(
	ctx context.Context, req *perfreporter.CreatePerfCaseRecordReq,
) (*perfreporter.CreatePerfCaseRecordResp, error) {
	return cli.Cli.CreatePerfCaseRecord(ctx, req)
}

func (cli *PerfReporterRPC) ModifyPerfCaseRecord(
	ctx context.Context, req *perfreporter.ModifyPerfCaseRecordReq,
) (*perfreporter.ModifyPerfCaseRecordResp, error) {
	return cli.Cli.ModifyPerfCaseRecord(ctx, req)
}

func (cli *PerfReporterRPC) GetPerfCaseRecord(
	ctx context.Context, req *perfreporter.GetPerfCaseRecordReq,
) (*perfreporter.GetPerfCaseRecordResp, error) {
	return cli.Cli.GetPerfCaseRecord(ctx, req)
}

func (cli *PerfReporterRPC) CreatePerfSuiteRecord(
	ctx context.Context, req *perfreporter.CreatePerfSuiteRecordReq,
) (*perfreporter.CreatePerfSuiteRecordResp, error) {
	return cli.Cli.CreatePerfSuiteRecord(ctx, req)
}

func (cli *PerfReporterRPC) ModifyPerfSuiteRecord(
	ctx context.Context, req *perfreporter.ModifyPerfSuiteRecordReq,
) (*perfreporter.ModifyPerfSuiteRecordResp, error) {
	return cli.Cli.ModifyPerfSuiteRecord(ctx, req)
}

func (cli *PerfReporterRPC) GetPerfSuiteRecord(
	ctx context.Context, req *perfreporter.GetPerfSuiteRecordReq,
) (*perfreporter.GetPerfSuiteRecordResp, error) {
	return cli.Cli.GetPerfSuiteRecord(ctx, req)
}

func (cli *PerfReporterRPC) CreatePerfPlanRecord(
	ctx context.Context, req *perfreporter.CreatePerfPlanRecordReq,
) (*perfreporter.CreatePerfPlanRecordResp, error) {
	return cli.Cli.CreatePerfPlanRecord(ctx, req)
}

func (cli *PerfReporterRPC) ModifyPerfPlanRecord(
	ctx context.Context, req *perfreporter.ModifyPerfPlanRecordReq,
) (*perfreporter.ModifyPerfPlanRecordResp, error) {
	return cli.Cli.ModifyPerfPlanRecord(ctx, req)
}

func (cli *PerfReporterRPC) SearchPerfPlanRecord(
	ctx context.Context, req *perfreporter.SearchPerfPlanRecordReq,
) (*perfreporter.SearchPerfPlanRecordResp, error) {
	return cli.Cli.SearchPerfPlanRecord(ctx, req)
}

func (cli *PerfReporterRPC) GetPerfPlanRecord(
	ctx context.Context, req *perfreporter.GetPerfPlanRecordReq,
) (*perfreporter.GetPerfPlanRecordResp, error) {
	return cli.Cli.GetPerfPlanRecord(ctx, req)
}

func (cli *PerfReporterRPC) SearchPerfCaseRecord(
	ctx context.Context, req *perfreporter.SearchPerfCaseRecordReq,
) (*perfreporter.SearchPerfCaseRecordResp, error) {
	return cli.Cli.SearchPerfCaseRecord(ctx, req)
}
