package zrpc

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/client/larkcontactuserservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/client/larkimchatmanagersservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/client/larkimchatmembersservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/client/larkimchatsservice"
	larkproxypb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/pb"
)

type LarkProxyRpc struct {
	conf zrpc.RpcClientConf

	Name                      string
	larkContactUserService    larkcontactuserservice.LarkContactUserService
	larkIMChatMembersService  larkimchatmembersservice.LarkIMChatMembersService
	larkIMChatsService        larkimchatsservice.LarkIMChatsService
	larkIMChatManagersService larkimchatmanagersservice.LarkIMChatManagersService
}

func NewLarkProxyRpc(conf zrpc.RpcClientConf) *LarkProxyRpc {
	return &LarkProxyRpc{
		conf: conf,
		Name: conf.Etcd.Key,
		larkContactUserService: larkcontactuserservice.NewLarkContactUserService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		larkIMChatMembersService: larkimchatmembersservice.NewLarkIMChatMembersService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		larkIMChatsService: larkimchatsservice.NewLarkIMChatsService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		larkIMChatManagersService: larkimchatmanagersservice.NewLarkIMChatManagersService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}

func (c *LarkProxyRpc) GetBatchUserID(
	ctx context.Context, req *larkproxypb.GetBatchUserIDReq, opts ...grpc.CallOption,
) (*larkproxypb.GetBatchUserIDResp, error) {
	return c.larkContactUserService.GetBatchUserID(ctx, req, opts...)
}

func (c *LarkProxyRpc) CreateChat(
	ctx context.Context, req *larkproxypb.CreateChatReq, opts ...grpc.CallOption,
) (*larkproxypb.CreateChatResp, error) {
	return c.larkIMChatsService.CreateChat(ctx, req, opts...)
}

func (c *LarkProxyRpc) CreateChatMembers(
	ctx context.Context, req *larkproxypb.CreateChatMembersReq, opts ...grpc.CallOption,
) (*larkproxypb.CreateChatMembersResp, error) {
	return c.larkIMChatMembersService.CreateChatMembers(ctx, req, opts...)
}

func (c *LarkProxyRpc) CreateChatManagers(
	ctx context.Context, req *larkproxypb.CreateChatManagersReq, opts ...grpc.CallOption,
) (*larkproxypb.CreateChatManagersResp, error) {
	return c.larkIMChatManagersService.CreateChatManagers(ctx, req, opts...)
}
