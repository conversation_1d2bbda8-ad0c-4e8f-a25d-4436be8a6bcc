package zrpc

import (
	"context"
	"flag"

	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/uireporter"
)

type UIReporterRPC struct {
	conf zrpc.RpcClientConf

	Name string
	Cli  uireporter.UIReporter
}

func NewUIReporterRPC(conf zrpc.RpcClientConf) *UIReporterRPC {
	return &UIReporterRPC{
		conf: conf,
		Name: conf.Etcd.Key,
		Cli:  uireporter.NewUIReporter(zrpc.MustNewClient(conf, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (cli *UIReporterRPC) CreateUIPlanRecord(
	ctx context.Context, req *uireporter.PutUIPlanRecordRequest,
) (*uireporter.CreateUIPlanRecordResponse, error) {
	return cli.Cli.CreateUIPlanRecord(ctx, req)
}

func (cli *UIReporterRPC) ModifyUIPlanRecord(
	ctx context.Context, req *uireporter.PutUIPlanRecordRequest,
) (*uireporter.ModifyUIPlanRecordResponse, error) {
	return cli.Cli.ModifyUIPlanRecord(ctx, req)
}

func (cli *UIReporterRPC) CreateUISuiteRecord(
	ctx context.Context, req *uireporter.PutUISuiteRecordRequest,
) (*uireporter.CreateUISuiteRecordResponse, error) {
	return cli.Cli.CreateUISuiteRecord(ctx, req)
}

func (cli *UIReporterRPC) ModifyUISuiteRecord(
	ctx context.Context, req *uireporter.PutUISuiteRecordRequest,
) (*uireporter.ModifyUISuiteRecordResponse, error) {
	return cli.Cli.ModifyUISuiteRecord(ctx, req)
}

func (cli *UIReporterRPC) ViewUIPlanRecord(
	ctx context.Context, req *uireporter.ViewUIPlanRecordRequest,
) (*uireporter.ViewUIPlanRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_GetUiPlanRecord(req)
	}
	return cli.Cli.ViewUIPlanRecord(ctx, req)
}

func (cli *UIReporterRPC) GetUIPlanCasesInfo(
	ctx context.Context, req *uireporter.GetUIPlanCasesInfoRequest,
) (*uireporter.GetUIPlanCasesInfoResponse, error) {
	return cli.Cli.GetUIPlanCasesInfo(ctx, req)
}

func (cli *UIReporterRPC) CreateUICaseRecord(
	ctx context.Context, req *uireporter.PutUICaseRecordRequest,
) (*uireporter.CreateUICaseRecordResponse, error) {
	return cli.Cli.CreateUICaseRecord(ctx, req)
}

func (cli *UIReporterRPC) ModifyUICaseRecord(
	ctx context.Context, req *uireporter.PutUICaseRecordRequest,
) (*uireporter.ModifyUICaseRecordResponse, error) {
	return cli.Cli.ModifyUICaseRecord(ctx, req)
}
