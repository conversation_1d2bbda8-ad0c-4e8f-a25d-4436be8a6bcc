package zrpc

import (
	"context"
	"flag"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/apiexecutionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/apiplanservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/apisuiteservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/applicationconfigurationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/interfacedefinitionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/notifyservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/perflarkmemberservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/perfplanv2service"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/projectdeviceservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/slanotifierservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/slathresholdservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/uiplanservice"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ManagerRPC struct {
	conf zrpc.RpcClientConf

	Name                            string
	ApiExecutionService             apiexecutionservice.ApiExecutionService
	ApiPlanService                  apiplanservice.ApiPlanService
	ApiSuiteService                 apisuiteservice.ApiSuiteService
	InterfaceDefinitionService      interfacedefinitionservice.InterfaceDefinitionService
	NotifyService                   notifyservice.NotifyService
	PerfPlanService                 perfplanv2service.PerfPlanV2Service
	UiPlanService                   uiplanservice.UiPlanService
	PerfLarkMemberService           perflarkmemberservice.PerfLarkMemberService
	SlaNotifierService              slanotifierservice.SlaNotifierService
	SlaThresholdService             slathresholdservice.SlaThresholdService
	ProjectDeviceService            projectdeviceservice.ProjectDeviceService
	ApplicationConfigurationService applicationconfigurationservice.ApplicationConfigurationService
}

func NewManagerRPC(conf zrpc.RpcClientConf) *ManagerRPC {
	return &ManagerRPC{
		conf: conf,
		Name: conf.Etcd.Key,
		ApiExecutionService: apiexecutionservice.NewApiExecutionService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ApiPlanService: apiplanservice.NewApiPlanService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ApiSuiteService: apisuiteservice.NewApiSuiteService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		InterfaceDefinitionService: interfacedefinitionservice.NewInterfaceDefinitionService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		NotifyService: notifyservice.NewNotifyService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		PerfPlanService: perfplanv2service.NewPerfPlanV2Service(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		UiPlanService: uiplanservice.NewUiPlanService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		PerfLarkMemberService: perflarkmemberservice.NewPerfLarkMemberService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		SlaNotifierService: slanotifierservice.NewSlaNotifierService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		SlaThresholdService: slathresholdservice.NewSlaThresholdService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ProjectDeviceService: projectdeviceservice.NewProjectDeviceService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ApplicationConfigurationService: applicationconfigurationservice.NewApplicationConfigurationService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}

func (cli *ManagerRPC) GetApiExecutionData(ctx context.Context, req *managerpb.GetApiExecutionDataReq) (
	*managerpb.ApiExecutionData, error,
) {
	if flag.Lookup("test.v") != nil {
		return mock_default_managerRpc_GetApiExecutionData(req)
	}

	return cli.ApiExecutionService.GetApiExecutionData(ctx, req)
}

func (cli *ManagerRPC) GetApiExecutionDataStructure(
	ctx context.Context, req *managerpb.GetApiExecutionDataStructureReq,
) (*managerpb.ApiExecutionData, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_managerRpc_GetApiExecutionDataStructure(req)
	}

	return cli.ApiExecutionService.GetApiExecutionDataStructure(ctx, req)
}

func (cli *ManagerRPC) GetPlanNotify(
	ctx context.Context, req *managerpb.GetPlanNotifyReq,
) (*managerpb.GetPlanNotifyResp, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_managerRpc_GetPlanNotify(req)
	}
	return cli.NotifyService.GetPlanNotify(ctx, req)
}

func (cli *ManagerRPC) ViewApiPlan(ctx context.Context, req *managerpb.ViewApiPlanReq) (
	*managerpb.ViewApiPlanResp, error,
) {
	return cli.ApiPlanService.ViewApiPlan(ctx, req)
}

func (cli *ManagerRPC) ViewApiSuite(ctx context.Context, req *managerpb.ViewApiSuiteReq) (
	*managerpb.ViewApiSuiteResp, error,
) {
	return cli.ApiSuiteService.ViewApiSuite(ctx, req)
}

func (cli *ManagerRPC) ViewInterfaceDocument(
	ctx context.Context, req *managerpb.ViewInterfaceDocumentReq,
) (*managerpb.ViewInterfaceDocumentResp, error) {
	return cli.InterfaceDefinitionService.ViewInterfaceDocument(ctx, req)
}

func (cli *ManagerRPC) ViewUiPlan(ctx context.Context, req *managerpb.ViewUiPlanReq) (
	*managerpb.ViewUiPlanResp, error,
) {
	return cli.UiPlanService.ViewUiPlan(ctx, req)
}

func (cli *ManagerRPC) ViewPerfPlan(
	ctx context.Context, req *managerpb.ViewPerfPlanV2Req, opts ...grpc.CallOption,
) (*managerpb.ViewPerfPlanV2Resp, error) {
	return cli.PerfPlanService.ViewPerfPlanV2(ctx, req, opts...)
}

func (cli *ManagerRPC) UpdatePerfPlanByChatID(
	ctx context.Context, req *managerpb.UpdatePerfPlanByChatIDReq, opts ...grpc.CallOption,
) (*managerpb.UpdatePerfPlanByChatIDResp, error) {
	return cli.PerfPlanService.UpdatePerfPlanByChatID(ctx, req, opts...)
}

func (cli *ManagerRPC) SearchPerfLarkMember(
	ctx context.Context, req *managerpb.SearchPerfLarkMemberReq, opts ...grpc.CallOption,
) (*managerpb.SearchPerfLarkMemberResp, error) {
	return cli.PerfLarkMemberService.SearchPerfLarkMember(ctx, req, opts...)
}

func (cli *ManagerRPC) SearchSlaNotifier(
	ctx context.Context, req *managerpb.SearchSlaNotifierReq, opts ...grpc.CallOption,
) (*managerpb.SearchSlaNotifierResp, error) {
	return cli.SlaNotifierService.SearchSlaNotifier(ctx, req, opts...)
}

func (cli *ManagerRPC) GetSlaThreshold(
	ctx context.Context, req *managerpb.GetSlaThresholdReq, opts ...grpc.CallOption,
) (*managerpb.GetSlaThresholdResp, error) {
	return cli.SlaThresholdService.GetSlaThreshold(ctx, req, opts...)
}

func (cli *ManagerRPC) GetProjectDevice(
	ctx context.Context, in *managerpb.GetProjectDeviceReq, opts ...grpc.CallOption,
) (*managerpb.GetProjectDeviceResp, error) {
	return cli.ProjectDeviceService.GetProjectDevice(ctx, in, opts...)
}

func (cli *ManagerRPC) ViewApplicationConfiguration(
	ctx context.Context, req *managerpb.ViewApplicationConfigurationReq, opts ...grpc.CallOption,
) (*managerpb.ViewApplicationConfigurationResp, error) {
	return cli.ApplicationConfigurationService.ViewApplicationConfiguration(ctx, req, opts...)
}
