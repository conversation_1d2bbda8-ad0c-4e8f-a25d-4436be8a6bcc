package zrpc

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
)

func mock_default_reporterRpc_Create(req *reporter.PutRecordRequest) (*reporter.CreateRecordResponse, error) {
	resp := &reporter.CreateRecordResponse{}
	resp.ComponentExecuteId = req.GetComponentExecuteId()
	if resp.GetComponentExecuteId() == "" {
		resp.ComponentExecuteId = utils.GenExecuteId()
	}

	return resp, nil
}

func mock_default_reporterRpc_Modify(req *reporter.PutRecordRequest) (*reporter.ModifyRecordResponse, error) {
	return &reporter.ModifyRecordResponse{}, nil
}

func mock_default_reporterRpc_Get(req *reporter.GetExecuteRecordRequest) (*reporter.GetExecuteRecordResponse, error) {
	typ := managerpb.ApiExecutionDataType(managerpb.ApiExecutionDataType_value[req.GetComponentType()])
	var parent *pb.ComponentExecuteKey
	switch typ {
	case managerpb.ApiExecutionDataType_API_CASE:
		parent = &pb.ComponentExecuteKey{
			ComponentExecuteId: utils.GenExecuteId(),
			Key: &pb.ComponentKey{
				ComponentId:   utils.GenSuiteId(),
				ComponentType: managerpb.ApiExecutionDataType_API_SUITE,
			},
		}
	case managerpb.ApiExecutionDataType_API_SUITE:
		parent = &pb.ComponentExecuteKey{
			ComponentExecuteId: utils.GenExecuteId(),
			Key: &pb.ComponentKey{
				ComponentId:   utils.GenPlanId(),
				ComponentType: managerpb.ApiExecutionDataType_API_PLAN,
			},
		}
	case managerpb.ApiExecutionDataType_API_PLAN:
		parent = &pb.ComponentExecuteKey{
			ComponentExecuteId: "",
			Key: &pb.ComponentKey{
				ComponentId: "",
			},
		}
	case managerpb.ApiExecutionDataType_INTERFACE_CASE:
		parent = &pb.ComponentExecuteKey{
			ComponentExecuteId: "",
			Key: &pb.ComponentKey{
				ComponentId: "",
			},
		}
	}

	return &reporter.GetExecuteRecordResponse{
		TaskId:                   req.GetTaskId(),
		ExecuteId:                req.GetExecuteId(),
		ProjectId:                req.GetProjectId(),
		ExecuteType:              req.GetExecuteType(),
		ComponentId:              req.GetComponentId(),
		ComponentName:            "test-component-name",
		ComponentType:            req.GetComponentType(),
		ComponentExecuteId:       req.GetComponentExecuteId(),
		ParentComponentId:        parent.GetKey().GetComponentId(),
		ParentComponentExecuteId: parent.GetComponentExecuteId(),
		Version:                  utils.GenVersion(),
		Status:                   pb.ComponentState_Success.String(),
		IsRoot:                   1,
		ExecutedBy:               "unit-user-id",
	}, nil
}

func mock_default_reporterRpc_GetParent(req *reporter.GetParentRecordRequest) (
	*reporter.GetParentRecordResponse, error,
) {
	typ := managerpb.ApiExecutionDataType(managerpb.ApiExecutionDataType_value[req.GetComponentType()])
	var parent *pb.ComponentExecuteKey
	switch typ {
	case managerpb.ApiExecutionDataType_API_CASE:
		parent = &pb.ComponentExecuteKey{
			ComponentExecuteId: utils.GenExecuteId(),
			Key: &pb.ComponentKey{
				ComponentId:   utils.GenSuiteId(),
				ComponentType: managerpb.ApiExecutionDataType_API_SUITE,
			},
		}
	case managerpb.ApiExecutionDataType_API_SUITE:
		parent = &pb.ComponentExecuteKey{
			ComponentExecuteId: utils.GenExecuteId(),
			Key: &pb.ComponentKey{
				ComponentId:   utils.GenPlanId(),
				ComponentType: managerpb.ApiExecutionDataType_API_PLAN,
			},
		}
	case managerpb.ApiExecutionDataType_API_PLAN:
		parent = &pb.ComponentExecuteKey{
			ComponentExecuteId: "",
			Key: &pb.ComponentKey{
				ComponentId: "",
			},
		}
	case managerpb.ApiExecutionDataType_INTERFACE_CASE:
		parent = &pb.ComponentExecuteKey{
			ComponentExecuteId: "",
			Key: &pb.ComponentKey{
				ComponentId: "",
			},
		}
	}

	return &reporter.GetParentRecordResponse{
		TaskId:             req.GetTaskId(),
		ExecuteId:          req.GetExecuteId(),
		ProjectId:          req.GetProjectId(),
		ExecuteType:        req.GetExecuteType(),
		ComponentId:        parent.GetKey().GetComponentId(),
		ComponentName:      "test-component-name",
		ComponentType:      parent.GetKey().GetComponentType().String(),
		ComponentExecuteId: parent.GetComponentExecuteId(),
		Status:             pb.ComponentState_Success.String(),
		ExecutedBy:         "unit-user-id",
	}, nil
}

func mock_default_reporterRpc_GetChild(req *reporter.GetChildrenRecordRequest) (
	*reporter.GetChildrenRecordResponse, error,
) {
	typ := managerpb.ApiExecutionDataType(managerpb.ApiExecutionDataType_value[req.GetComponentType()])
	var child *pb.ComponentExecuteKey
	switch typ {
	case managerpb.ApiExecutionDataType_API_SUITE:
		child = &pb.ComponentExecuteKey{
			ComponentExecuteId: utils.GenExecuteId(),
			Key: &pb.ComponentKey{
				ComponentId:   utils.GenCaseId(),
				ComponentType: managerpb.ApiExecutionDataType_API_CASE,
				Version:       utils.GenVersion(),
			},
		}
	case managerpb.ApiExecutionDataType_API_PLAN:
		child = &pb.ComponentExecuteKey{
			ComponentExecuteId: utils.GenExecuteId(),
			Key: &pb.ComponentKey{
				ComponentId:   utils.GenSuiteId(),
				ComponentType: managerpb.ApiExecutionDataType_API_SUITE,
				Version:       utils.GenVersion(),
			},
		}
	case managerpb.ApiExecutionDataType_INTERFACE_CASE:
		child = &pb.ComponentExecuteKey{
			ComponentExecuteId: utils.GenExecuteId(),
			Key: &pb.ComponentKey{
				ComponentId:   utils.GenInterfaceCaseId(),
				ComponentType: managerpb.ApiExecutionDataType_INTERFACE_CASE,
				Version:       utils.GenVersion(),
			},
		}
	}

	return &reporter.GetChildrenRecordResponse{
		ChildRecordArray: []*reporter.GetChildrenRecordResponse_ChildRecord{
			{
				TaskId:                   req.GetTaskId(),
				ExecuteId:                req.GetExecuteId(),
				ProjectId:                req.GetProjectId(),
				ExecuteType:              req.GetExecuteType(),
				ComponentId:              child.GetKey().GetComponentId(),
				ComponentName:            "test-component-name",
				ComponentType:            child.GetKey().GetComponentType().String(),
				ComponentExecuteId:       child.GetComponentExecuteId(),
				ParentComponentId:        req.GetComponentId(),
				ParentComponentExecuteId: req.GetComponentExecuteId(),
				Version:                  utils.GenVersion(),
				Status:                   pb.ComponentState_Success.String(),
				IsRoot:                   1,
				ExecutedBy:               "unit-user-id",
			},
		},
	}, nil
}

func mockDefaultReporterRPCCreateCaseRecord(req *reporter.PutRecordRequest) (*reporter.CreateRecordResponse, error) {
	resp := &reporter.CreateRecordResponse{}
	resp.ComponentExecuteId = utils.GenExecuteId()
	if req.GetComponentExecuteId() != "" {
		resp.ComponentExecuteId = req.GetComponentExecuteId()
	}

	return resp, nil
}

func mockDefaultReporterRPCModifyCaseRecord(req *reporter.PutRecordRequest) (*reporter.ModifyRecordResponse, error) {
	resp := &reporter.ModifyRecordResponse{}
	return resp, nil
}

func mock_default_reporterRpc_CreateInterfaceRecord(req *reporter.PutInterfaceRecordRequest) (
	*reporter.CreateInterfaceRecordResponse, error,
) {
	resp := &reporter.CreateInterfaceRecordResponse{}
	resp.InterfaceExecuteId = req.GetInterfaceExecuteId()
	if resp.GetInterfaceExecuteId() == "" {
		resp.InterfaceExecuteId = utils.GenExecuteId()
	}
	return resp, nil
}

func mock_default_reporterRpc_ModifyInterfaceRecord(req *reporter.PutInterfaceRecordRequest) (
	*reporter.ModifyInterfaceRecordResponse, error,
) {
	resp := &reporter.ModifyInterfaceRecordResponse{}
	return resp, nil
}

func mock_default_reporterRpc_CreateSuiteRecord(req *reporter.PutSuiteRecordRequest) (
	*reporter.CreateSuiteRecordResponse, error,
) {
	resp := &reporter.CreateSuiteRecordResponse{}
	resp.SuiteExecuteId = req.GetSuiteExecuteId()
	if resp.GetSuiteExecuteId() == "" {
		resp.SuiteExecuteId = utils.GenExecuteId()
	}
	return resp, nil
}

func mock_default_reporterRpc_ModifySuiteRecord(req *reporter.PutSuiteRecordRequest) (
	*reporter.ModifySuiteRecordResponse, error,
) {
	resp := &reporter.ModifySuiteRecordResponse{}
	return resp, nil
}

func mock_default_reporterRpc_CreateServiceRecord(req *reporter.PutServiceRecordRequest) (
	*reporter.CreateServiceRecordResponse, error,
) {
	resp := &reporter.CreateServiceRecordResponse{}
	resp.ServiceExecuteId = req.GetServiceExecuteId()
	if resp.GetServiceExecuteId() == "" {
		resp.ServiceExecuteId = utils.GenExecuteId()
	}
	return resp, nil
}

func mock_default_reporterRpc_ModifyServiceRecord(req *reporter.PutServiceRecordRequest) (
	*reporter.ModifyServiceRecordResponse, error,
) {
	resp := &reporter.ModifyServiceRecordResponse{}
	return resp, nil
}

func mock_default_reporterRpc_CreatePlanRecord(req *reporter.PutPlanRecordRequest) (
	*reporter.CreatePlanRecordResponse, error,
) {
	resp := &reporter.CreatePlanRecordResponse{}
	resp.PlanExecuteId = req.GetPlanExecuteId()
	if resp.GetPlanExecuteId() == "" {
		resp.PlanExecuteId = utils.GenExecuteId()
	}
	return resp, nil
}

func mock_default_reporterRpc_ModifyPlanRecord(req *reporter.PutPlanRecordRequest) (
	*reporter.ModifyPlanRecordResponse, error,
) {
	resp := &reporter.ModifyPlanRecordResponse{}
	return resp, nil
}

func mock_default_reporterRpc_GetPlanRecord(req *reporter.GetPlanRecordRequest) (
	*reporter.GetPlanRecordResponse, error,
) {
	resp := &reporter.GetPlanRecordResponse{}
	return resp, nil
}

func mock_default_reporterRpc_GetUiPlanRecord(req *reporter.ViewUIPlanRecordRequest) (
	*reporter.ViewUIPlanRecordResponse, error,
) {
	resp := &reporter.ViewUIPlanRecordResponse{}
	return resp, nil
}
