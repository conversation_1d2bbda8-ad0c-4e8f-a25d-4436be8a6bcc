package zrpc

import (
	"context"
	"flag"

	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/notifier/rpc/client/notificationservice"
)

type NotifierRpc struct {
	conf zrpc.RpcClientConf

	Name string
	Cli  notificationservice.NotificationService
}

func NewNotifierRpc(conf zrpc.RpcClientConf) *NotifierRpc {
	cli := &NotifierRpc{
		conf: conf,
		Name: conf.Etcd.Key,
		Cli: notificationservice.NewNotificationService(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
	return cli
}

func (cli *NotifierRpc) Notify(ctx context.Context, in *notificationservice.NotifyReq) (
	*notificationservice.NotifyResp, error,
) {
	if flag.Lookup("test.v") != nil {
		return nil, nil
	}

	return cli.Cli.Notify(ctx, in)
}
