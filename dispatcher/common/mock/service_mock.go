package mock

import (
	"context"

	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"go.uber.org/zap"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	commonredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/zrpc"
)

func Mock_Mysql() (string, cache.CacheConf) {
	// return "root:66243766@tcp(***************:3306)/test?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai", []cache.NodeConf{
	return "probe:Quwan@2020_TTinternation@tcp(************:3306)/dispatcher?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai", []cache.NodeConf{
		{
			RedisConf: Mock_RedisConfV2(),
			Weight:    10,
		},
	}
}

func Mock_RedisConf() redis.RedisConf {
	return redis.RedisConf{
		Host: "*************:6379",
		Type: "node",
		Pass: "Quwan@2020",
		DB:   4,
	}
}

func Mock_RedisConfV2() redis.RedisConf {
	return redis.RedisConf{
		Host: "************:6379",
		Type: "node",
		DB:   4,
	}
}

func Mock_Redis() red.UniversalClient {
	return commonredis.NewClient(Mock_RedisConf())
}

func Mock_ReporterRpc() *zrpc.ReporterRPC {
	return &zrpc.ReporterRPC{
		Name: "rpc.reporter.prod",
	}
}

func Mock_DispatcherRpc() *zrpc.DispatcherRpc {
	return &zrpc.DispatcherRpc{
		Name: "rpc.dispatcher.prod",
	}
}

func Mock_ManagerRpc() *zrpc.ManagerRPC {
	return &zrpc.ManagerRPC{
		Name: "rpc.manager.prod",
	}
}

func Mock_UserRpc() *zrpc.UserRPC {
	return &zrpc.UserRPC{
		Name: "rpc.user.prod",
	}
}

func Mock_NotifyRpc() *zrpc.NotifierRpc {
	return &zrpc.NotifierRpc{
		Name: "rpc.notifier.prod",
	}
}

func Mock_ApiworkerProducerConf() producer.Config {
	return producer.Config{
		Broker:  "***************:6379",
		Backend: "***************:6379",
		Queue:   "mqc:apiworker:unittest",
		Db:      4,
	}
}

func Mock_ApiworkerProducer() *producer.Producer {
	return producer.NewProducer(Mock_ApiworkerProducerConf())
}

func Mock_UIApiworkerProducerConf() producer.Config {
	return producer.Config{
		Broker:  "***************:6379",
		Backend: "***************:6379",
		Queue:   "mqc:uiworker:unittest",
		Db:      4,
	}
}

func Mock_UIApiworkerProducer() *producer.Producer {
	return producer.NewProducer(Mock_UIApiworkerProducerConf())
}

func Mock_DispatcherProducerConf() producer.Config {
	return producer.Config{
		Broker:  "***************:6379",
		Backend: "***************:6379",
		Queue:   "mqc:dispatcher:unittest",
		Db:      4,
	}
}

func Mock_DispatcherProducer() *producer.Producer {
	return producer.NewProducer(Mock_DispatcherProducerConf())
}

func Mock_Log() {
	logconf := logx.LogConf{
		ServiceName: "mqc.dispatcher",
		Encoding:    "plain",
		Mode:        "console",
	}
	w := log.NewZapWriter(logconf, zap.AddCaller(), zap.Development())
	log.SetWriter(w)
}

func Mock_Context() context.Context {
	return context.Background()
}
