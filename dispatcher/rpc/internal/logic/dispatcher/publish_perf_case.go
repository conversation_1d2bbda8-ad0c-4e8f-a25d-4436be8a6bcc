package dispatcherlogic

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type PerfCasePublisher struct {
	*BasePublisher
}

func NewPerfCasePublisher(ctx context.Context, svcCtx *svc.ServiceContext) *PerfCasePublisher {
	return &PerfCasePublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *PerfCasePublisher) CreateRecord(in *pb.PublishReq) (err error) {
	var (
		perfPlanMetadata = in.GetPerfCase().GetMetaData()
		perfCase         = in.GetPerfCase().GetPerfCase().GetPerfCase()
		perfData         = perfCase.GetPerfData()

		perfDataInfo *reporterpb.PerfDataInfo
	)

	if perfData != nil {
		perfDataInfo = &reporterpb.PerfDataInfo{
			ProjectId:  perfData.GetProjectId(),
			DataId:     perfData.GetDataId(),
			Name:       perfData.GetName(),
			NumberOfVu: perfData.GetNumberOfVu(),
		}
	} else {
		perfDataInfo = &reporterpb.PerfDataInfo{
			ProjectId:  perfCase.GetProjectId(),
			NumberOfVu: perfCase.GetNumberOfVu(),
		}
	}

	_, err = l.svcCtx.PerfReporterRPC.CreatePerfCaseRecord(
		l.ctx, &reporterpb.CreatePerfCaseRecordReq{
			TaskId:         l.taskId,
			ExecuteId:      l.executeId,
			SuiteExecuteId: in.GetPerfCase().GetPerfSuiteExecuteId(),
			ProjectId:      in.GetProjectId(),
			CaseId:         in.GetPerfCase().GetPerfCaseId(),
			CaseName:       l.getComponentName(in.GetPerfCase().GetPerfCase()),
			Steps:          l.generatePerfCaseStepInfos(perfPlanMetadata.GetProtocol(), perfCase),
			PerfData:       perfDataInfo,
			LoadGenerator:  perfCase.GetLoadGenerator(),
			Status:         perfCase.GetState().String(),
			ExecutedBy:     in.GetUserId(),
			StartedAt:      timestamppb.New(time.Now()),
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}

	return nil
}

func (l *PerfCasePublisher) generatePerfCaseStepInfos(
	protocol commonpb.Protocol, perfCase *managerpb.PerfCaseComponent,
) []*reporterpb.PerfCaseStepInfo {
	var (
		setupSteps    = perfCase.GetSetupSteps()
		serialSteps   = perfCase.GetSerialSteps()
		parallelSteps = perfCase.GetParallelSteps()
		teardownSteps = perfCase.GetTeardownSteps()
	)

	steps := make(
		[]*reporterpb.PerfCaseStepInfo, 0, len(setupSteps)+len(serialSteps)+len(parallelSteps)+len(teardownSteps),
	)
	for _, item := range []struct {
		stepType commonpb.PerfCaseStepType
		steps    []*commonpb.PerfCaseStepV2
	}{
		{
			stepType: commonpb.PerfCaseStepType_PerfCaseStepType_SETUP,
			steps:    setupSteps,
		},
		{
			stepType: commonpb.PerfCaseStepType_PerfCaseStepType_SERIAL,
			steps:    serialSteps,
		},
		{
			stepType: commonpb.PerfCaseStepType_PerfCaseStepType_PARALLEL,
			steps:    parallelSteps,
		},
		{
			stepType: commonpb.PerfCaseStepType_PerfCaseStepType_TEARDOWN,
			steps:    teardownSteps,
		},
	} {
		for _, step := range item.steps {
			v := &reporterpb.PerfCaseStepInfo{
				Name:       step.GetName(),
				Type:       item.stepType,
				RateLimits: step.GetRateLimits(),
			}
			if protocol == commonpb.Protocol_PROTOCOL_HTTP {
				v.ApiName = step.GetUrl()
			} else {
				v.ApiName = step.GetMethod()
			}

			steps = append(steps, v)
		}
	}

	return steps
}

func (l *PerfCasePublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *PerfCasePublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	perfCaseData := in.GetPerfCase().GetPerfCase()
	perfPlanMetaData := in.GetPerfCase().GetMetaData()

	defer func() {
		_, err = l.recordPerfPlan(in, perfCaseData)
		if err != nil {
			l.Logger.Errorf("[PerfCasePublisher.publish] recordPerfPlan error:%s", err.Error())
			return
		}
	}()

	_, err = l.record(in, perfCaseData)
	if err != nil {
		return nil, err
	}

	if !l.IsValid(perfCaseData) || l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	taskData := l.getWorkerReq(in, perfCaseData, perfPlanMetaData)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "压测任务参数序列化失败, error: %s", err,
		)
	}
	l.Infof("perf case task data: %s", payload)

	task := base.NewTask(
		constants.MQTaskTypePerfWorkerExecutePerfTest,
		payload,
		base.WithMaxRetryOptions(0),
		base.WithTimeoutOptions(2*time.Hour),
		base.WithRetentionOptions(time.Hour),
	)

	// delay
	estimatedTime := taskData.GetPerfCase().GetPerfPlanInfo().GetEstimatedTime()
	if estimatedTime == 0 {
		// 立即发布任务
		_, err = l.svcCtx.PerfWorkerProducer.Send(
			l.ctx, task, mq.ConvertPbEnumerationToQueuePriority(taskData.GetPriorityType()),
		)
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err,
			)
		}
	} else {
		// 延迟发布任务
		nowTs := time.Now().Unix()
		delayTs := estimatedTime - nowTs
		_, err = l.svcCtx.PerfWorkerProducer.SendDelay(
			l.ctx, task, time.Second*time.Duration(delayTs),
			mq.ConvertPbEnumerationToQueuePriority(taskData.GetPriorityType()),
		)
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.InternalError, err.Error()), "发送延迟数据到mq失败, error: %s", err,
			)
		}
	}

	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   "",
	}, nil
}

func (l *PerfCasePublisher) getWorkerReq(
	in *pb.PublishReq, perfCaseData *managerpb.ApiExecutionData, perfPlanMetadata *managerpb.PerfPlanMetaData,
) *pb.WorkerReq {
	req := &pb.WorkerReq{
		TriggerMode:   in.GetTriggerMode(),
		TriggerRule:   in.GetTriggerRule(),
		ProjectId:     in.GetProjectId(),
		TaskId:        l.taskId,
		ExecuteId:     l.executeId,
		ExecuteType:   in.GetExecuteType(),
		WorkerType:    pb.WorkerType_WorkerType_PERF_CASE,
		GeneralConfig: perfPlanMetadata.GetGeneralConfig(),
		AccountConfig: []*commonpb.AccountConfig{
			perfPlanMetadata.GetAccountConfig(),
		},
		UserId:       in.GetUserId(),
		NodeData:     perfCaseData,
		Debug:        in.GetDebug(),
		PriorityType: l.priorityType,
		Data: &pb.WorkerReq_PerfCase{
			PerfCase: &pb.PerfCaseWorkerInfo{
				PerfCaseId:         in.GetPerfCase().GetPerfCaseId(),
				PerfCaseExecuteId:  in.GetPerfCase().GetPerfCaseExecuteId(),
				PerfSuiteId:        in.GetPerfCase().GetPerfSuiteId(),
				PerfSuiteExecuteId: in.GetPerfCase().GetPerfSuiteExecuteId(),
				PerfPlanId:         in.GetPerfCase().GetPerfPlanId(),
				PerfPlanExecuteId:  in.GetPerfCase().GetPerfPlanExecuteId(),
				MetaData:           perfPlanMetadata,
				PerfPlanInfo:       in.GetPerfCase().GetPerfPlanInfo(),
			},
		},
	}

	return req
}

func (l *PerfCasePublisher) record(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifyPerfCaseRecordResp, err error) {
	status := l.GetRecordStatus(l.IsValid(data))
	_, end := l.GetRecordTime(status)

	resp, err = l.svcCtx.PerfReporterRPC.ModifyPerfCaseRecord(
		l.ctx, &reporterpb.ModifyPerfCaseRecordReq{
			TaskId:         l.taskId,
			ExecuteId:      l.executeId,
			SuiteExecuteId: in.GetPerfCase().GetPerfSuiteExecuteId(),
			ProjectId:      in.GetProjectId(),
			CaseId:         data.GetId(),
			Status:         status.String(),
			EndedAt:        timestamppb.New(time.Unix(end, 0)),
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求perf reporter rpc服务出现错误, error: %+v", err,
		)
	}

	return resp, nil
}

func (l *PerfCasePublisher) recordPerfPlan(
	in *pb.PublishReq, _ *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifyPerfPlanRecordResp, err error) {
	var status string

	if in.GetPerfCase().GetPerfPlanInfo().GetEstimatedTime() != 0 &&
		in.GetPerfCase().GetPerfPlanInfo().GetExecuteType() == commonpb.PerfTaskType_RUN {
		status = constants.PerfExecuteStatePreview.String()
	} else {
		status = constants.PerfExecuteStateRunning.String()
	}

	resp, err = l.svcCtx.PerfReporterRPC.ModifyPerfPlanRecord(
		l.ctx, &reporterpb.ModifyPerfPlanRecordReq{
			TaskId:    l.taskId,
			ExecuteId: in.GetPerfCase().GetPerfPlanExecuteId(),

			ProjectId: in.GetProjectId(),
			PlanId:    in.GetPerfCase().GetPerfPlanId(),

			Status: status,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求perf reporter rpc服务出现错误, error: %+v", err,
		)
	}

	return resp, nil
}

// IsValid 是否有效
func (l *PerfCasePublisher) IsValid(data *managerpb.ApiExecutionData) bool {
	// 调试不受固有状态影响
	return true
}

// Panic 异常处理
func (l *PerfCasePublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	// 直接更新状态
	_, _ = l.svcCtx.PerfReporterRPC.ModifyPerfCaseRecord(
		l.ctx, &reporterpb.ModifyPerfCaseRecordReq{
			TaskId:         l.taskId,
			ExecuteId:      l.executeId,
			SuiteExecuteId: in.GetUiCase().GetUiSuiteExecuteId(),

			ProjectId: in.GetProjectId(),
			CaseId:    in.GetPerfCase().GetPerfCaseId(),

			Status:  pb.ComponentState_Panic.String(),
			EndedAt: timestamppb.New(time.Now()),
		},
	)

	if in.GetExecuteType() == managerpb.ApiExecutionDataType_PERF_CASE {
		return
	}

	// 发起回调
	cb := &pb.CallbackReq{
		TriggerMode:  in.GetTriggerMode(),
		TriggerRule:  in.GetTriggerRule(),
		ProjectId:    in.GetProjectId(),
		TaskId:       l.taskId,
		ExecuteType:  in.GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_PERF_CASE,
		Data: &pb.CallbackReq_PerfCase{
			PerfCase: &pb.PerfCaseCallbackData{
				PerfPlanId:         in.GetPerfCase().GetPerfPlanId(),
				PerfPlanExecuteId:  in.GetPerfCase().GetPerfPlanExecuteId(),
				PerfSuiteId:        in.GetPerfCase().GetPerfSuiteId(),
				PerfSuiteExecuteId: in.GetPerfCase().GetPerfSuiteExecuteId(),
				PerfCaseId:         in.GetPerfCase().GetPerfCaseId(),
				PerfCaseExecuteId:  l.executeId,
				CaseState:          pb.ComponentState_Panic,
				MetaData:           in.GetPerfCase().GetMetaData(),
				// Metrics:
			},
		},
	}

	_ = callbackclient.CallBack(
		l.ctx,
		l.svcCtx.DispatcherProducer,
		l.svcCtx.Config.Name,
		l.svcCtx.Config.DispatcherProducer.Queue,
		l.getUUID(),
		cb,
	)
}
