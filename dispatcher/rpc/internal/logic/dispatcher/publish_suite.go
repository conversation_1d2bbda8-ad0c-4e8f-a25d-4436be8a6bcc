package dispatcherlogic

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type SuitePublisher struct {
	*BasePublisher
}

func NewSuitePublisher(ctx context.Context, svcCtx *svc.ServiceContext) *SuitePublisher {
	return &SuitePublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *SuitePublisher) CreateRecord(in *pb.PublishReq) (err error) {
	_, err = l.svcCtx.ReporterRPC.CreateSuiteRecord(
		l.ctx, &reporterpb.PutSuiteRecordRequest{
			TaskId:         l.taskId,
			ExecuteId:      l.executeId,
			ProjectId:      in.GetProjectId(),
			ExecuteType:    in.GetExecuteType().String(),
			GeneralConfig:  jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetSuite().GetGeneralConfig())),
			AccountConfig:  jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetSuite().GetAccountConfig())),
			SuiteId:        in.GetSuite().GetSuiteId(),
			PlanExecuteId:  in.GetSuite().GetPlanExecuteId(),
			SuiteExecuteId: l.executeId,
			ExecutedBy:     in.GetUserId(),
			StartedAt:      time.Now().UnixMilli(),
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return nil
}

func (l *SuitePublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *SuitePublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	apiExecutionDataStructure, err := l.getApiExecutionDataStructure(
		&managerpb.GetApiExecutionDataStructureReq{
			ProjectId: in.GetProjectId(),
			Type:      managerpb.ApiExecutionDataType_API_SUITE,
			Id:        in.GetSuite().GetSuiteId(),
			Extra:     nil,
		},
	)
	if err != nil {
		return nil, err
	}

	_, err = l.record(in, apiExecutionDataStructure)
	if err != nil {
		return resp, err
	}

	if !l.IsValid(apiExecutionDataStructure) || l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	// 创建任务请求
	taskData := l.getDistributeReq(in, apiExecutionDataStructure)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "api任务参数序列化失败, error: %s", err,
		)
	}

	_, err = l.svcCtx.DispatcherProducer.Send(
		l.ctx, base.NewTask(
			constants.MQTaskTypeDispatcherSuite,
			payload,
			base.WithRetentionOptions(time.Minute*2),
			base.WithMaxRetryOptions(3),
		), base.QueuePriorityDefault,
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   l.getVersion(apiExecutionDataStructure),
	}, nil
}

func (l *SuitePublisher) getDistributeReq(in *pb.PublishReq, data *managerpb.ApiExecutionData) *pb.DistributeReq {
	suite := in.GetSuite()

	return &pb.DistributeReq{
		TriggerMode:    in.GetTriggerMode(),
		TriggerRule:    in.GetTriggerRule(),
		ProjectId:      in.GetProjectId(),
		TaskId:         l.taskId,
		ExecuteType:    in.GetExecuteType(),
		DistributeType: pb.DistributeType_DistributeType_API_SUITE,
		GeneralConfig:  suite.GetGeneralConfig(),
		AccountConfig:  suite.GetAccountConfig(),
		UserId:         in.GetUserId(),
		Debug:          in.GetDebug(),
		PurposeType:    in.GetPurposeType(),
		Data: &pb.DistributeReq_Suite{
			Suite: &pb.SuiteDistributeData{
				SuiteId:        suite.GetSuiteId(),
				SuiteExecuteId: l.executeId,
				PlanId:         suite.GetPlanId(),
				PlanExecuteId:  suite.GetPlanExecuteId(),
				State:          pb.ComponentState_Pending,
				Suite:          data.GetSuite(),
				Cases:          l.getCases(in, data),
				PlanName:       suite.GetPlanName(),
			},
		},
	}
}

func (l *SuitePublisher) record(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifySuiteRecordResponse, err error) {
	status := l.GetRecordStatus(l.IsValid(data))
	start, end := l.GetRecordTime(status)

	resp, err = l.svcCtx.ReporterRPC.ModifySuiteRecord(
		l.ctx, &reporterpb.PutSuiteRecordRequest{
			TaskId:         l.taskId,
			ExecuteId:      l.executeId,
			ProjectId:      in.GetProjectId(),
			ExecuteType:    in.GetExecuteType().String(),
			GeneralConfig:  jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetSuite().GetGeneralConfig())),
			AccountConfig:  jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetSuite().GetAccountConfig())),
			SuiteId:        in.GetSuite().GetSuiteId(),
			SuiteName:      data.GetSuite().GetName(),
			PlanExecuteId:  in.GetSuite().GetPlanExecuteId(),
			SuiteExecuteId: l.executeId,
			Status:         status.String(),
			ExecutedBy:     in.GetUserId(),
			StartedAt:      start,
			EndedAt:        end,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return
}

func (l *SuitePublisher) getCases(in *pb.PublishReq, data *managerpb.ApiExecutionData) []*managerpb.ApiExecutionData {
	cases := make([]*managerpb.ApiExecutionData, 0, 1)
	if len(in.GetSuite().GetCases()) > 0 {
		return in.GetSuite().GetCases()
	}

	if len(data.GetChildren()) == 0 {
		return cases
	}

	if len(data.GetChildren()[0].GetChild()) > 0 {
		cases = data.GetChildren()[0].GetChild()
	}
	return cases
}

// IsValid 是否有效
func (l *SuitePublisher) IsValid(data *managerpb.ApiExecutionData) bool {
	// 调试不受固有状态影响
	return true
}

// Panic 异常处理
func (l *SuitePublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	// 直接更新状态
	_, _ = l.svcCtx.ReporterRPC.ModifySuiteRecord(
		l.ctx, &reporterpb.PutSuiteRecordRequest{
			TaskId:         l.taskId,
			ProjectId:      in.GetProjectId(),
			ExecuteId:      l.executeId,
			ExecuteType:    in.GetExecuteType().String(),
			SuiteExecuteId: l.executeId,
			SuiteId:        in.GetSuite().GetSuiteId(),
			Status:         pb.ComponentState_Panic.String(),
			ExecutedBy:     in.GetUserId(),
			Content:        jsonx.MarshalToStringIgnoreError(err.Error()),
			EndedAt:        time.Now().UnixMilli(),
		},
	)
	if in.GetExecuteType() == managerpb.ApiExecutionDataType_API_SUITE {
		return
	}

	// 发起回调
	cb := &pb.CallbackReq{
		TriggerMode:  in.GetTriggerMode(),
		TriggerRule:  in.GetTriggerRule(),
		ProjectId:    in.GetProjectId(),
		TaskId:       l.taskId,
		ExecuteType:  in.GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_API_SUITE,
		PurposeType:  in.GetPurposeType(),
		Data: &pb.CallbackReq_Suite{
			Suite: &pb.SuiteCallbackData{
				PlanId:         in.GetSuite().GetPlanId(),
				PlanExecuteId:  in.GetSuite().GetPlanExecuteId(),
				SuiteId:        in.GetSuite().GetSuiteId(),
				SuiteExecuteId: l.executeId,
				SuiteState:     pb.ComponentState_Panic,
			},
		},
	}

	_ = callbackclient.CallBack(
		l.ctx,
		l.svcCtx.DispatcherProducer,
		l.svcCtx.Config.Name,
		l.svcCtx.Config.DispatcherProducer.Queue,
		l.getUUID(),
		cb,
	)
}
