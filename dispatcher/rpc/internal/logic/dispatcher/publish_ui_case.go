package dispatcherlogic

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type UICasePublisher struct {
	*BasePublisher
}

func NewUICasePublisher(ctx context.Context, svcCtx *svc.ServiceContext) *UICasePublisher {
	return &UICasePublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *UICasePublisher) CreateRecord(in *pb.PublishReq) (err error) {
	_, err = l.svcCtx.UIReporterRPC.CreateUICaseRecord(
		l.ctx, &reporterpb.PutUICaseRecordRequest{
			TaskId:         l.taskId,
			ProjectId:      in.GetProjectId(),
			ExecuteId:      l.executeId,
			CaseId:         in.GetUiCase().GetUiCaseId(),
			CaseName:       l.getComponentName(in.GetUiCase().GetUiCase()),
			SuiteExecuteId: in.GetUiCase().GetUiSuiteExecuteId(),
			ExecutedBy:     in.GetTriggerUser().GetAccount(),
			StartedAt:      time.Now().UnixMilli(),
			Udid:           l.getDeviceUDID(in.GetUiCase().GetUiCase()),
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}

	return nil
}

func (l *UICasePublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *UICasePublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	mgrData := in.GetUiCase().GetUiCase()

	_, err = l.record(in, mgrData)
	if err != nil {
		return nil, err
	}

	if !l.IsValid(mgrData) || l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	taskData := l.getWorkerReq(in, mgrData)

	//gitURL := in.GetUiCase().GetMetaData().GetGitConfig().GetUrl()
	//gitAccessToken := in.GetUiCase().GetMetaData().GetGitConfig().GetAccessToken()
	//u, err := url.Parse(gitURL)
	//if err != nil {
	//	return nil, errors.Wrapf(
	//		errorx.Err(errorx.ValidateParamError, err.Error()), "failed to parse the git url[%s], error: %+v", gitURL,
	//		err,
	//	)
	//} else if _, ok := u.User.Password(); !ok || u.User.Username() == "" {
	//	u.User = url.UserPassword(constants.ConstDefaultGitUsername, gitAccessToken)
	//}
	//gitURLWithAuth := u.String()

	//taskInfo := &pb.UITestTaskInfo{
	//	ProjectId:           in.GetProjectId(),
	//	PlanId:              in.GetUiCase().GetUiPlanId(),
	//	TaskId:              in.GetTaskId(),
	//	TestTarget:          taskData.GetNodeData().GetUiCase().GetPath(),
	//	TestFrameworkUrl:    gitURLWithAuth,
	//	TestFrameworkBranch: in.GetUiCase().GetMetaData().GetGitConfig().GetBranch(),
	//	DeviceType:          in.GetUiCase().GetMetaData().GetDeviceType(),
	//	PlatformType:        in.GetUiCase().GetMetaData().GetPlatformType(),
	//	AppDownloadLink:     in.GetUiCase().GetMetaData().GetAppDownloadLink(),
	//	TestLanguage:        in.GetUiCase().GetMetaData().GetTestLanguage(),
	//	TestLanguageVersion: in.GetUiCase().GetMetaData().GetTestLanguageVersion(),
	//	TestFramework:       in.GetUiCase().GetMetaData().GetTestFramework(),
	//	TestArgs:            in.GetUiCase().GetMetaData().GetTestArgs(),
	//	TriggerMode:         in.GetTriggerMode(),
	//	TriggerRule:         in.GetTriggerRule(),
	//	ExecuteType:         in.GetExecuteType(),
	//	CallbackType:        pb.CallbackType_CallbackType_UI_CASE,
	//	UiCase:              taskData.GetUiCase(),
	//	PriorityType:        taskData.GetPriorityType(),
	//}
	task := base.NewTask(
		constants.MQTaskTypeUIWorkerExecuteUiTest,
		protobuf.MarshalJSONIgnoreError(taskData),
		base.WithMaxRetryOptions(0),
		base.WithTimeoutOptions(time.Hour),
		base.WithRetentionOptions(time.Hour),
	)

	// 发布任务
	_, err = l.svcCtx.UIWorkerProducer.Send(
		l.ctx, task, mq.ConvertPbEnumerationToQueuePriority(taskData.GetPriorityType()),
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   "",
	}, nil
}

func (l *UICasePublisher) getWorkerReq(in *pb.PublishReq, data *managerpb.ApiExecutionData) *pb.WorkerReq {
	return &pb.WorkerReq{
		TriggerMode:   in.GetTriggerMode(),
		TriggerRule:   in.GetTriggerRule(),
		ProjectId:     in.GetProjectId(),
		TaskId:        l.taskId,
		ExecuteId:     l.executeId,
		ExecuteType:   in.GetExecuteType(),
		WorkerType:    pb.WorkerType_WorkerType_UI_CASE,
		GeneralConfig: nil,
		AccountConfig: nil,
		UserId:        in.GetUserId(),
		NodeData:      data,
		Debug:         in.GetDebug(),
		PriorityType:  l.priorityType,
		Data: &pb.WorkerReq_UiCase{
			UiCase: &pb.UICaseWorkerInfo{
				UiCaseId:         in.GetUiCase().GetUiCaseId(),
				UiCaseExecuteId:  l.executeId,
				UiSuiteId:        in.GetUiCase().GetUiSuiteId(),
				UiSuiteExecuteId: in.GetUiCase().GetUiSuiteExecuteId(),
				UiPlanId:         in.GetUiCase().GetUiPlanId(),
				UiPlanExecuteId:  in.GetUiCase().GetUiPlanExecuteId(),
				MetaData:         in.GetUiCase().GetMetaData(),
			},
		},
	}
}

func (l *UICasePublisher) record(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifyUICaseRecordResponse, err error) {
	status := l.GetRecordStatus(l.IsValid(data))
	start, end := l.GetRecordTime(status)

	resp, err = l.svcCtx.UIReporterRPC.ModifyUICaseRecord(
		l.ctx, &reporterpb.PutUICaseRecordRequest{
			TaskId:         l.taskId,
			ProjectId:      in.GetProjectId(),
			ExecuteId:      l.executeId,
			CaseId:         data.GetId(),
			CaseName:       l.getComponentName(data),
			SuiteExecuteId: in.GetUiCase().GetUiSuiteExecuteId(),
			Status:         status.String(),
			ExecutedBy:     in.GetTriggerUser().GetAccount(),
			StartedAt:      start,
			EndedAt:        end,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求ui reporter rpc服务出现错误, error: %+v", err,
		)
	}

	return resp, nil
}

// IsValid 是否有效
func (l *UICasePublisher) IsValid(data *managerpb.ApiExecutionData) bool {
	// 调试不受固有状态影响
	return true
}

// Panic 异常处理
func (l *UICasePublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	// 直接更新状态
	_, _ = l.svcCtx.UIReporterRPC.ModifyUICaseRecord(
		l.ctx, &reporterpb.PutUICaseRecordRequest{
			TaskId:         l.taskId,
			ProjectId:      in.GetProjectId(),
			ExecuteId:      l.executeId,
			CaseId:         in.GetUiCase().GetUiCaseId(),
			CaseName:       in.GetUiCase().GetUiCase().GetUiCase().GetName(),
			SuiteExecuteId: in.GetUiCase().GetUiSuiteExecuteId(),
			Status:         pb.ComponentState_Panic.String(),
			Content:        jsonx.MarshalToStringIgnoreError(err.Error()),
			ExecutedBy:     in.GetTriggerUser().GetAccount(),
			EndedAt:        time.Now().UnixMilli(),
		},
	)

	if in.GetExecuteType() == managerpb.ApiExecutionDataType_UI_CASE {
		return
	}

	// 发起回调
	cb := &pb.CallbackReq{
		TriggerMode:  in.GetTriggerMode(),
		TriggerRule:  in.GetTriggerRule(),
		ProjectId:    in.GetProjectId(),
		TaskId:       l.taskId,
		ExecuteType:  in.GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_UI_CASE,
		Data: &pb.CallbackReq_UiCase{
			UiCase: &pb.UICaseCallbackData{
				UiPlanId:         in.GetUiCase().GetUiPlanId(),
				UiPlanExecuteId:  in.GetUiCase().GetUiPlanExecuteId(),
				UiSuiteId:        in.GetUiCase().GetUiSuiteId(),
				UiSuiteExecuteId: in.GetUiCase().GetUiSuiteExecuteId(),
				UiCaseId:         in.GetUiCase().GetUiCaseId(),
				UiCaseExecuteId:  l.executeId,
				CaseState:        pb.ComponentState_Panic,
				MetaData:         in.GetUiCase().GetMetaData(),
			},
		},
	}

	_ = callbackclient.CallBack(
		l.ctx,
		l.svcCtx.DispatcherProducer,
		l.svcCtx.Config.Name,
		l.svcCtx.Config.DispatcherProducer.Queue,
		l.getUUID(),
		cb,
	)
}
