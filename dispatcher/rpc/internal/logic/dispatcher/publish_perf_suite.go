package dispatcherlogic

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type PerfSuitePublisher struct {
	*BasePublisher
}

func NewPerfSuitePublisher(ctx context.Context, svcCtx *svc.ServiceContext) *PerfSuitePublisher {
	return &PerfSuitePublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *PerfSuitePublisher) CreateRecord(in *pb.PublishReq) (err error) {
	_, err = l.svcCtx.PerfReporterRPC.CreatePerfSuiteRecord(
		l.ctx, &reporterpb.CreatePerfSuiteRecordReq{
			TaskId:        l.taskId,
			ExecuteId:     l.executeId,
			PlanExecuteId: in.GetPerfSuite().GetPerfPlanExecuteId(),

			ProjectId: in.GetProjectId(),
			SuiteId:   in.GetPerfSuite().GetPerfSuiteId(),
			SuiteName: in.GetPerfSuite().GetPerfSuite().GetPerfSuite().GetName(),

			Status:     in.GetPerfSuite().GetPerfSuite().GetPerfSuite().GetState().String(),
			ExecutedBy: in.GetUserId(),
			StartedAt:  timestamppb.New(time.Now()),
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return nil
}

func (l *PerfSuitePublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *PerfSuitePublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	mgrData := in.GetPerfSuite().PerfSuite
	_, err = l.record(in, mgrData)
	if err != nil {
		return resp, err
	}

	if !l.IsValid(mgrData) || l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	// 创建任务请求
	taskData := l.getDistributeReq(in, mgrData)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "压测任务参数序列化失败, error: %s", err,
		)
	}

	// 直接使用taskData中的优先级因为已经在上层判断设置
	task := base.NewTask(
		constants.MQTaskTypeDispatcherPerfSuite, payload,
		base.WithMaxRetryOptions(0),
		base.WithRetentionOptions(time.Second*300),
	)
	_, err = l.svcCtx.DispatcherProducer.Send(
		l.ctx, task, mq.ConvertPbEnumerationToQueuePriority(taskData.PriorityType),
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   "",
	}, nil
}

func (l *PerfSuitePublisher) getDistributeReq(in *pb.PublishReq, data *managerpb.ApiExecutionData) *pb.DistributeReq {
	return &pb.DistributeReq{
		TriggerMode:    in.GetTriggerMode(),
		TriggerRule:    in.GetTriggerRule(),
		ProjectId:      in.GetProjectId(),
		TaskId:         l.taskId,
		ExecuteType:    in.GetExecuteType(),
		DistributeType: pb.DistributeType_DistributeType_PERF_SUITE,
		GeneralConfig:  in.GetSuite().GetGeneralConfig(),
		AccountConfig:  in.GetSuite().GetAccountConfig(),
		UserId:         in.GetUserId(),
		PriorityType:   in.GetPriorityType(), // 不取data 因为上层设置在了根类中
		Debug:          in.GetDebug(),
		Data: &pb.DistributeReq_PerfSuite{
			PerfSuite: &pb.PerfSuiteDistributeData{
				PerfSuiteId:        in.GetPerfSuite().GetPerfSuiteId(),
				PerfSuiteExecuteId: l.executeId,
				PerfPlanId:         in.GetPerfSuite().GetPerfPlanId(),
				PerfPlanExecuteId:  in.GetPerfSuite().GetPerfPlanExecuteId(),
				State:              pb.ComponentState_Pending,
				PerfSuite:          data.GetPerfSuite(),
				PerfCases:          l.getPerfCases(in, data),
				MetaData:           in.GetPerfSuite().GetMetaData(),
				PerfPlanInfo:       in.GetPerfSuite().GetPerfPlanInfo(),
			},
		},
	}
}

func (l *PerfSuitePublisher) record(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifyPerfSuiteRecordResp, err error) {
	status := l.GetRecordStatus(l.IsValid(data))
	_, end := l.GetRecordTime(status)

	resp, err = l.svcCtx.PerfReporterRPC.ModifyPerfSuiteRecord(
		l.ctx, &reporterpb.ModifyPerfSuiteRecordReq{
			TaskId:        l.taskId,
			ProjectId:     in.GetProjectId(),
			ExecuteId:     l.executeId,
			SuiteId:       in.GetPerfSuite().GetPerfSuiteId(),
			PlanExecuteId: in.GetPerfSuite().GetPerfPlanExecuteId(),
			Status:        status.String(),
			EndedAt:       timestamppb.New(time.Unix(end, 0)),
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}

	return
}

func (l *PerfSuitePublisher) getPerfCases(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) []*managerpb.ApiExecutionData {
	if len(in.GetPerfSuite().GetPerfCases()) > 0 {
		return in.GetPerfSuite().GetPerfCases()
	}

	if len(data.GetChildren()) == 0 || len(data.GetChildren()[0].GetChild()) == 0 {
		return make([]*managerpb.ApiExecutionData, 0)
	}

	return data.GetChildren()[0].GetChild()
}

// IsValid 是否有效
func (l *PerfSuitePublisher) IsValid(data *managerpb.ApiExecutionData) bool {
	// 调试不受固有状态影响
	return true
}

// Panic 异常处理
func (l *PerfSuitePublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	// 直接更新状态
	_, _ = l.svcCtx.PerfReporterRPC.ModifyPerfSuiteRecord(
		l.ctx, &reporterpb.ModifyPerfSuiteRecordReq{
			TaskId:    l.taskId,
			ProjectId: in.GetProjectId(),
			ExecuteId: l.executeId,
			SuiteId:   in.GetPerfSuite().GetPerfSuiteId(),
			Status:    pb.ComponentState_Panic.String(),
			EndedAt:   timestamppb.New(time.Now()),
		},
	)
	if in.GetExecuteType() == managerpb.ApiExecutionDataType_PERF_SUITE {
		return
	}
	//
	//// 发起回调
	//cb := &pb.CallbackReq{
	//    TriggerMode:  in.GetTriggerMode(),
	//    TriggerRule:  in.GetTriggerRule(),
	//    ProjectId:    in.GetProjectId(),
	//    TaskId:       l.taskId,
	//    ExecuteType:  in.GetExecuteType(),
	//    CallbackType: pb.CallbackType_CallbackType_UI_SUITE,
	//    Data: &pb.CallbackReq_UiSuite{
	//        UiSuite: &pb.UISuiteCallbackData{
	//            UiPlanId:         in.GetUiSuite().GetUiPlanId(),
	//            UiPlanExecuteId:  in.GetUiSuite().GetUiPlanExecuteId(),
	//            UiSuiteId:        in.GetUiSuite().GetUiSuiteId(),
	//            UiSuiteExecuteId: l.executeId,
	//            SuiteState:       pb.ComponentState_Panic,
	//            MetaData:         in.GetUiSuite().GetMetaData(),
	//        },
	//    },
	//}
	//
	//_ = callbackclient.CallBack(
	//    l.ctx,
	//    l.svcCtx.DispatcherProducer,
	//    l.svcCtx.Config.Name,
	//    l.svcCtx.Config.DispatcherProducer.Queue,
	//    l.getUUID(),
	//    cb,
	//)
}
