package dispatcherlogic

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type PrecisionInterfacePublisher struct {
	*BasePublisher
}

// Deprecated: invalid publish type
func NewPrecisionInterfacePublisher(ctx context.Context, svcCtx *svc.ServiceContext) *PrecisionInterfacePublisher {
	return &PrecisionInterfacePublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *PrecisionInterfacePublisher) CreateRecord(in *pb.PublishReq) (err error) {
	_, err = l.svcCtx.ReporterRPC.CreateInterfaceRecord(
		l.ctx, &reporterpb.PutInterfaceRecordRequest{
			TaskId:             l.taskId,
			ExecuteId:          l.executeId,
			ExecuteType:        in.GetExecuteType().String(),
			ProjectId:          in.GetProjectId(),
			GeneralConfig:      jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetPrecisionInterface().GetGeneralConfig())),
			AccountConfig:      jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetPrecisionInterface().GetAccountConfig())),
			InterfaceId:        in.GetPrecisionInterface().GetInterfaceDocumentId(),
			PlanExecuteId:      in.GetPrecisionInterface().GetPlanExecuteId(),
			InterfaceExecuteId: l.executeId,
			ExecutedBy:         in.GetUserId(),
			StartedAt:          time.Now().UnixMilli(),
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return nil
}

func (l *PrecisionInterfacePublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *PrecisionInterfacePublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	var caseChildren []*managerpb.ApiExecutionData_ChildData
	for _, caseExecutionData := range in.GetPrecisionInterface().GetInterfaceCases() {
		caseChild := &managerpb.ApiExecutionData_ChildData{
			Child: []*managerpb.ApiExecutionData{caseExecutionData},
		}
		caseChildren = append(caseChildren, caseChild)
	}

	virtualInterfaceDocumentName := fmt.Sprintf("精准测试接口用例_%d", time.Now().UnixMilli())

	apiExecutionDataStructure := &managerpb.ApiExecutionData{
		Id:   in.GetPrecisionInterface().GetInterfaceDocumentId(),
		Type: managerpb.ApiExecutionDataType_INTERFACE_DOCUMENT,
		Data: &managerpb.ApiExecutionData_InterfaceDocument{
			InterfaceDocument: &managerpb.InterfaceDocumentComponent{
				ProjectId:         in.ProjectId,
				DocumentId:        in.GetPrecisionInterface().GetInterfaceDocumentId(),
				Name:              virtualInterfaceDocumentName,
				State:             managerpb.CommonState_CS_ENABLE,
				ReferenceState:    managerpb.CommonState_CS_ENABLE,
				CaseExecutionMode: managerpb.ExecutionMode_EM_PARALLEL,
			},
		},
		Children: caseChildren,
	}

	_, err = l.record(in, apiExecutionDataStructure)
	if err != nil {
		return resp, err
	}

	if !l.IsValid(apiExecutionDataStructure) || l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	taskData := l.getDistributeReq(in, apiExecutionDataStructure)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "api任务参数序列化失败, error: %s", err,
		)
	}

	_, err = l.svcCtx.DispatcherProducer.Send(
		l.ctx, base.NewTask(
			constants.MQTaskTypeDispatcherPrecisionInterface,
			payload,
			base.WithRetentionOptions(time.Minute*2),
			base.WithMaxRetryOptions(3),
		), base.QueuePriorityDefault,
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}
	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   l.getVersion(apiExecutionDataStructure),
	}, nil
}

func (l *PrecisionInterfacePublisher) getDistributeReq(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) *pb.DistributeReq {
	document := in.GetPrecisionInterface()

	return &pb.DistributeReq{
		TriggerMode:    in.GetTriggerMode(),
		TriggerRule:    in.GetTriggerRule(),
		ProjectId:      in.GetProjectId(),
		TaskId:         l.taskId,
		ExecuteType:    in.GetExecuteType(),
		DistributeType: pb.DistributeType_DistributeType_INTERFACE_DOCUMENT,
		GeneralConfig:  document.GetGeneralConfig(),
		AccountConfig:  document.GetAccountConfig(),
		UserId:         in.GetUserId(),
		Debug:          in.GetDebug(),
		Data: &pb.DistributeReq_InterfaceDocument{
			InterfaceDocument: &pb.InterfaceDocumentDistributeData{
				InterfaceDocumentId:        document.GetInterfaceDocumentId(),
				InterfaceDocumentExecuteId: l.executeId,
				PlanId:                     document.GetPlanId(),
				PlanExecuteId:              document.GetPlanExecuteId(),
				State:                      pb.ComponentState_Pending,
				InterfaceDocument:          data.GetInterfaceDocument(),
				InterfaceCases:             l.getCases(in),
				PlanName:                   document.GetPlanName(),
			},
		},
	}
}

func (l *PrecisionInterfacePublisher) record(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifyInterfaceRecordResponse, err error) {
	status := l.GetRecordStatus(l.IsValid(data))
	start, end := l.GetRecordTime(status)

	resp, err = l.svcCtx.ReporterRPC.ModifyInterfaceRecord(
		l.ctx, &reporterpb.PutInterfaceRecordRequest{
			TaskId:             l.taskId,
			ExecuteId:          l.executeId,
			ExecuteType:        in.GetExecuteType().String(),
			ProjectId:          in.GetProjectId(),
			GeneralConfig:      jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetPrecisionInterface().GetGeneralConfig())),
			AccountConfig:      jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetPrecisionInterface().GetAccountConfig())),
			InterfaceId:        in.GetPrecisionInterface().GetInterfaceDocumentId(),
			InterfaceName:      data.GetInterfaceDocument().GetName(),
			PlanExecuteId:      in.GetPrecisionInterface().GetPlanExecuteId(),
			InterfaceExecuteId: l.executeId,
			Status:             status.String(),
			ExecutedBy:         in.GetUserId(),
			StartedAt:          start,
			EndedAt:            end,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return
}

func (l *PrecisionInterfacePublisher) getCases(in *pb.PublishReq) []*managerpb.ApiExecutionData {
	cases := make([]*managerpb.ApiExecutionData, 0, 1)
	if len(in.GetPrecisionInterface().GetInterfaceCases()) > 0 {
		return in.GetPrecisionInterface().GetInterfaceCases()
	}
	return cases
}

// IsValid 是否有效
func (l *PrecisionInterfacePublisher) IsValid(data *managerpb.ApiExecutionData) bool {
	// 调试不受固有状态影响
	return true
}

// Panic 异常处理
func (l *PrecisionInterfacePublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	_, _ = l.svcCtx.ReporterRPC.ModifyInterfaceRecord(
		l.ctx, &reporterpb.PutInterfaceRecordRequest{
			TaskId:             l.taskId,
			ProjectId:          in.GetProjectId(),
			ExecuteId:          l.executeId,
			ExecuteType:        in.GetExecuteType().String(),
			InterfaceExecuteId: l.executeId,
			InterfaceId:        in.GetPrecisionInterface().GetInterfaceDocumentId(),
			Status:             pb.ComponentState_Panic.String(),
			ExecutedBy:         in.GetUserId(),
			Content:            jsonx.MarshalToStringIgnoreError(err.Error()),
			EndedAt:            time.Now().UnixMilli(),
		},
	)

	// 发起回调
	cb := &pb.CallbackReq{
		TriggerMode:  in.GetTriggerMode(),
		TriggerRule:  in.GetTriggerRule(),
		ProjectId:    in.GetProjectId(),
		TaskId:       l.taskId,
		ExecuteType:  in.GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_INTERFACE_DOCUMENT,
		PurposeType:  in.GetPurposeType(),
		Data: &pb.CallbackReq_InterfaceDocument{
			InterfaceDocument: &pb.InterfaceDocumentCallbackData{
				PlanId:             in.GetPrecisionInterface().GetPlanId(),
				PlanExecuteId:      in.GetPrecisionInterface().GetPlanExecuteId(),
				InterfaceId:        in.GetPrecisionInterface().GetInterfaceDocumentId(),
				InterfaceExecuteId: l.executeId,
				InterfaceState:     pb.ComponentState_Panic,
			},
		},
	}

	_ = callbackclient.CallBack(
		l.ctx,
		l.svcCtx.DispatcherProducer,
		l.svcCtx.Config.Name,
		l.svcCtx.Config.DispatcherProducer.Queue,
		l.getUUID(),
		cb,
	)
}
