package dispatcherlogic

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/callbackclient"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type UISuitePublisher struct {
	*BasePublisher
}

func NewUISuitePublisher(ctx context.Context, svcCtx *svc.ServiceContext) *UISuitePublisher {
	return &UISuitePublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *UISuitePublisher) CreateRecord(in *pb.PublishReq) (err error) {
	_, err = l.svcCtx.UIReporterRPC.CreateUISuiteRecord(
		l.ctx, &reporterpb.PutUISuiteRecordRequest{
			TaskId:        l.taskId,
			ProjectId:     in.GetProjectId(),
			ExecuteId:     l.executeId,
			SuiteId:       in.GetUiSuite().GetUiSuiteId(),
			PlanExecuteId: in.GetUiSuite().GetUiPlanExecuteId(),
			ExecutedBy:    in.GetUserId(),
			StartedAt:     time.Now().UnixMilli(),
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return nil
}

func (l *UISuitePublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *UISuitePublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	mgrData := in.GetUiSuite().GetUiSuite()
	_, err = l.record(in, mgrData)
	if err != nil {
		return resp, err
	}

	if !l.IsValid(mgrData) || l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	// 创建任务请求
	taskData := l.getDistributeReq(in, mgrData)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "ui任务参数序列化失败, error: %s", err,
		)
	}

	// 直接使用taskData中的优先级因为已经在上层判断设置
	newTask := base.NewTask(
		constants.MQTaskTypeDispatcherUISuite, payload,
		base.WithMaxRetryOptions(0),
		base.WithRetentionOptions(time.Second*300),
	)
	_, err = l.svcCtx.DispatcherProducer.Send(
		l.ctx, newTask, mq.ConvertPbEnumerationToQueuePriority(taskData.PriorityType),
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   "",
	}, nil
}

func (l *UISuitePublisher) getDistributeReq(in *pb.PublishReq, data *managerpb.ApiExecutionData) *pb.DistributeReq {
	return &pb.DistributeReq{
		TriggerMode:    in.GetTriggerMode(),
		TriggerRule:    in.GetTriggerRule(),
		ProjectId:      in.GetProjectId(),
		TaskId:         l.taskId,
		ExecuteType:    in.GetExecuteType(),
		DistributeType: pb.DistributeType_DistributeType_UI_SUITE,
		GeneralConfig:  in.GetSuite().GetGeneralConfig(),
		AccountConfig:  in.GetSuite().GetAccountConfig(),
		UserId:         in.GetUserId(),
		PriorityType:   in.GetPriorityType(), // 不取data 因为上层设置在了根类中
		Debug:          in.GetDebug(),
		Data: &pb.DistributeReq_UiSuite{
			UiSuite: &pb.UISuiteDistributeData{
				UiSuiteId:        in.GetUiSuite().GetUiSuiteId(),
				UiSuiteExecuteId: l.executeId,
				UiPlanId:         in.GetUiSuite().GetUiPlanId(),
				UiPlanExecuteId:  in.GetUiSuite().GetUiPlanExecuteId(),
				State:            pb.ComponentState_Pending,
				UiSuite:          data.GetUiSuite(),
				UiCases:          l.getCases(in, data),
				MetaData:         in.GetUiSuite().GetMetaData(),
			},
		},
	}
}

func (l *UISuitePublisher) record(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifyUISuiteRecordResponse, err error) {
	status := l.GetRecordStatus(l.IsValid(data))
	start, end := l.GetRecordTime(status)

	resp, err = l.svcCtx.UIReporterRPC.ModifyUISuiteRecord(
		l.ctx, &reporterpb.PutUISuiteRecordRequest{
			TaskId:        l.taskId,
			ProjectId:     in.GetProjectId(),
			ExecuteId:     l.executeId,
			SuiteId:       in.GetUiSuite().GetUiSuiteId(),
			PlanExecuteId: in.GetUiSuite().GetUiPlanExecuteId(),
			SuiteName:     data.GetUiSuite().GetName(),
			Status:        status.String(),
			ExecutedBy:    in.GetUserId(),
			StartedAt:     start,
			EndedAt:       end,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}

	return
}

func (l *UISuitePublisher) getCases(in *pb.PublishReq, data *managerpb.ApiExecutionData) []*managerpb.ApiExecutionData {
	cases := make([]*managerpb.ApiExecutionData, 0, 1)
	if len(in.GetUiSuite().GetUiCases()) > 0 {
		return in.GetUiSuite().GetUiCases()
	}

	if len(data.GetChildren()) == 0 {
		return cases
	}

	if len(data.GetChildren()[0].GetChild()) > 0 {
		cases = data.GetChildren()[0].GetChild()
	}
	return cases
}

// IsValid 是否有效
func (l *UISuitePublisher) IsValid(data *managerpb.ApiExecutionData) bool {
	// 调试不受固有状态影响
	return true
}

// Panic 异常处理
func (l *UISuitePublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	// 直接更新状态
	_, _ = l.svcCtx.UIReporterRPC.ModifyUISuiteRecord(
		l.ctx, &reporterpb.PutUISuiteRecordRequest{
			TaskId:     l.taskId,
			ProjectId:  in.GetProjectId(),
			ExecuteId:  l.executeId,
			SuiteId:    in.GetUiSuite().GetUiSuiteId(),
			Status:     pb.ComponentState_Panic.String(),
			ExecutedBy: in.GetUserId(),
			Content:    jsonx.MarshalToStringIgnoreError(err.Error()),
			EndedAt:    time.Now().UnixMilli(),
		},
	)
	if in.GetExecuteType() == managerpb.ApiExecutionDataType_UI_SUITE {
		return
	}

	// 发起回调
	cb := &pb.CallbackReq{
		TriggerMode:  in.GetTriggerMode(),
		TriggerRule:  in.GetTriggerRule(),
		ProjectId:    in.GetProjectId(),
		TaskId:       l.taskId,
		ExecuteType:  in.GetExecuteType(),
		CallbackType: pb.CallbackType_CallbackType_UI_SUITE,
		Data: &pb.CallbackReq_UiSuite{
			UiSuite: &pb.UISuiteCallbackData{
				UiPlanId:         in.GetUiSuite().GetUiPlanId(),
				UiPlanExecuteId:  in.GetUiSuite().GetUiPlanExecuteId(),
				UiSuiteId:        in.GetUiSuite().GetUiSuiteId(),
				UiSuiteExecuteId: l.executeId,
				SuiteState:       pb.ComponentState_Panic,
				MetaData:         in.GetUiSuite().GetMetaData(),
			},
		},
	}

	_ = callbackclient.CallBack(
		l.ctx,
		l.svcCtx.DispatcherProducer,
		l.svcCtx.Config.Name,
		l.svcCtx.Config.DispatcherProducer.Queue,
		l.getUUID(),
		cb,
	)
}
