package dispatcherlogic

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	dutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type BasePublisher struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger

	taskId       string
	executeId    string
	isStop       bool                  // 是否已经停止
	priorityType commonpb.PriorityType // 优先级
}

func NewBasePublisher(ctx context.Context, svcCtx *svc.ServiceContext) *BasePublisher {
	return &BasePublisher{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *BasePublisher) setTaskId(in *pb.PublishReq) {
	l.taskId = in.GetTaskId()
	if l.taskId == "" {
		l.taskId = utils.GenTaskId()
	}
}

func (l *BasePublisher) setExecuteId(in *pb.PublishReq) {
	l.executeId = in.GetExecuteId()
	if l.executeId == "" {
		l.executeId = utils.GenExecuteId()
	}
}

func (l *BasePublisher) setPriorityType(in *pb.PublishReq) {
	l.priorityType = in.GetPriorityType()
}

func (l *BasePublisher) Setup(in *pb.PublishReq) (err error) {
	l.setTaskId(in)
	l.setExecuteId(in)
	l.setPriorityType(in)

	// 获取停止状态
	l.isStop, err = dutils.GetStopStatus(l.ctx, l.svcCtx.Redis, l.taskId)
	if err != nil {
		return errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "获取停止状态失败, error: %s", err)
	}

	return nil
}

func (l *BasePublisher) getApiExecutionData(req *managerpb.GetApiExecutionDataReq) (
	resp *managerpb.ApiExecutionData, err error,
) {
	resp, err = l.svcCtx.ManagerRPC.GetApiExecutionData(l.ctx, req)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (l *BasePublisher) getApiExecutionDataStructure(req *managerpb.GetApiExecutionDataStructureReq) (
	resp *managerpb.ApiExecutionData, err error,
) {
	resp, err = l.svcCtx.ManagerRPC.GetApiExecutionDataStructure(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "获取执行数据结构失败, error: %s", err)
	}

	return resp, nil
}

func (l *BasePublisher) getMaintainer(data *managerpb.ApiExecutionData) string {
	if data == nil {
		return ""
	}

	switch data.GetType() {
	case managerpb.ApiExecutionDataType_API_CASE:
		return data.GetCase().GetMaintainedBy()
	case managerpb.ApiExecutionDataType_INTERFACE_CASE:
		return data.GetInterfaceCase().GetMaintainedBy()
	default:
		return ""
	}
}

func (l *BasePublisher) getVersion(data *managerpb.ApiExecutionData) string {
	if data == nil {
		return ""
	}

	switch data.GetType() {
	case managerpb.ApiExecutionDataType_API_COMPONENT_GROUP:
		return data.GetGroup().GetVersion()
	case managerpb.ApiExecutionDataType_API_CASE:
		return data.GetCase().GetVersion()
	case managerpb.ApiExecutionDataType_INTERFACE_CASE:
		return data.GetInterfaceCase().GetVersion()
	default:
		return ""
	}
}

func (l *BasePublisher) getComponentName(data *managerpb.ApiExecutionData) string {
	if data == nil {
		return ""
	}

	switch data.GetType() {
	case managerpb.ApiExecutionDataType_API_COMPONENT_GROUP:
		return data.GetGroup().GetName()
	case managerpb.ApiExecutionDataType_API_CASE:
		return data.GetCase().GetName()
	case managerpb.ApiExecutionDataType_API_SUITE:
		return data.GetSuite().GetName()
	case managerpb.ApiExecutionDataType_API_PLAN:
		return data.GetPlan().GetName()
	case managerpb.ApiExecutionDataType_INTERFACE_CASE:
		return data.GetInterfaceCase().GetName()
	case managerpb.ApiExecutionDataType_UI_CASE:
		name := data.GetUiCase().GetName()
		if name == "" {
			// disable ui case's name is empty
			return data.GetUiCase().GetPath()
		}
		return name
	case managerpb.ApiExecutionDataType_UI_SUITE:
		return data.GetUiSuite().GetName()
	case managerpb.ApiExecutionDataType_UI_PLAN:
		return data.GetUiPlan().GetName()
	case managerpb.ApiExecutionDataType_PERF_CASE:
		return data.GetPerfCase().GetName()
	case managerpb.ApiExecutionDataType_PERF_SUITE:
		return data.GetPerfSuite().GetName()
	case managerpb.ApiExecutionDataType_PERF_PLAN:
		return data.GetPerfPlan().GetName()
	case managerpb.ApiExecutionDataType_STABILITY_PLAN:
		return data.GetStabilityPlan().GetName()
	case managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT:
		return data.GetUiAgentComponent().GetName()
	default:
		return ""
	}
}

func (l *BasePublisher) getDeviceUDID(data *managerpb.ApiExecutionData) string {
	if data == nil {
		return ""
	}

	switch data.GetType() {
	case managerpb.ApiExecutionDataType_UI_CASE:
		return data.GetUiCase().GetUdid()
	default:
		return ""
	}
}

// GetRecordStatus 获取执行记录的状态
func (l *BasePublisher) GetRecordStatus(valid bool) (status pb.ComponentState) {
	status = pb.ComponentState_Pending
	if l.isStop {
		status = pb.ComponentState_Stop
	}

	if !valid {
		status = pb.ComponentState_Invalid
	}

	return status
}

// GetRecordTime 获取执行记录的时间
func (l *BasePublisher) GetRecordTime(status pb.ComponentState) (start, end int64) {
	start = time.Now().UnixMilli()
	if status != pb.ComponentState_Pending {
		end = start
	}
	return start, end
}

func (l *BasePublisher) getUUID() string {
	return fmt.Sprintf("%s::%s", l.taskId, l.executeId)
}

func Pb2ApiAccountConfig(configs []*commonpb.AccountConfig) []*commontypes.ApiAccountConfig {
	cs := make([]*commontypes.ApiAccountConfig, len(configs))
	for idx, item := range configs {
		cs[idx] = &commontypes.ApiAccountConfig{
			ProjectId:    item.GetProjectId(),
			ConfigId:     item.GetConfigId(),
			Name:         item.GetName(),
			Description:  item.GetDescription(),
			ProductType:  item.GetProductType(),
			ProductName:  item.GetProductName(),
			PoolEnvTable: item.GetPoolEnvTable(),
			PoolEnvName:  item.GetPoolEnvName(),
		}
	}
	return cs
}

func Pb2ApiGeneralConfig(config *commonpb.GeneralConfig) *commontypes.ApiGeneralConfig {
	c := &commontypes.ApiGeneralConfig{
		ProjectId:   config.GetProjectId(),
		ConfigId:    config.GetConfigId(),
		Name:        config.GetName(),
		Description: config.GetDescription(),
		BaseUrl:     config.GetBaseUrl(),
		Verify:      config.GetVerify(),
		Variables:   make([]commontypes.GeneralConfigVar, len(config.GetVariables())),
	}

	for idx, item := range config.GetVariables() {
		c.Variables[idx] = commontypes.GeneralConfigVar{
			Key:   item.GetKey(),
			Value: item.GetValue(),
		}
	}

	return c
}

type Publisher interface {
	Setup(in *pb.PublishReq) (err error)
	CreateRecord(in *pb.PublishReq) (err error)
	Publish(in *pb.PublishReq) (out *pb.PublishResp, err error)
	Panic(in *pb.PublishReq, err error)
}
