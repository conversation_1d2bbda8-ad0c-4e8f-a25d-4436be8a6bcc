package dispatcherlogic

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ComponentGroupPublisher struct {
	*BasePublisher
}

func NewComponentGroupPublisher(ctx context.Context, svcCtx *svc.ServiceContext) *ComponentGroupPublisher {
	return &ComponentGroupPublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (l *ComponentGroupPublisher) CreateRecord(in *pb.PublishReq) (err error) {
	_, err = l.svcCtx.ReporterRPC.Create(
		l.ctx, &reporterpb.PutRecordRequest{
			TaskId:                   l.taskId,
			ProjectId:                in.GetProjectId(),
			ExecuteId:                l.executeId,
			ExecuteType:              in.GetExecuteType().String(),
			GeneralConfig:            jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetComponentGroup().GetGeneralConfig())),
			AccountConfig:            jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetComponentGroup().GetAccountConfig())),
			ComponentId:              in.GetComponentGroup().GetComponentGroupId(),
			ComponentType:            managerpb.ApiExecutionDataType_API_COMPONENT_GROUP.String(),
			ComponentExecuteId:       l.executeId,
			ParentComponentId:        "",
			ParentComponentExecuteId: "",
			Version:                  in.GetComponentGroup().GetVersion(),
			Times:                    1,
			ExecutedBy:               in.GetUserId(),
			StartedAt:                time.Now().UnixMilli(),
			IsRoot:                   1,
		},
	)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}

	return nil
}

func (l *ComponentGroupPublisher) Publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	return l.publish(in)
}

func (l *ComponentGroupPublisher) publish(in *pb.PublishReq) (resp *pb.PublishResp, err error) {
	apiExecutionData, err := l.getApiExecutionData(
		&managerpb.GetApiExecutionDataReq{
			ProjectId: in.GetProjectId(),
			Type:      managerpb.ApiExecutionDataType_API_COMPONENT_GROUP,
			Id:        in.GetComponentGroup().GetComponentGroupId(),
			Version:   in.GetComponentGroup().GetVersion(),
		},
	)
	if err != nil {
		return resp, err
	}

	_, err = l.record(in, apiExecutionData)
	if err != nil {
		return resp, err
	}

	if l.isStop {
		return &pb.PublishResp{TaskId: l.taskId, ExecuteId: l.executeId}, nil
	}

	// 创建任务请求
	taskData := l.getWorkerReq(in, apiExecutionData)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "api任务参数序列化失败, error: %s", err,
		)
	}

	task := base.NewTask(
		constants.MQTaskTypeAPIWorkerComponentGroup,
		payload,
		base.WithRetentionOptions(time.Minute*2),
		base.WithMaxRetryOptions(0),
	)

	_, err = l.svcCtx.ApiWorkerProducer.Send(
		l.ctx, task, base.QueuePriorityDefault,
	)
	if err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.PublishResp{
		TaskId:    l.taskId,
		ExecuteId: l.executeId,
		Version:   l.getVersion(apiExecutionData),
	}, nil
}

func (l *ComponentGroupPublisher) getWorkerReq(in *pb.PublishReq, data *managerpb.ApiExecutionData) *pb.WorkerReq {
	return &pb.WorkerReq{
		TriggerMode:   in.GetTriggerMode(),
		TriggerRule:   in.GetTriggerRule(),
		ProjectId:     in.GetProjectId(),
		TaskId:        l.taskId,
		ExecuteId:     l.executeId,
		ExecuteType:   in.GetExecuteType(),
		WorkerType:    pb.WorkerType_WorkerType_API_COMPONENT_GROUP,
		GeneralConfig: in.GetComponentGroup().GetGeneralConfig(),
		AccountConfig: in.GetComponentGroup().GetAccountConfig(),
		UserId:        in.GetUserId(),
		NodeData:      data,
		Debug:         in.GetDebug(),
		Data: &pb.WorkerReq_ComponentGroup{
			ComponentGroup: &pb.ComponentGroupWorkerInfo{
				ComponentGroupId:        in.GetComponentGroup().GetComponentGroupId(),
				ComponentGroupExecuteId: l.executeId,
				Version:                 l.getVersion(data),
			},
		},
	}
}

func (l *ComponentGroupPublisher) record(
	in *pb.PublishReq, data *managerpb.ApiExecutionData,
) (resp *reporterpb.ModifyRecordResponse, err error) {
	status := l.GetRecordStatus(true)
	start, end := l.GetRecordTime(status)

	resp, err = l.svcCtx.ReporterRPC.Modify(
		l.ctx, &reporterpb.PutRecordRequest{
			TaskId:                   l.taskId,
			ProjectId:                in.GetProjectId(),
			ExecuteId:                l.executeId,
			ExecuteType:              in.GetExecuteType().String(),
			GeneralConfig:            jsonx.MarshalToStringIgnoreError(Pb2ApiGeneralConfig(in.GetComponentGroup().GetGeneralConfig())),
			AccountConfig:            jsonx.MarshalToStringIgnoreError(Pb2ApiAccountConfig(in.GetComponentGroup().GetAccountConfig())),
			ComponentId:              data.GetId(),
			ComponentType:            data.GetType().String(),
			ComponentName:            l.getComponentName(data),
			ComponentExecuteId:       l.executeId,
			ParentComponentId:        "",
			ParentComponentExecuteId: "",
			Version:                  l.getVersion(data),
			Times:                    1,
			Status:                   status.String(),
			ExecutedBy:               in.GetUserId(),
			StartedAt:                start,
			EndedAt:                  end,
			IsRoot:                   1,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}
	return resp, err
}

// Panic 异常处理
func (l *ComponentGroupPublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	// 直接更新状态
	_, _ = l.svcCtx.ReporterRPC.Modify(
		l.ctx, &reporterpb.PutRecordRequest{
			TaskId:             l.taskId,
			ProjectId:          in.GetProjectId(),
			ExecuteId:          l.executeId,
			ExecuteType:        in.GetExecuteType().String(),
			ComponentExecuteId: l.executeId,
			ComponentId:        in.GetComponentGroup().GetComponentGroupId(),
			ComponentType:      managerpb.ApiExecutionDataType_API_COMPONENT_GROUP.String(),
			Times:              1,
			Status:             pb.ComponentState_Panic.String(),
			Content:            jsonx.MarshalToStringIgnoreError(err.Error()),
			ExecutedBy:         in.GetUserId(),
			IsRoot:             1,
			EndedAt:            time.Now().UnixMilli(),
		},
	)
}
