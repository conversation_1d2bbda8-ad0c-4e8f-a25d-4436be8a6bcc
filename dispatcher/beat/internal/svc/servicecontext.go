package svc

import (
	"time"

	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/cronscheduler"
	commonredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/zrpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/model"

	rediscommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis/task"
)

type ServiceContext struct {
	Config config.Config

	Redis     *redis.Redis
	RedisNode red.UniversalClient

	DispatcherRpc *zrpc.DispatcherRpc
	ReporterRpc   *zrpc.ReporterRPC
	Scheduler     *cronscheduler.Scheduler

	TaskInfoProcessor   *task.InfoProcessor
	TaskInfoRecordModel model.TaskInfoRecordModel
}

func NewServiceContext(conf config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(conf.DB.DataSource)
	newScheduler := cronscheduler.NewScheduler()
	newScheduler.Scheduler.ChangeLocation(time.Local) // change to local timezone
	return &ServiceContext{
		Config: conf,

		Redis:     redis.MustNewRedis(conf.Redis),
		RedisNode: commonredis.NewClient(conf.Redis),

		DispatcherRpc: zrpc.NewDispatcherRpc(conf.Dispatcher),
		ReporterRpc:   zrpc.NewReporterRPC(conf.Reporter),
		Scheduler:     newScheduler,

		TaskInfoProcessor: task.NewTaskInfoProcessor(
			rediscommon.NewRCacheService(
				conf.Redis, rediscommon.DispatcherRedisKey,
			),
		),
		TaskInfoRecordModel: model.NewTaskInfoRecordModel(sqlConn, conf.Cache),
	}
}
