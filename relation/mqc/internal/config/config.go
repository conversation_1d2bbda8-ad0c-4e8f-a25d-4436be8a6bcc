package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/tlink"
)

type Config struct {
	service.ServiceConf

	Redis redis.RedisConf
	Cache cache.CacheConf
	DB    types.DBConfig

	Consumer consumerv2.Config

	TTServiceUrl string `json:",omitempty,optional"` // Deprecated: use `TLink` instead.
	TLink        tlink.Config

	DomainReplacements []Replacement `json:",omitempty,optional"`
}

type Replacement struct {
	From string
	To   string
}

func (c Config) LogConfig() logx.LogConf {
	return c.ServiceConf.Log
}

func (c Config) ListenOn() string {
	return constants.NoNeedToListen
}
