package relationservice

import (
	"context"
	"database/sql"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/jmespath/go-jmespath"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/tlink"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

var (
	clientTypeCompile = jmespath.MustCompile(clientTypeJMESPathExpression)

	errNoNeedToHandle = errors.New("no need to handle")

	systemUser = userinfo.System()
)

type BindCaseToServiceLogic struct {
	*BaseLogic
}

func NewBindCaseToServiceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BindCaseToServiceLogic {
	return &BindCaseToServiceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *BindCaseToServiceLogic) BindCaseToService(in *pb.BindCaseToServiceReq) (*pb.BindCaseToServiceResp, error) {
	method, err := l.getMethodFromReq(in)
	if err != nil {
		if !errors.Is(err, errNoNeedToHandle) {
			l.Errorf("failed to get method from request, req: %s, error: %+v", protobuf.MarshalJSONIgnoreError(in), err)
			return nil, err
		}

		return &pb.BindCaseToServiceResp{}, nil
	}

	relation, err := l.getServiceByMethod(method)
	if err != nil {
		if re, ok := errorx.RootError(err); ok && re.Code() == errorx.NotExists {
			return &pb.BindCaseToServiceResp{}, nil
		}

		return nil, err
	}

	services, err := l.getToServiceByMethod(relation.Service, relation.Type, relation.ReqPath)
	if err != nil {
		return nil, err
	} else if services.Size() == 0 {
		return &pb.BindCaseToServiceResp{}, nil
	}

	info := in.GetPrecisionInfo()
	relations, err := l.getServiceCaseRelations(
		info.GetProjectId(), info.GetGeneralConfigId(), relation.ReqPath, info.GetCaseId(),
	)
	if err != nil {
		return nil, err
	}

	l.bindCaseToService(info, relation, relations, services)
	return &pb.BindCaseToServiceResp{}, nil
}

type methodItem struct {
	Type    constants.MethodType
	Method  string // 用于查询`relation`的表
	ReqPath string // 用于查询`tlink`数据
}

func (x methodItem) String() string {
	return fmt.Sprintf("[%s - %s - %s]", x.Type, x.Method, x.ReqPath)
}

func (l *BindCaseToServiceLogic) getMethodFromReq(req *pb.BindCaseToServiceReq) (item methodItem, err error) {
	if !strings.Contains(req.GetRequestUrl(), ApiProxyCommonCallUrl) {
		return item, errNoNeedToHandle
	}

	info := req.GetPrecisionInfo()
	if info == nil {
		return item, errNoNeedToHandle
	}
	if info.GetProjectId() == "" || info.GetGeneralConfigId() == "" || info.GetCaseId() == "" {
		return item, errNoNeedToHandle
	}

	var (
		reqBody, respBody map[string]any
		clientType        string
	)
	if err = jsonx.UnmarshalFromString(req.GetRequestBody(), &reqBody); err != nil {
		return item, err
	}
	if err = jsonx.UnmarshalFromString(req.GetResponseBody(), &respBody); err != nil {
		return item, err
	}

	v, ok := reqBody[reqBodyKeyOfMethod]
	if !ok {
		return item, errNoNeedToHandle
	}

	method := cast.ToString(v)
	if http.IsHTTPMethod(method) {
		v, ok = reqBody[reqBodyKeyOfURL]
		if !ok {
			return item, errNoNeedToHandle
		}

		url_ := cast.ToString(v)
		u, err := url.ParseRequestURI(url_)
		if err != nil {
			return item, err
		}

		hostname := u.Hostname()
		for _, r := range l.svcCtx.Config.DomainReplacements {
			if strings.EqualFold(hostname, r.From) {
				hostname = r.To
				break
			}
		}

		firstPath := "/"
		if paths := strings.SplitN(u.Path, "/", 3); len(paths) >= 2 {
			firstPath += paths[1]
		}
		item = methodItem{
			Type:    constants.MethodTypeOfHTTP,
			Method:  firstPath,
			ReqPath: hostname + firstPath,
		}
	} else {
		item = methodItem{
			Type:    constants.MethodTypeOfGRPC,
			Method:  method,
			ReqPath: utils.GetGRPCFullMethodName(method),
		}
	}

	v, err = clientTypeCompile.Search(respBody)
	if err != nil {
		return item, err
	}
	clientType = cast.ToString(v)
	if !strings.EqualFold(clientType, clientTypeOfTT) && !strings.HasPrefix(clientType, clientTypeOfTT+"_") {
		return item, errNoNeedToHandle
	}

	return item, nil
}

func (l *BindCaseToServiceLogic) getServiceByMethod(method methodItem) (*model.ServiceMethodRelation, error) {
	records, err := l.svcCtx.ServiceMethodRelationModel.FindByMethod(l.ctx, method.Method)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find service method relation, method: %s, error: %+v",
			method, err,
		)
	} else if len(records) == 0 {
		record, err := l.getServiceFromTLink(method)
		if err != nil {
			l.Warnf("not found any service method relations, method: %s, error: %+v", method, err)
			return nil, errorx.Errorf(
				errorx.NotExists,
				"not found any service method relations, method: %s, error: %+v",
				method, err,
			)
		}

		return record, nil
	} else if len(records) > 1 {
		l.Warnf(
			"got more than one service method relation, method: %s, relations: %s",
			method, jsonx.MarshalIgnoreError(records),
		)
	}

	return records[0], nil
}

func (l *BindCaseToServiceLogic) getServiceFromTLink(method methodItem) (*model.ServiceMethodRelation, error) {
	entity, err := l.svcCtx.TLinkClient.QueryServiceByMethod(method.Type, method.ReqPath)
	if err != nil {
		return nil, err
	}

	var (
		service   = entity.Name
		namespace = entity.Properties.Namespace

		now = time.Now()
	)
	r, err := l.svcCtx.ServiceMethodRelationModel.FindOneByServiceNamespaceMethod(
		l.ctx, service, namespace, method.Method,
	)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		l.Errorf(
			"failed to find the service method relation, service: %s, namespace: %s, method: %s, error: %+v",
			service, namespace, method, err,
		)
		return nil, err
	} else if errors.Is(err, model.ErrNotFound) {
		r = &model.ServiceMethodRelation{
			Service:   service,
			Namespace: namespace,
			Type:      string(method.Type),
			Method:    method.Method,
			ReqPath:   method.ReqPath,
			CreatedBy: systemUser.Account,
			UpdatedBy: systemUser.Account,
			CreatedAt: now,
			UpdatedAt: now,
		}

		_, err = l.svcCtx.ServiceMethodRelationModel.InsertTX(l.ctx, nil, r)
	} else if r != nil {
		r.ReqPath = method.ReqPath
		r.UpdatedBy = systemUser.Account
		r.UpdatedAt = now

		_, err = l.svcCtx.ServiceMethodRelationModel.UpdateTX(l.ctx, nil, r)
	}

	if err != nil {
		l.Errorf(
			"failed to save the service method relation, service: %s, namespace: %s, method: %s, error: %+v",
			service, namespace, method, err,
		)
		return nil, err
	}

	return r, nil
}

func (l *BindCaseToServiceLogic) getToServiceByMethod(service, methodType, method string) (set.Set[string], error) {
	var (
		key, value string
		entities   []*tlink.Entity
		services   set.Set[string]
		err        error
	)

	key = fmt.Sprintf("%s%s:%s:%s", cacheKeyPrefixOfServiceAndMethod, service, methodType, method)
	defer func() {
		if err == nil && len(entities) > 0 {
			if _, e := l.svcCtx.Redis.SetnxExCtx(
				l.ctx, key, jsonx.MarshalToStringIgnoreError(entities), cacheExpireSeconds,
			); e != nil {
				l.Errorf(
					"failed to set cache, key: %s, value: %s, error: %+v",
					key, jsonx.MarshalIgnoreError(entities), err,
				)
			}
		}
	}()

	value, err = l.svcCtx.Redis.GetCtx(l.ctx, key)
	if err == nil && value != "" {
		if err = jsonx.UnmarshalFromString(value, &entities); err != nil {
			l.Errorf("failed to unmarshal the cache value, key: %s, value: %s, error: %+v", key, value, err)
			entities = []*tlink.Entity{}
		} else {
			l.Debugf("hit the cache value, key: %s, value: %s", key, value)
		}
	}

	if len(entities) == 0 {
		entities, err = l.svcCtx.TLinkClient.QueryToServicesByMethod(service, constants.MethodType(methodType), method)
		if err != nil {
			return services, err
		}
	}

	services = set.NewHashset(uint64(len(entities)+1), generic.Equals[string], generic.HashString)
	services.Put(service) // including self
	for _, entity := range entities {
		if entity.Label != string(tlink.EntityTypeOfWorkload) || entity.Name == "" {
			continue
		}

		services.Put(entity.Name)
	}

	return services, nil
}

func (l *BindCaseToServiceLogic) getServiceCaseRelations(projectID, generalConfigID, reqPath, caseID string) (
	*hashmap.Map[string, *model.ServiceCaseRelationV2], error,
) {
	records, err := l.svcCtx.ServiceCaseRelationV2Model.FindByReqPathAndCaseID(
		l.ctx, projectID, generalConfigID, reqPath, caseID,
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find service case relation, project_id: %s, general_config_id: %s, req_path: %s, case_id: %s, error: %+v",
			projectID, generalConfigID, reqPath, caseID, err,
		)
	}

	cache := hashmap.New[string, *model.ServiceCaseRelationV2](
		uint64(len(records)), generic.Equals[string], generic.HashString,
	)
	for _, record := range records {
		cache.Put(record.ToService, record)
	}

	return cache, nil
}

func (l *BindCaseToServiceLogic) bindCaseToService(
	info *pb.PrecisionInfo, service *model.ServiceMethodRelation,
	from *hashmap.Map[string, *model.ServiceCaseRelationV2], to set.Set[string],
) {
	var (
		user = userinfo.System()
		now  = time.Now()
	)

	_ = mr.MapReduceVoid[*model.ServiceCaseRelationV2, any](
		func(source chan<- *model.ServiceCaseRelationV2) {
			to.Each(
				func(key string) {
					value, ok := from.Get(key)
					if !ok {
						// 不存在则新增
						source <- &model.ServiceCaseRelationV2{
							ProjectId:       info.GetProjectId(),
							GeneralConfigId: info.GetGeneralConfigId(),
							Service:         service.Service,
							Namespace:       service.Namespace,
							Method:          service.Method,
							ReqPath:         service.ReqPath,
							Cmd:             service.Cmd,
							ToService:       key,
							DocumentId: sql.NullString{
								String: info.GetDocumentId(),
								Valid:  info.GetDocumentId() != "",
							},
							CaseId:    info.GetCaseId(),
							CreatedBy: user.Account,
							UpdatedBy: user.Account,
							CreatedAt: now,
							UpdatedAt: now,
						}
					} else {
						// 存在则更新
						value.Service = service.Service
						value.Namespace = service.Namespace
						value.Cmd = service.Cmd
						value.DocumentId = sql.NullString{
							String: info.GetDocumentId(),
							Valid:  info.GetDocumentId() != "",
						}
						value.UpdatedBy = user.Account
						value.UpdatedAt = now
						source <- value

						from.Remove(key)
					}
				},
			)
		}, func(item *model.ServiceCaseRelationV2, writer mr.Writer[any], cancel func(error)) {
			if item == nil {
				return
			}

			var err error
			if item.Id == 0 {
				_, err = l.svcCtx.ServiceCaseRelationV2Model.InsertTX(l.ctx, nil, item)
			} else {
				_, err = l.svcCtx.ServiceCaseRelationV2Model.UpdateTX(l.ctx, nil, item)
			}

			if err != nil {
				l.Errorf(
					"failed to save the service case relation, item: %s, error: %+v",
					jsonx.MarshalIgnoreError(item), err,
				)
			}
		}, func(pipe <-chan any, cancel func(error)) {
		}, mr.WithContext(l.ctx), mr.WithWorkers(mrWorkers),
	)

	from.Each(
		func(key string, val *model.ServiceCaseRelationV2) {
			if err := l.svcCtx.ServiceCaseRelationV2Model.Delete(l.ctx, nil, val.Id); err != nil {
				l.Errorf(
					"failed to delete the service case relation, item: %s, error: %+v",
					jsonx.MarshalIgnoreError(val), err,
				)
			}
		},
	)
}
