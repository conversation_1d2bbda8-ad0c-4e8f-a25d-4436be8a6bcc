package config

import (
	"github.com/alicebob/miniredis/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/clickPilot"
)

func NewMockConfig(s *miniredis.Miniredis) Config {
	addr := s.Addr()

	return Config{
		ServiceConf: service.ServiceConf{
			Name: "uaworker",
			Log: logx.LogConf{
				ServiceName: "mqc.uaworker",
				Encoding:    "plain",
				Level:       "info",
				Stat:        false,
			},
		},

		Redis: redis.RedisKeyConf{
			RedisConf: redis.RedisConf{
				Host: addr,
				Type: redis.NodeType,
				DB:   24,
			},
			Key: "mqc.uaworker",
		},
		DispatcherRedis: redis.RedisConf{
			Host: addr,
			Type: redis.NodeType,
			DB:   4,
		},

		Reporter: zrpc.RpcClientConf{
			Endpoints: []string{"127.0.0.1:20511"},
			NonBlock:  true,
			Timeout:   0,
		},

		UIAgentWorkerConsumer: consumer.Config{
			Broker:      addr,
			Backend:     addr,
			Queue:       "mqc:uaworker",
			ConsumerTag: "mqc:uaworker",
			Db:          20,
		},
		ManagerProducer: producer.Config{
			Broker:  addr,
			Backend: addr,
			Queue:   "mqc:manager",
			Db:      20,
		},
		ReporterProducer: producer.Config{
			Broker:  addr,
			Backend: addr,
			Queue:   "mqc:reporter",
			Db:      20,
		},

		ClickPilot: clickPilot.Config{
			BaseURL: "http://127.0.0.1:8000",
		},

		LocalPath: ".",
	}
}
