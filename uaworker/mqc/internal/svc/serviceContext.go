package svc

import (
	"github.com/electricbubble/gadb"
	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"

	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/clickPilot"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/common/zrpc/discovery"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/common/zrpc/reporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/mqc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	Redis           *redis.Redis
	RedisNode       red.UniversalClient
	DispatcherRedis red.UniversalClient

	ADBClient *gadb.Client

	DiscoveryRPC discovery.IClient // 注意：为`*discovery.NoopClient`时，则通过远程方式安装App
	ReporterRPC  reporter.IClient

	UIAgentWorkerConsumer *consumer.Consumer
	ManagerProducer       *producer.Producer
	ReporterProducer      *producer.Producer

	ClickPilotClient clickPilot.IClient

	ExitChannel chan lang.PlaceholderType // 接收退出信号
}

func NewServiceContext(c config.Config) *ServiceContext {
	rdb := qetredis.NewClient(c.Redis.RedisConf)

	return &ServiceContext{
		Config: c,

		Redis:           redis.MustNewRedis(c.Redis.RedisConf, redis.WithDB(c.Redis.RedisConf.DB)),
		RedisNode:       rdb,
		DispatcherRedis: qetredis.NewClient(c.DispatcherRedis),

		ADBClient: mustNewADBClient(c.ADB),

		DiscoveryRPC: discovery.NewRPCClient(c.Discovery),
		ReporterRPC:  reporter.NewRPCClient(c.Reporter),

		UIAgentWorkerConsumer: consumer.NewConsumer(c.UIAgentWorkerConsumer),
		ManagerProducer:       producer.NewProducer(c.ManagerProducer),
		ReporterProducer:      producer.NewProducer(c.ReporterProducer),

		ClickPilotClient: clickPilot.NewClient(c.ClickPilot),

		ExitChannel: make(chan lang.PlaceholderType),
	}
}

func mustNewADBClient(c gadb.Config) *gadb.Client {
	client, err := gadb.NewClientWithConfig(c)
	logx.Must(err)
	return client
}
