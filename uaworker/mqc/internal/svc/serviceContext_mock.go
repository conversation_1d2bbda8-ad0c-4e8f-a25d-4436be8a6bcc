package svc

import (
	"github.com/alicebob/miniredis/v2"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/clickPilot"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/common/zrpc/discovery"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/common/zrpc/reporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/mqc/internal/config"
)

func NewMockServiceContext(s *miniredis.Miniredis) *ServiceContext {
	c := config.NewMockConfig(s)
	rdb := qetredis.NewClient(c.Redis.RedisConf)

	return &ServiceContext{
		Config: c,

		Redis:           redis.MustNewRedis(c.Redis.RedisConf, redis.WithDB(c.Redis.RedisConf.DB)),
		RedisNode:       rdb,
		DispatcherRedis: qetredis.NewClient(c.DispatcherRedis),

		DiscoveryRPC: &discovery.NoopClient{},
		ReporterRPC:  reporter.NewMockClient(),

		UIAgentWorkerConsumer: consumer.NewConsumer(c.UIAgentWorkerConsumer),
		ManagerProducer:       producer.NewProducer(c.ManagerProducer),
		ReporterProducer:      producer.NewProducer(c.ReporterProducer),

		ClickPilotClient: clickPilot.NewMockClient(c.ClickPilot),

		ExitChannel: make(chan lang.PlaceholderType),
	}
}
