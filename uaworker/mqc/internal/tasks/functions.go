package tasks

import (
	"context"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/common"
)

func updateContext(ctx context.Context, taskID, executeID, executedBy string) context.Context {
	ctx = logx.ContextWithFields(
		ctx,
		logx.Field(common.ConstLogFieldKeyOfTaskID, taskID),
		logx.Field(common.ConstLogFieldKeyOfExecuteID, executeID),
	)
	if v := userinfo.FromContext(ctx); v == nil {
		logx.WithContext(ctx).Infof(
			"userinfo has not been retained in the context, set `executed_by` to the context, executed_by: %s",
			executedBy,
		)
		ctx = userinfo.WithContext(
			ctx, &userinfo.UserInfo{
				Account: executedBy,
			},
		)
	} else {
		logx.WithContext(ctx).Infof(
			"userinfo has been stored in the context, userinfo: %s, executed_by: %s",
			jsonx.MarshalIgnoreError(v), executedBy,
		)
	}

	return ctx
}
