package tasks

import (
	"context"
	"os"
	"path/filepath"
	"reflect"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/mqc/internal/svc"
)

func TestUIAgentComponentTaskProcessor_ProcessTask(t *testing.T) {
	if basePath, err := filepath.Abs("../../../.."); err == nil {
		_ = os.Chdir(basePath)
	}
	currentPath, _ := os.Getwd()
	t.Logf("PWD: %s", currentPath)

	logx.SetLevel(logx.DebugLevel)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	s := miniredis.RunT(t)
	svcCtx := svc.NewMockServiceContext(s)
	proc.AddShutdownListener(
		func() {
			close(svcCtx.ExitChannel)
		},
	)
	threading.GoSafe(
		func() {
			for {
				select {
				case <-svcCtx.ExitChannel:
					t.Log("got an exit signal")
					return
				case <-ctx.Done():
					t.Logf("got a done signal, error: %+v", ctx.Err())
					return
				}
			}
		},
	)

	projectID := "project_id:Kqllt5-9fA-I5UOdhjA5d"
	componentID := "ui_agent_component_id:1"
	processor := NewUIAgentComponentTaskProcessor(svcCtx)

	type args struct {
		name string
		info *dispatcherpb.WorkerReq
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
	}{
		{
			name: "execute ui agent component task",
			args: args{
				name: constants.MQTaskTypeUIAgentWorkerExecuteComponentTask,
				info: &dispatcherpb.WorkerReq{
					TriggerMode: commonpb.TriggerMode_MANUAL,
					ProjectId:   projectID,
					TaskId:      "task_id:1",
					ExecuteId:   "execute_id:11",
					ExecuteType: managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT,
					WorkerType:  dispatcherpb.WorkerType_WorkerType_UI_AGENT_COMPONENT,
					UserId:      "T1704",
					NodeData: &managerpb.ApiExecutionData{
						Id:   componentID,
						Type: managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT,
						Data: &managerpb.ApiExecutionData_UiAgentComponent{
							UiAgentComponent: &managerpb.UIAgentComponentComponent{
								ProjectId:   projectID,
								ComponentId: componentID,
								Name:        "修改生日",
								Description: "",
								ApplicationConfig: &commonpb.ApplicationConfig{
									ProjectId:       projectID,
									ConfigId:        "application_config_id:YBmuq97ETHu2YtVuM1jYb",
									Name:            "TT语音 - Android",
									Description:     "",
									PlatformType:    commonpb.PlatformType_ANDROID,
									AppId:           "com.yiyou.ga",
									AppDownloadLink: "",
									Prompts: []*commonpb.PromptConfig{
										{
											ProjectId:   projectID,
											ConfigId:    "prompt_config_id:KknVisF2-mXzPapKeRwDe",
											Purpose:     commonpb.PromptPurpose_PromptPurpose_UI_AGENT,
											Category:    commonpb.PromptCategory_PromptCategory_BACKGROUND,
											Name:        "TT语音",
											Description: "",
											Content:     "TT语音，以游戏互动为主的兴趣社交平台，超1亿年轻人都在用！\n为你提供组队开黑、线上K歌、玩伴扩列等时下流行的社交组局玩法~\n上TT，随时有玩伴！\n【组局开黑】\n快速组局，集结开黑小队\n精准匹配默契队友，上分更轻松~\n【电子竞技】\nLPL/PEL等六大头部电竞赛事官方合作伙伴\n拥有TTG王者荣耀分部、TT英雄联盟分部、TT和平精英分部等多支专业战队~\n【音乐互动】\n一起K歌，欢乐接唱\n还有超炸的说唱live，随时都有同频音乐玩伴等你上线~\n【赛事玩法】\n丰富的赛事玩法，畅享多人实时互动\n搜索「赏金赛」玩王者吃鸡轻松赢赏金，免费参赛，赏金可提取～\n【多人团战房】\n「团战房间」上线，闪电召集\n一呼百应，更多麦位和工具助战队决胜千里\n【娱乐听听】\n扩列交友、达人点唱、剧场PIA戏\n超多有魅力有才华的声音玩伴,等你来撩~",
										},
										{
											ProjectId:   projectID,
											ConfigId:    "prompt_config_id:hiAYeSnyGxtOax7x1ma_p",
											Purpose:     commonpb.PromptPurpose_PromptPurpose_UI_AGENT,
											Category:    commonpb.PromptCategory_PromptCategory_EXCEPTION_HANDLING,
											Name:        "TT语音",
											Description: "",
											Content:     "如果出现广告、通知等信息遮挡页面时，有倒计时的等待倒计时结束，有关闭按钮的点击关闭按钮",
										},
									},
								},
								Mode: commonpb.UIAgentMode_UIAgentMode_AGENT,
								Steps: []*commonpb.UIAgentComponentStep{
									{
										Content: "点击我tab",
										Expectation: &commonpb.UIAgentComponentExpectation{
											Text:  "进去个人中心",
											Image: "",
										},
									},
									{
										Content: "点击编辑资料",
										Expectation: &commonpb.UIAgentComponentExpectation{
											Text:  "进入资料编辑页面，显示生日信息",
											Image: "",
										},
									},
									{
										Content: "点击生日",
										Expectation: &commonpb.UIAgentComponentExpectation{
											Text:  "弹出生日修改窗口",
											Image: "",
										},
									},
									{
										Content: "生日修改成{{.year}}",
										Expectation: &commonpb.UIAgentComponentExpectation{
											Text:  "弹出生日修改窗口",
											Image: "",
										},
									},
								},
								Expectation: &commonpb.UIAgentComponentExpectation{
									Text:  "成功修改我的生日，我的资料页下拉刷新可看到最新消息",
									Image: "",
								},
								Variables: []*commonpb.GeneralConfigVar{
									{
										Key:   "year",
										Value: "1995",
									},
								},
								State:        managerpb.CommonState_CS_ENABLE,
								MaintainedBy: "T1704",
								CreatedBy:    "T1704",
								UpdatedBy:    "T1704",
							},
						},
					},
					Data: &dispatcherpb.WorkerReq_UiAgentComponent{
						UiAgentComponent: &dispatcherpb.UIAgentComponentWorkerInfo{
							UiAgentComponentId:        componentID,
							UiAgentComponentExecuteId: "execute_id:11",
							Device: &commonpb.UIAgentDevice{
								Device: &commonpb.UIAgentDevice_UserDevice_{
									UserDevice: &commonpb.UIAgentDevice_UserDevice{
										DeviceType:    commonpb.DeviceType_REAL_PHONE,
										PlatformType:  commonpb.PlatformType_ANDROID,
										Udid:          "PXUYD22628002359",
										RemoteAddress: "************:20004",
									},
								},
							},
							Reinstall: false,
							Restart:   true,
						},
					},
					Debug: false,
				},
			},
			want:    []byte(constants.SUCCESS),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := processor.ProcessTask(
					ctx, base.NewTask(
						tt.args.name,
						protobuf.MarshalJSONIgnoreError(tt.args.info),
					),
				)
				if (err != nil) != tt.wantErr {
					t.Errorf("ProcessTask() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("ProcessTask() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
