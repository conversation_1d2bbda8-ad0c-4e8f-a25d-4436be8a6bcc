package internal

import (
	"os"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uaworker/mqc/internal/tasks"
)

func HandleSetupOperations(svcCtx *svc.ServiceContext) error {
	// 启动`adb`服务
	if err := startADBServer(svcCtx); err != nil {
		return err
	}

	// 注册任务
	return registerTasks(svcCtx)
}

func startADBServer(svcCtx *svc.ServiceContext) error {
	logx.Infof("`PATH`: %s", os.Getenv("PATH"))

	if err := svcCtx.ADBClient.StartServer(); err != nil {
		return errors.Errorf("failed to start adb server, error: %+v", err)
	}
	if version, err := svcCtx.ADBClient.ServerVersion(); err != nil {
		logx.Errorf("failed to get adb server version, error: %+v", err)
	} else {
		logx.Infof("succeed to start adb server, version: %d", version)
	}

	proc.AddShutdownListener(
		func() {
			if svcCtx != nil && svcCtx.ADBClient != nil {
				_ = svcCtx.ADBClient.KillServer()
			}
		},
	)

	return nil
}

func registerTasks(svcCtx *svc.ServiceContext) error {
	return svcCtx.UIAgentWorkerConsumer.RegisterHandlers(
		consumer.NewTaskHandlerOjb(
			constants.MQTaskTypeUIAgentWorkerExecuteComponentTask,
			tasks.NewUIAgentComponentTaskProcessor(svcCtx),
		),
	)
}
