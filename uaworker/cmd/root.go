package cmd

import (
	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/command"
)

const (
	rootCmdUse   = "uaworker"
	rootCmdShort = "uaworker is one of the microservices of the Quality Platform"
	rootCmdLong  = `uaworker is one of the microservices of the Quality Platform.
The main function is to execute the UI Agent testing tasks.`
)

func NewRootCommand() *cobra.Command {
	return command.NewRootCommand(rootCmdUse, rootCmdShort, rootCmdLong)
}
