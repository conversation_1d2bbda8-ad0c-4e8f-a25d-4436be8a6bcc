package reporter

import (
	"context"

	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type IClient interface {
	CreateUIAgentComponentRecord(
		ctx context.Context, in *pb.CreateUIAgentComponentRecordReq, opts ...grpc.CallOption,
	) (*pb.CreateUIAgentComponentRecordResp, error)
	ModifyUIAgentComponentRecord(
		ctx context.Context, in *pb.ModifyUIAgentComponentRecordReq, opts ...grpc.CallOption,
	) (*pb.ModifyUIAgentComponentRecordResp, error)
}
