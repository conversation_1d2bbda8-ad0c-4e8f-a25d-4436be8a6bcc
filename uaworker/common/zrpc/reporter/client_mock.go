package reporter

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

var _ IClient = (*mockClient)(nil)

type mockClient struct{}

func NewMockClient() IClient {
	return &mockClient{}
}

func (c *mockClient) CreateUIAgentComponentRecord(
	ctx context.Context, in *pb.CreateUIAgentComponentRecordReq, opts ...grpc.CallOption,
) (*pb.CreateUIAgentComponentRecordResp, error) {
	logx.WithContext(ctx).Infof("mockClient CreateUIAgentComponentRecord: %s", protobuf.MarshalJSONIgnoreError(in))
	return &pb.CreateUIAgentComponentRecordResp{}, nil
}

func (c *mockClient) ModifyUIAgentComponentRecord(
	ctx context.Context, in *pb.ModifyUIAgentComponentRecordReq, opts ...grpc.CallOption,
) (*pb.ModifyUIAgentComponentRecordResp, error) {
	logx.WithContext(ctx).Infof("mockClient ModifyUIAgentComponentRecord: %s", protobuf.MarshalJSONIgnoreError(in))
	return &pb.ModifyUIAgentComponentRecordResp{}, nil
}
