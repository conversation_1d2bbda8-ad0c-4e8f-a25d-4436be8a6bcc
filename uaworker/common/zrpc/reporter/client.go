package reporter

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/uiagentreporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

var _ IClient = (*RPCClient)(nil)

type RPCClient struct {
	conf zrpc.RpcClientConf

	client uiagentreporter.UIAgentReporter
}

func NewRPCClient(conf zrpc.RpcClientConf) IClient {
	return &RPCClient{
		conf: conf,

		client: uiagentreporter.NewUIAgentReporter(
			zrpc.MustNewClient(
				conf, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}

func (c *RPCClient) CreateUIAgentComponentRecord(
	ctx context.Context, in *pb.CreateUIAgentComponentRecordReq, opts ...grpc.CallOption,
) (*pb.CreateUIAgentComponentRecordResp, error) {
	return c.client.CreateUIAgentComponentRecord(ctx, in, opts...)
}

func (c *RPCClient) ModifyUIAgentComponentRecord(
	ctx context.Context, in *pb.ModifyUIAgentComponentRecordReq, opts ...grpc.CallOption,
) (*pb.ModifyUIAgentComponentRecordResp, error) {
	return c.client.ModifyUIAgentComponentRecord(ctx, in, opts...)
}

func (c *RPCClient) SearchUIAgentComponentRecord(
	ctx context.Context, in *pb.SearchUIAgentComponentRecordReq, opts ...grpc.CallOption,
) (*pb.SearchUIAgentComponentRecordResp, error) {
	return c.client.SearchUIAgentComponentRecord(ctx, in, opts...)
}

func (c *RPCClient) GetUIAgentComponentRecord(
	ctx context.Context, in *pb.GetUIAgentComponentRecordReq, opts ...grpc.CallOption,
) (*pb.GetUIAgentComponentRecordResp, error) {
	return c.client.GetUIAgentComponentRecord(ctx, in, opts...)
}
