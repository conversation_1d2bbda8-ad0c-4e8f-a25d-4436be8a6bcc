package discovery

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/client/discoveryservice"
	discoverypb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/rpc/pb"
)

var (
	_ IClient = (*RPCClient)(nil)
	_ IClient = (*NoopClient)(nil)
)

type RPCClient struct {
	logx.Logger
	ctx context.Context

	conf   zrpc.RpcClientConf
	target string

	discovery discoveryservice.DiscoveryService
}

func NewRPCClient(c zrpc.RpcClientConf) IClient {
	target, err := getTarget(c)
	if err != nil {
		return &NoopClient{}
	}

	ctx := context.Background()
	client := &RPCClient{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,

		conf:   c,
		target: target,

		discovery: discoveryservice.NewDiscoveryService(
			zrpc.MustNewClient(
				c, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
	if err = client.HealthCheck(); err != nil {
		client.Errorf("discovery service health check failed: %s", err.Error())
	} else {
		client.Infof("discovery service health check passed")
	}

	return client
}

func getTarget(c zrpc.RpcClientConf) (string, error) {
	if len(c.Endpoints) > 0 {
		return c.Endpoints[0], nil
	} else if len(c.Target) > 0 {
		return c.Target, nil
	}

	return "", errors.New("no target found")
}

func (c *RPCClient) HealthCheck() error {
	host, _ := splitHostPort(c.target)

	ctx, cancel := context.WithTimeout(c.ctx, 3*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, fmt.Sprintf("http://%s:21531/healthz", host), nil)
	if err != nil {
		return err
	}
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	defer func() {
		if resp != nil && resp.Body != nil {
			_ = resp.Body.Close()
		}
	}()

	if resp.StatusCode != http.StatusOK {
		return errors.Errorf("unexpected status code[%d]", resp.StatusCode)
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return errors.Errorf("read body err[%s]", err.Error())
	} else if string(body) != "OK" {
		return errors.Errorf("unexpected response body[%s]", string(body))
	}

	return nil
}

// splitHostPort separates host and port. If the port is not valid, it returns
// the entire input as host, and it doesn't check the validity of the host.
// Unlike net.SplitHostPort, but per RFC 3986, it requires ports to be numeric.
func splitHostPort(hostPort string) (host, port string) {
	host = hostPort

	colon := strings.LastIndexByte(host, ':')
	if colon != -1 && validOptionalPort(host[colon:]) {
		host, port = host[:colon], host[colon+1:]
	}

	if strings.HasPrefix(host, "[") && strings.HasSuffix(host, "]") {
		host = host[1 : len(host)-1]
	}

	return
}

// validOptionalPort reports whether port is either an empty string
// or matches /^:\d*$/
func validOptionalPort(port string) bool {
	if port == "" {
		return true
	}
	if port[0] != ':' {
		return false
	}
	for _, b := range port[1:] {
		if b < '0' || b > '9' {
			return false
		}
	}
	return true
}

func (c *RPCClient) IsDiscovery() bool {
	return true
}

func (c *RPCClient) DownloadApp(
	ctx context.Context, in *discoverypb.DownloadAppReq, opts ...grpc.CallOption,
) (*discoverypb.DownloadAppResp, error) {
	return c.discovery.DownloadApp(ctx, in, opts...)
}

func (c *RPCClient) InstallApp(
	ctx context.Context, in *discoverypb.InstallAppReq, opts ...grpc.CallOption,
) (*discoverypb.InstallAppResp, error) {
	return c.discovery.InstallApp(ctx, in, opts...)
}

func (c *RPCClient) RemoveApp(
	ctx context.Context, in *discoverypb.RemoveAppReq, opts ...grpc.CallOption,
) (*discoverypb.RemoveAppResp, error) {
	return c.discovery.RemoveApp(ctx, in, opts...)
}

type NoopClient struct{}

func (c *NoopClient) HealthCheck() error {
	return nil
}

func (c *NoopClient) IsDiscovery() bool {
	return false
}

func (c *NoopClient) DownloadApp(
	ctx context.Context, in *discoverypb.DownloadAppReq, opts ...grpc.CallOption,
) (*discoverypb.DownloadAppResp, error) {
	return &discoverypb.DownloadAppResp{}, nil
}

func (c *NoopClient) InstallApp(
	ctx context.Context, in *discoverypb.InstallAppReq, opts ...grpc.CallOption,
) (*discoverypb.InstallAppResp, error) {
	return &discoverypb.InstallAppResp{}, nil
}

func (c *NoopClient) RemoveApp(
	ctx context.Context, in *discoverypb.RemoveAppReq, opts ...grpc.CallOption,
) (*discoverypb.RemoveAppResp, error) {
	return &discoverypb.RemoveAppResp{}, nil
}
