package common

import "time"

const (
	ConstLogFieldKeyOfTaskID    = "task_id"
	ConstLogFieldKeyOfExecuteID = "execute_id"

	ConstIntervalOfWatchStopSignal = 5 * time.Second
	ConstTimeoutOfSendTask         = 2 * time.Second
	ConstTimeoutOfInvokeRPC        = 5 * time.Second
	ConstTimeoutOfDownloadApp      = 5 * time.Minute
	ConstTimeoutOfInstallApp       = 3 * time.Minute
	ConstTimeoutOfExecuteTask      = 10 * time.Minute
	ConstIntervalOfCheckTaskStatus = 5 * time.Second
)
