package common

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/errmsg"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

const (
	ErrZHMsgOfNullTaskInfo           errmsg.ErrZHMsg = "内部错误，任务信息为空"
	ErrZHMsgOfValidateTaskInfoFailed errmsg.ErrZHMsg = "内部错误，任务信息校验失败"
	ErrZHMsgOfNullAppConfig          errmsg.ErrZHMsg = "内部错误，应用配置为空"
	ErrZHMsgOfNullDeviceInfo         errmsg.ErrZHMsg = "内部错误，设备信息为空"
	ErrZHMsgOfInvalidDeviceType      errmsg.ErrZHMsg = "内部错误，无效的设备类型"
	ErrZHMsgOfInvalidPlatformType    errmsg.ErrZHMsg = "内部错误，无效的平台类型"
	ErrZHMsgOfExecuteTaskPanic       errmsg.ErrZHMsg = "内部错误，执行任务时遇到异常"

	ErrZHMsgOfInitDeviceFailed           errmsg.ErrZHMsg = "初始化设备失败，如：连接设备失败、获取设备信息失败等等"
	ErrZHMsgOfDeviceUnInitialized        errmsg.ErrZHMsg = "设备未初始化"
	ErrZHMsgOfDownloadAppFailed          errmsg.ErrZHMsg = "下载应用失败"
	ErrZHMsgOfGetAppInfoFailed           errmsg.ErrZHMsg = "获取应用信息失败"
	ErrZHMsgOfInstallAppTimeout          errmsg.ErrZHMsg = "安装应用超时"
	ErrZHMsgOfInstallAppFailed           errmsg.ErrZHMsg = "安装应用失败"
	ErrZHMsgOfExpectationImageNotFound   errmsg.ErrZHMsg = "期望结果图片不存在"
	ErrZHMsgOfCreateUIAgentTaskFailed    errmsg.ErrZHMsg = "创建`UI Agent`任务失败"
	ErrZHMsgOfCheckUIAgentTaskFailed     errmsg.ErrZHMsg = "检查`UI Agent`任务失败"
	ErrZHMsgOfExecuteUIAgentTaskFailed   errmsg.ErrZHMsg = "执行`UI Agent`任务失败"
	ErrZHMsgOfExecuteUIAgentTaskTimeout  errmsg.ErrZHMsg = "执行`UI Agent`任务超时"
	ErrZHMsgOfTerminateUIAgentTaskFailed errmsg.ErrZHMsg = "终止`UI Agent`任务失败"
	ErrZHMsgOfTerminateExecutingTask     errmsg.ErrZHMsg = "终止执行任务"
	ErrZHMsgOfExecuteTaskTimeout         errmsg.ErrZHMsg = "执行任务超时"
)

func init() {
	errmsg.RegisterErrZHMsg(
		map[errmsg.ErrZHMsg]errmsg.ErrCode{
			// 系统错误
			ErrZHMsgOfNullTaskInfo:           errmsg.ErrCode(dispatcherpb.ComponentState_Panic),
			ErrZHMsgOfValidateTaskInfoFailed: errmsg.ErrCode(dispatcherpb.ComponentState_Panic),
			ErrZHMsgOfNullAppConfig:          errmsg.ErrCode(dispatcherpb.ComponentState_Panic),
			ErrZHMsgOfNullDeviceInfo:         errmsg.ErrCode(dispatcherpb.ComponentState_Panic),
			ErrZHMsgOfInvalidDeviceType:      errmsg.ErrCode(dispatcherpb.ComponentState_Panic),
			ErrZHMsgOfInvalidPlatformType:    errmsg.ErrCode(dispatcherpb.ComponentState_Panic),
			ErrZHMsgOfExecuteTaskPanic:       errmsg.ErrCode(dispatcherpb.ComponentState_Panic),

			// 业务错误
			ErrZHMsgOfInitDeviceFailed:           errmsg.ErrCode(dispatcherpb.ComponentState_Failure),
			ErrZHMsgOfDeviceUnInitialized:        errmsg.ErrCode(dispatcherpb.ComponentState_Failure),
			ErrZHMsgOfDownloadAppFailed:          errmsg.ErrCode(dispatcherpb.ComponentState_Failure),
			ErrZHMsgOfGetAppInfoFailed:           errmsg.ErrCode(dispatcherpb.ComponentState_Failure),
			ErrZHMsgOfInstallAppTimeout:          errmsg.ErrCode(dispatcherpb.ComponentState_Failure),
			ErrZHMsgOfInstallAppFailed:           errmsg.ErrCode(dispatcherpb.ComponentState_Failure),
			ErrZHMsgOfExpectationImageNotFound:   errmsg.ErrCode(dispatcherpb.ComponentState_Failure),
			ErrZHMsgOfCreateUIAgentTaskFailed:    errmsg.ErrCode(dispatcherpb.ComponentState_Failure),
			ErrZHMsgOfCheckUIAgentTaskFailed:     errmsg.ErrCode(dispatcherpb.ComponentState_Failure),
			ErrZHMsgOfExecuteUIAgentTaskFailed:   errmsg.ErrCode(dispatcherpb.ComponentState_Failure),
			ErrZHMsgOfExecuteUIAgentTaskTimeout:  errmsg.ErrCode(dispatcherpb.ComponentState_Failure),
			ErrZHMsgOfTerminateUIAgentTaskFailed: errmsg.ErrCode(dispatcherpb.ComponentState_Stop),
			ErrZHMsgOfTerminateExecutingTask:     errmsg.ErrCode(dispatcherpb.ComponentState_Stop),
			ErrZHMsgOfExecuteTaskTimeout:         errmsg.ErrCode(dispatcherpb.ComponentState_Failure),
		},
	)
}
