package constants

// 任务类型
const (
	MQTaskTypeAPIWorkerCase                = "case"                    // `apiworker` - 用例
	MQTaskTypeAPIWorkerComponentGroup      = "component_group"         // `apiworker` - 组件组
	MQTaskTypeAPIWorkerInterfaceCase       = "interface_case"          // `apiworker` - 接口用例
	MQTaskTypeUIWorkerExecuteUiTest        = "execute_ui_test"         // `uiworker` - 执行UI测试任务
	MQTaskTypeUIWorkerExecuteAndroidUiTest = "execute_android_ui_test" // `uiworker` - 执行Android UI测试任务
	MQTaskTypeUIWorkerExecuteIOSUiTest     = "execute_ios_ui_test"     // `uiworker` - 执行IOS UI测试任务
	MQTaskTypeUIWorkerGenerateReport       = "generate_report"         // `uiworker` - 生成UI测试报告
	MQTaskTypeUIWorkerGenerateReportResult = "generate_report_result"  // `uiworker` - 生成UI测试报告回调结果

	MQTaskTypePerfToolExecutePerfTest = "execute_perf_test" // `perftool` - 执行压力测试任务

	MQTaskTypePerfWorkerExecutePerfTest     = "execute_perf_task_test"    // `perfworker` - 执行压力测试任务
	MQTaskTypePerfWorkerFinalHandle         = "execute_perf_final_handle" // `perfworker` - 压测任务最终处理(清理压测数据、合并指标等)
	MQTaskTypePerfWorkerRemovePerfTestQueue = "remove_perf_test_queue"    // `perfworker` - 移除压测任务队列
	MQTaskTypePerfWorkerToolResultFeedback  = "perf_tool_result_feedback" // `perfworker` - 压测工具结果反馈

	MQTaskTypeStabilityWorkerExecutePlanTask = "execute_stability_plan_task" // `staworker` - 执行稳定性测试计划任务
	MQTaskTypeStabilityWorkerExecuteCaseTask = "execute_stability_case_task" // `statool` - 执行稳定性测试用例任务

	MQTaskTypeUIAgentWorkerExecuteComponentTask = "execute_ui_agent_component_task" // `uaworker` - 执行`UI Agent`组件任务

	MQTaskTypeDeviceHubHandleReleaseDevice = "handle_release_device" // `devicehub` - 处理释放设备

	MQTaskTypeDispatcherCallback                     = "callback"                         // `dispatcher` - 回调
	MQTaskTypeDispatcherPlan                         = "plan"                             // `dispatcher` - 计划
	MQTaskTypeDispatcherSuite                        = "suite"                            // `dispatcher` - 集合
	MQTaskTypeDispatcherPrecisionSuite               = "precision_suite"                  // Deprecated: `dispatcher` - 精准测试集合
	MQTaskTypeDispatcherPrecisionInterface           = "precision_interface"              // Deprecated: `dispatcher` - 精准测试接口
	MQTaskTypeDispatcherInterface                    = "interface"                        // `dispatcher` - 接口
	MQTaskTypeDispatcherService                      = "service"                          // `dispatcher` - 精准测试服务
	MQTaskTypeDispatcherUIPlan                       = "ui_plan"                          // `dispatcher` - UI测试计划
	MQTaskTypeDispatcherUISuite                      = "ui_suite"                         // `dispatcher` - UI测试集合
	MQTaskTypeDispatcherUIReport                     = "ui_export"                        // `dispatcher` - UI测试报告
	MQTaskTypeDispatcherPeriodicPlanTask             = "periodic_plan_task"               // `dispatcher` - 定时触发执行测试计划的任务
	MQTaskTypeDispatcherPublishTask                  = "publish_task"                     // `dispatcher` - 分发任务
	MQTaskTypeDispatcherPublishTaskCaseExecuting     = "publish_task_case_executing"      // `dispatcher` - 任务执行
	MQTaskTypeDispatcherPublishPerfTaskCaseExecuting = "publish_perf_task_case_executing" // `dispatcher` - 压力测试任务执行
	MQTaskTypeDispatcherPerfPlan                     = "perf_plan"                        // `dispatcher` - 压力测试计划
	MQTaskTypeDispatcherPerfSuite                    = "perf_suite"                       // `dispatcher` - 压力测试集合
	MQTaskTypeDispatcherSendPerfNotification         = "send_perf_notification"           // `dispatcher` - 发送压测通知
	MQTaskTypeDispatcherSendStabilityNotification    = "send_stability_notification"      // `dispatcher` - 发送稳定性测试通知

	MQTaskTypeManagerHandleParsePythonProjectResult      = "handle_parse_python_project_result"      // `manager` - 处理解析Python项目的结果
	MQTaskTypeManagerHandleCaseFailStatResult            = "handle_case_fail_stat_result"            // `manager` - 处理失败结果统计
	MQTaskTypeManagerHandleUpdatePerfPlanByCase          = "handle_update_perf_plan_by_case"         // `manager` - 更新压测计划用例
	MQTaskTypeManagerHandleUpdateInterfaceDefinition     = "handle_update_interface_definition"      // `manager` - 更新接口定义
	MQTaskTypeManagerHandleUpdateInterfaceCoverage       = "handle_update_interface_coverage"        // `manager` - 统计接口用例覆盖率
	MQTaskTypeManagerHandleDeleteDisabledDevice          = "handle_delete_disabled_device"           // `manager` - 删除无效的设备
	MQTaskTypeManagerHandleDeleteUnusedUIAgentImage      = "handle_delete_unused_ui_agent_image"     // `manager` - 删除未使用的`UI Agent`图片
	MQTaskTypeManagerHandlerUpdateUIAgentComponentResult = "handle_update_ui_agent_component_result" // `manager` - 更新`UI Agent`组件的执行结果

	MQTaskTypeReporterCheckTask                       = "check_task"                          // `reporter` - 检查定时清理任务状态
	MQTaskTypeReporterSplitCleanTask                  = "split_clean_task"                    // `reporter` - 拆分清理执行记录任务
	MQTaskTypeReporterSetCleanedTask                  = "set_cleaned_task"                    // `reporter` - 设置执行记录为已清理的任务
	MQTaskTypeReporterDelCleanedTask                  = "del_cleaned_task"                    // `reporter` - 删除已清理的执行记录的任务
	MQTaskTypeReporterCaseFailLogResult               = "case_fail_log_result"                // `reporter` - 失败结果日志
	MQTaskTypeReporterSaveDevicePerfDataTask          = "save_device_perf_data_task"          // `reporter` - 保存设备性能数据任务
	MQTaskTypeReporterSaveDeviceStepTask              = "save_device_step_task"               // `reporter` - 保存设备步骤信息任务
	MQTaskTypeReporterModifyStabilityDeviceRecordTask = "modify_stability_device_record_task" // `reporter` - 修改稳定性测试设备记录任务
	MQTaskTypeReporterHandleUIAgentRecordTask         = "handle_ui_agent_record_task"         // `reporter` - 处理`UI Agent`记录任务

	MQTaskTypeWorkerPlanMonitor        = "plan_monitor"         // `worker` - 监控计划进度，结束后回调
	MQTaskTypeWorkerParsePythonProject = "parse_python_project" // `worker` - 解析Python项目

	MQTaskTypeLarkProxyChatDisbanded  = "chat_disbanded"   // `larkproxy` - 飞书群解散
	MQTaskTypeLarkProxyChatUpdated    = "chat_updated"     // `larkproxy` - 飞书群配置修改
	MQTaskTypeLarkProxyChatBotDeleted = "chat_bot_deleted" // `larkproxy` - 机器人被移出飞书群

	MQTaskTypeAccountReleaseTestAccount = "release_test_account" // `account` - 释放测试账号
)

const (
	MQCNameDispatcherConsumer    = "mqc:dispatcher"               // `dispatcher` mqc生产消费队列名
	MQNameDispatcherTask         = "mq:dispatcher_task"           // `dispatcher` - 分发任务
	MQNamePeriodicPlanTask       = "mq:periodic_plan_task"        // 定时触发执行测试计划的任务
	MQNameParsePythonProjectTask = "mq:parse_python_project_task" // 接口触发执行解析Python项目任务

	MQNameUIWorkerDevicePrefix = "mqc:ui_worker_device:" // `uiworker` - 设备队列名前缀
)

const (
	MQTaskTypeRelationBindTestCaseToService   = "mq:relation_bind_test_case_to_service"    // Deprecated: `relation` 精准测试之服务与测试用例绑定
	MQTaskTypeRelationBindTestCaseToServiceV2 = "mq:relation_bind_test_case_to_service_v2" // Deprecated: `relation` 精准测试之服务与测试用例绑定v2
	MQTaskTypeRelationBindCaseToService       = "bind_case_to_service"                     // `relation` - 服务与测试用例绑定
)
