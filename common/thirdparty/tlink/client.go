package tlink

import (
	"fmt"
	"net/http"
	"net/url"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/fasthttp"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

type Client struct {
	c    *fasthttp.Client
	conf Config
}

func NewClient(conf Config) *Client {
	return &Client{
		c: fasthttp.NewClient(
			fasthttp.ClientConf{
				BaseURL: conf.BaseURL,
			},
		),
		conf: conf,
	}
}

func (c *Client) send(apiName string, req *fasthttp.Request, resp *fasthttp.Response) (
	body []byte, err error,
) {
	if err = c.c.Send(req, resp, requestTimeout); err != nil {
		return body, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of TLink, api: %s, error: %+v",
			apiName, err,
		)
	}

	body = resp.Body()
	status := resp.StatusCode()
	if status != http.StatusOK {
		return body, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of TLink, api: %s, status code: %d, resp body: %s",
			apiName, status, body,
		)
	}

	return body, err
}

// GetQueryTemplates 获取当前支持的查询模板
func (c *Client) GetQueryTemplates() (*GetQueryTemplatesResp, error) {
	apiName := getQueryTemplatesAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodGet),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return nil, err
	}

	var out GetQueryTemplatesResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of TLink, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	return &out, nil
}

// QueryServiceByMethod 通过方法名查询服务
// 注：指定的方法不是实际调用的方法则返回空
// 如：
// `/ga.api.present_go.PresentGoLogic/CommonSendPresent`: 返回空
// `/logic.present_go_logic.PresentGoLogic/CommonSendPresent`: 返回非空
func (c *Client) QueryServiceByMethod(methodType constants.MethodType, method string) (*Entity, error) {
	apiName := queryTraceDataAPIName
	if methodType == constants.MethodTypeOfGRPC {
		method = utils.GetGRPCFullMethodName(method)
	}

	values := url.Values{}
	values.Add(queryOfName, templateOfRelationMethodBindWorkload)
	values.Add(queryOfParams, method)

	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(fmt.Sprintf("%s?%s", apiName, values.Encode()))),
		fasthttp.SetMethod(http.MethodGet),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return nil, err
	}

	var out QueryTraceDataResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of TLink, api: %s, method: %s, resp body: %s, error: %+v",
			apiName, method, body, err,
		)
	}

	if len(out.Entities) == 0 {
		return nil, errorx.Errorf(
			errorx.NotExists,
			"not found the service by method, api: %s, method: %s, resp body: %s",
			apiName, method, body,
		)
	}

	return out.Entities[0], nil
}

// QueryServicesByMethod 通过方法名查询多个服务
// 注：指定的方法没有`workload`则返回空
// 如：
// `/ga.api.present_go.PresentGoLogic/CommonSendPresent`: 返回空
// `/logic.present_go_logic.PresentGoLogic/CommonSendPresent`: 返回非空
func (c *Client) QueryServicesByMethod(methodType constants.MethodType, method string) ([]*Entity, error) {
	apiName := queryTraceDataAPIName
	if methodType == constants.MethodTypeOfGRPC {
		method = utils.GetGRPCFullMethodName(method)
	}

	values := url.Values{}
	values.Add(queryOfName, templateOfRelationMethodBindWorkload)
	values.Add(queryOfParams, method)

	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(fmt.Sprintf("%s?%s", apiName, values.Encode()))),
		fasthttp.SetMethod(http.MethodGet),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return nil, err
	}

	var out QueryTraceDataResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of TLink, api: %s, method: %s, resp body: %s, error: %+v",
			apiName, method, body, err,
		)
	}

	return out.Entities, nil
}

// QueryServiceAndRewriteByMethod 通过方法名查询服务和重写的方法路径
// 注：指定的方法没有`rewrite`则返回空
// 如：
// `/ga.api.present_go.PresentGoLogic/CommonSendPresent`: 返回非空
// `/ga.api.revenue_ext_game.RevenueExtGameLogic/GetUserExtGameInfo`: 返回空
func (c *Client) QueryServiceAndRewriteByMethod(methodType constants.MethodType, method string) ([]*Entity, error) {
	apiName := queryTraceDataAPIName
	if methodType == constants.MethodTypeOfGRPC {
		method = utils.GetGRPCFullMethodName(method)
	}

	values := url.Values{}
	values.Add(queryOfName, templateOfRelationRewriteBindMethod)
	values.Add(queryOfParams, method)

	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(fmt.Sprintf("%s?%s", apiName, values.Encode()))),
		fasthttp.SetMethod(http.MethodGet),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return nil, err
	}

	var out QueryTraceDataResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of TLink, api: %s, method: %s, resp body: %s, error: %+v",
			apiName, method, body, err,
		)
	}

	return out.Entities, nil
}

// QueryToServicesByMethod 通过方法名查询经过的服务
// 注：指定的方法不是实际调用的方法则返回空
// 如：
// `/ga.api.present_go.PresentGoLogic/CommonSendPresent`: 返回空
// `/logic.present_go_logic.PresentGoLogic/CommonSendPresent`: 返回非空
func (c *Client) QueryToServicesByMethod(service string, methodType constants.MethodType, method string) (
	[]*Entity, error,
) {
	apiName := queryTraceDataAPIName
	if methodType == constants.MethodTypeOfGRPC {
		method = utils.GetGRPCFullMethodName(method)
	}

	values := url.Values{}
	values.Add(queryOfName, templateOfRelationMethodToWorkload)
	values.Add(queryOfParams, service)
	values.Add(queryOfParams, method)

	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(fmt.Sprintf("%s?%s", apiName, values.Encode()))),
		fasthttp.SetMethod(http.MethodGet),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return nil, err
	}

	var out QueryTraceDataResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of TLink, api: %s, method: %s, resp body: %s, error: %+v",
			apiName, method, body, err,
		)
	}

	return out.Entities, nil
}
