package tlink

import (
	"reflect"
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
)

func TestClient_GetQueryTemplates(t *testing.T) {
	c := NewClient(
		Config{
			BaseURL: "https://testing-internal-api.ttyuyin.com",
		},
	)
	got, err := c.GetQueryTemplates()
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("%s", jsonx.MarshalIgnoreError(got))
}

func TestClient_QueryServiceByMethod(t *testing.T) {
	c := NewClient(
		Config{
			BaseURL: "https://testing-internal-api.ttyuyin.com",
		},
	)

	type args struct {
		methodType constants.MethodType
		method     string
	}
	tests := []struct {
		name    string
		args    args
		want    *Entity
		wantErr bool
	}{
		{
			name: "ga.api.channel_rank.ChannelRankLogic.ChannelGetMemberList",
			args: args{
				methodType: constants.MethodTypeOfGRPC,
				method:     "/ga.api.channel_rank.ChannelRankLogic/ChannelGetMemberList",
			},
			want: &Entity{
				Label: "Workload",
				Name:  "channel-rank-logic",
				Properties: Properties{
					Namespace: "quicksilver",
				},
			},
			wantErr: false,
		},
		{
			name: "ga.api.auth.AuthLogic.Auth",
			args: args{
				methodType: constants.MethodTypeOfGRPC,
				method:     "/ga.api.auth.AuthLogic/Auth",
			},
			want: &Entity{
				Label: "Workload",
				Name:  "auth-logic",
				Properties: Properties{
					Namespace: "quicksilver",
				},
			},
			wantErr: false,
		},
		{
			name: "ga.api.sync.SyncGoLogic.CheckSyncKey",
			args: args{
				methodType: constants.MethodTypeOfGRPC,
				method:     "/ga.api.sync.SyncGoLogic/CheckSyncKey",
			},
			want: &Entity{
				Label: "Workload",
				Name:  "sync-logic",
				Properties: Properties{
					Namespace: "quicksilver",
				},
			},
			wantErr: false,
		},
		{
			name: "ga.api.auth.AuthCppLogic.RefreshUnionTokenService",
			args: args{
				methodType: constants.MethodTypeOfGRPC,
				method:     "/ga.api.auth.AuthCppLogic/RefreshUnionTokenService",
			},
			want: &Entity{
				Label: "Workload",
				Name:  "authlogic",
				Properties: Properties{
					Namespace: "appsvr",
				},
			},
			wantErr: false,
		},
		{
			name: "ga.api.grpc_transport_cfg.GrpcTransportCfgLogic.RefreshTransportConfig",
			args: args{
				methodType: constants.MethodTypeOfGRPC,
				method:     "/ga.api.grpc_transport_cfg.GrpcTransportCfgLogic/RefreshTransportConfig",
			},
			want: &Entity{
				Label: "Workload",
				Name:  "grpc-transport-cfg-logic",
				Properties: Properties{
					Namespace: "quicksilver",
				},
			},
			wantErr: false,
		},
		{
			name: "testing-internal-api.ttyuyin.com/user-httplogic",
			args: args{
				methodType: constants.MethodTypeOfHTTP,
				method:     "testing-internal-api.ttyuyin.com/user-httplogic",
			},
			want: &Entity{
				Label: "Workload",
				Name:  "user-httplogic",
				Properties: Properties{
					Namespace: "quicksilver",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := c.QueryServiceByMethod(tt.args.methodType, tt.args.method)
				if (err != nil) != tt.wantErr {
					t.Errorf("QueryServiceByMethod() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				t.Logf("QueryServiceByMethod() got: %s", jsonx.MarshalIgnoreError(got))

				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("QueryServiceByMethod() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestClient_QueryServiceAndRewriteByMethod(t *testing.T) {
	c := NewClient(
		Config{
			BaseURL: "https://testing-internal-api.ttyuyin.com",
		},
	)

	type args struct {
		methodType constants.MethodType
		method     string
	}
	tests := []struct {
		name    string
		args    args
		want    []*Entity
		wantErr bool
	}{
		{
			name: "has rewrite",
			args: args{
				methodType: constants.MethodTypeOfGRPC,
				method:     "/ga.api.present_go.PresentGoLogic/CommonSendPresent",
			},
			want: []*Entity{
				{
					Label: "Method",
					Name:  "/logic.present_go_logic.PresentGoLogic/CommonSendPresent",
					Properties: Properties{
						Namespace: "quicksilver",
					},
				},
				{
					Label: "Workload",
					Name:  "present-go-logic",
					Properties: Properties{
						Level:     "1",
						Namespace: "quicksilver",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "no rewrite - grpc",
			args: args{
				methodType: constants.MethodTypeOfGRPC,
				method:     "/ga.api.revenue_ext_game.RevenueExtGameLogic/GetUserExtGameInfo",
			},
			want:    []*Entity{},
			wantErr: false,
		},
		{
			name: "no rewrite - http",
			args: args{
				methodType: constants.MethodTypeOfHTTP,
				method:     "testing-internal-api.ttyuyin.com/user-httplogic",
			},
			want:    []*Entity{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := c.QueryServiceAndRewriteByMethod(tt.args.methodType, tt.args.method)
				if (err != nil) != tt.wantErr {
					t.Errorf("QueryServiceAndRewriteByMethod() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				t.Logf("QueryServiceAndRewriteByMethod() got: %s", jsonx.MarshalIgnoreError(got))

				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("QueryServiceAndRewriteByMethod() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestClient_QueryToServicesByMethod(t *testing.T) {
	c := NewClient(
		Config{
			BaseURL: "https://testing-internal-api.ttyuyin.com",
		},
	)

	type args struct {
		service    string
		methodType constants.MethodType
		method     string
	}
	tests := []struct {
		name    string
		args    args
		want    []*Entity
		wantErr bool
	}{
		{
			name: "",
			args: args{
				service:    "present-go-logic",
				methodType: constants.MethodTypeOfGRPC,
				method:     "/logic.present_go_logic.PresentGoLogic/CommonSendPresent",
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "with wrong service",
			args: args{
				service:    "present-logic",
				methodType: constants.MethodTypeOfGRPC,
				method:     "/logic.present_go_logic.PresentGoLogic/CommonSendPresent",
			},
			want:    []*Entity{},
			wantErr: false,
		},
		{
			name: "with wrong method",
			args: args{
				service:    "present-go-logic",
				methodType: constants.MethodTypeOfGRPC,
				method:     "/ga.api.present_go.PresentGoLogic/CommonSendPresent",
			},
			want:    []*Entity{},
			wantErr: false,
		},
		{
			name: "",
			args: args{
				service:    "user-httplogic",
				methodType: constants.MethodTypeOfHTTP,
				method:     "testing-internal-api.ttyuyin.com/user-httplogic",
			},
			want:    []*Entity{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := c.QueryToServicesByMethod(tt.args.service, tt.args.methodType, tt.args.method)
				if (err != nil) != tt.wantErr {
					t.Errorf("QueryToServicesByMethod() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				t.Logf("QueryToServicesByMethod() got: %s", jsonx.MarshalIgnoreError(got))

				//if !reflect.DeepEqual(got, tt.want) {
				//	t.Errorf("QueryToServicesByMethod() got = %v, want %v", got, tt.want)
				//}
			},
		)
	}
}
