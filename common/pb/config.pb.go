// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: common/config.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GeneralConfigVar struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GeneralConfigVar) Reset() {
	*x = GeneralConfigVar{}
	mi := &file_common_config_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GeneralConfigVar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneralConfigVar) ProtoMessage() {}

func (x *GeneralConfigVar) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneralConfigVar.ProtoReflect.Descriptor instead.
func (*GeneralConfigVar) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{0}
}

func (x *GeneralConfigVar) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *GeneralConfigVar) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type GeneralConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	ConfigId      string                 `protobuf:"bytes,2,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	BaseUrl       string                 `protobuf:"bytes,5,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
	Verify        bool                   `protobuf:"varint,6,opt,name=verify,proto3" json:"verify,omitempty"`
	Variables     []*GeneralConfigVar    `protobuf:"bytes,7,rep,name=variables,proto3" json:"variables,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GeneralConfig) Reset() {
	*x = GeneralConfig{}
	mi := &file_common_config_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GeneralConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneralConfig) ProtoMessage() {}

func (x *GeneralConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneralConfig.ProtoReflect.Descriptor instead.
func (*GeneralConfig) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{1}
}

func (x *GeneralConfig) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *GeneralConfig) GetConfigId() string {
	if x != nil {
		return x.ConfigId
	}
	return ""
}

func (x *GeneralConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GeneralConfig) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GeneralConfig) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

func (x *GeneralConfig) GetVerify() bool {
	if x != nil {
		return x.Verify
	}
	return false
}

func (x *GeneralConfig) GetVariables() []*GeneralConfigVar {
	if x != nil {
		return x.Variables
	}
	return nil
}

type AccountConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	ConfigId      string                 `protobuf:"bytes,2,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	ProductType   int64                  `protobuf:"varint,5,opt,name=product_type,json=productType,proto3" json:"product_type,omitempty"`
	ProductName   string                 `protobuf:"bytes,6,opt,name=product_name,json=productName,proto3" json:"product_name,omitempty"`
	PoolEnvTable  string                 `protobuf:"bytes,7,opt,name=pool_env_table,json=poolEnvTable,proto3" json:"pool_env_table,omitempty"`
	PoolEnvName   string                 `protobuf:"bytes,8,opt,name=pool_env_name,json=poolEnvName,proto3" json:"pool_env_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountConfig) Reset() {
	*x = AccountConfig{}
	mi := &file_common_config_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountConfig) ProtoMessage() {}

func (x *AccountConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountConfig.ProtoReflect.Descriptor instead.
func (*AccountConfig) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{2}
}

func (x *AccountConfig) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *AccountConfig) GetConfigId() string {
	if x != nil {
		return x.ConfigId
	}
	return ""
}

func (x *AccountConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AccountConfig) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AccountConfig) GetProductType() int64 {
	if x != nil {
		return x.ProductType
	}
	return 0
}

func (x *AccountConfig) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *AccountConfig) GetPoolEnvTable() string {
	if x != nil {
		return x.PoolEnvTable
	}
	return ""
}

func (x *AccountConfig) GetPoolEnvName() string {
	if x != nil {
		return x.PoolEnvName
	}
	return ""
}

type GitConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`       // 项目ID
	ConfigId      string                 `protobuf:"bytes,2,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`          // Git配置ID
	Type          string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`                                  // Git类型（GitLab、GitHub、Gitee）
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                                  // Git配置名称
	Description   string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                    // Git配置描述
	Url           string                 `protobuf:"bytes,6,opt,name=url,proto3" json:"url,omitempty"`                                    // Git项目URL（包括访问令牌）
	AccessToken   string                 `protobuf:"bytes,7,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"` // Git项目访问令牌
	Branch        string                 `protobuf:"bytes,8,opt,name=branch,proto3" json:"branch,omitempty"`                              // Git项目分支名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GitConfig) Reset() {
	*x = GitConfig{}
	mi := &file_common_config_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GitConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GitConfig) ProtoMessage() {}

func (x *GitConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GitConfig.ProtoReflect.Descriptor instead.
func (*GitConfig) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{3}
}

func (x *GitConfig) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *GitConfig) GetConfigId() string {
	if x != nil {
		return x.ConfigId
	}
	return ""
}

func (x *GitConfig) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GitConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GitConfig) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GitConfig) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *GitConfig) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *GitConfig) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

type ProtobufConfig struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	ProjectId     string                      `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`          // 项目ID
	ConfigId      string                      `protobuf:"bytes,2,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`             // Protobuf配置ID
	Name          string                      `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                     // Protobuf配置名称
	Description   string                      `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                       // Protobuf配置描述
	GitConfig     *GitConfig                  `protobuf:"bytes,5,opt,name=git_config,json=gitConfig,proto3" json:"git_config,omitempty"`          // Git配置
	ImportPath    string                      `protobuf:"bytes,6,opt,name=import_path,json=importPath,proto3" json:"import_path,omitempty"`       // 导入路径
	ExcludePaths  []string                    `protobuf:"bytes,7,rep,name=exclude_paths,json=excludePaths,proto3" json:"exclude_paths,omitempty"` // 排除的路径列表
	ExcludeFiles  []string                    `protobuf:"bytes,8,rep,name=exclude_files,json=excludeFiles,proto3" json:"exclude_files,omitempty"` // 排除的文件列表
	Dependencies  []*ProtobufDependenceConfig `protobuf:"bytes,9,rep,name=dependencies,proto3" json:"dependencies,omitempty"`                     // 依赖列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProtobufConfig) Reset() {
	*x = ProtobufConfig{}
	mi := &file_common_config_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProtobufConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProtobufConfig) ProtoMessage() {}

func (x *ProtobufConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProtobufConfig.ProtoReflect.Descriptor instead.
func (*ProtobufConfig) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{4}
}

func (x *ProtobufConfig) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ProtobufConfig) GetConfigId() string {
	if x != nil {
		return x.ConfigId
	}
	return ""
}

func (x *ProtobufConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProtobufConfig) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ProtobufConfig) GetGitConfig() *GitConfig {
	if x != nil {
		return x.GitConfig
	}
	return nil
}

func (x *ProtobufConfig) GetImportPath() string {
	if x != nil {
		return x.ImportPath
	}
	return ""
}

func (x *ProtobufConfig) GetExcludePaths() []string {
	if x != nil {
		return x.ExcludePaths
	}
	return nil
}

func (x *ProtobufConfig) GetExcludeFiles() []string {
	if x != nil {
		return x.ExcludeFiles
	}
	return nil
}

func (x *ProtobufConfig) GetDependencies() []*ProtobufDependenceConfig {
	if x != nil {
		return x.Dependencies
	}
	return nil
}

type ProtobufDependenceConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`    // 项目ID
	ConfigId      string                 `protobuf:"bytes,2,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`       // Protobuf配置ID
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                               // Protobuf配置名称
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                 // Protobuf配置描述
	GitConfig     *GitConfig             `protobuf:"bytes,5,opt,name=git_config,json=gitConfig,proto3" json:"git_config,omitempty"`    // Git配置
	ImportPath    string                 `protobuf:"bytes,6,opt,name=import_path,json=importPath,proto3" json:"import_path,omitempty"` // 导入路径
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProtobufDependenceConfig) Reset() {
	*x = ProtobufDependenceConfig{}
	mi := &file_common_config_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProtobufDependenceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProtobufDependenceConfig) ProtoMessage() {}

func (x *ProtobufDependenceConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProtobufDependenceConfig.ProtoReflect.Descriptor instead.
func (*ProtobufDependenceConfig) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{5}
}

func (x *ProtobufDependenceConfig) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ProtobufDependenceConfig) GetConfigId() string {
	if x != nil {
		return x.ConfigId
	}
	return ""
}

func (x *ProtobufDependenceConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProtobufDependenceConfig) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ProtobufDependenceConfig) GetGitConfig() *GitConfig {
	if x != nil {
		return x.GitConfig
	}
	return nil
}

func (x *ProtobufDependenceConfig) GetImportPath() string {
	if x != nil {
		return x.ImportPath
	}
	return ""
}

type PromptConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`          // 项目ID
	ConfigId      string                 `protobuf:"bytes,2,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`             // Prompt配置ID
	Purpose       PromptPurpose          `protobuf:"varint,3,opt,name=purpose,proto3,enum=common.PromptPurpose" json:"purpose,omitempty"`    // 用途（1: UI_AGENT）
	Category      PromptCategory         `protobuf:"varint,4,opt,name=category,proto3,enum=common.PromptCategory" json:"category,omitempty"` // 分类（1: 背景、2: UI组件、3: 异常处理）
	Name          string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                    // Prompt配置名称
	Description   string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`                      // Prompt配置描述
	Content       string                 `protobuf:"bytes,13,opt,name=content,proto3" json:"content,omitempty"`                              // Prompt配置内容
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PromptConfig) Reset() {
	*x = PromptConfig{}
	mi := &file_common_config_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PromptConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PromptConfig) ProtoMessage() {}

func (x *PromptConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PromptConfig.ProtoReflect.Descriptor instead.
func (*PromptConfig) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{6}
}

func (x *PromptConfig) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PromptConfig) GetConfigId() string {
	if x != nil {
		return x.ConfigId
	}
	return ""
}

func (x *PromptConfig) GetPurpose() PromptPurpose {
	if x != nil {
		return x.Purpose
	}
	return PromptPurpose_PromptPurpose_NULL
}

func (x *PromptConfig) GetCategory() PromptCategory {
	if x != nil {
		return x.Category
	}
	return PromptCategory_PromptCategory_NULL
}

func (x *PromptConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PromptConfig) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PromptConfig) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type ApplicationConfig struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                     // 项目ID
	ConfigId        string                 `protobuf:"bytes,2,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`                                        // 应用配置ID
	Name            string                 `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                                               // 应用配置名称
	Description     string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`                                                 // 应用配置描述
	PlatformType    PlatformType           `protobuf:"varint,13,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"` // 平台类型（Android、IOS）
	AppId           string                 `protobuf:"bytes,14,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                                                // 应用ID（Android：package_name；IOS：bundle_id）
	AppDownloadLink string                 `protobuf:"bytes,15,opt,name=app_download_link,json=appDownloadLink,proto3" json:"app_download_link,omitempty"`                // APP下载地址
	Prompts         []*PromptConfig        `protobuf:"bytes,21,rep,name=prompts,proto3" json:"prompts,omitempty"`                                                         // Prompt配置列表
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ApplicationConfig) Reset() {
	*x = ApplicationConfig{}
	mi := &file_common_config_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApplicationConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicationConfig) ProtoMessage() {}

func (x *ApplicationConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicationConfig.ProtoReflect.Descriptor instead.
func (*ApplicationConfig) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{7}
}

func (x *ApplicationConfig) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ApplicationConfig) GetConfigId() string {
	if x != nil {
		return x.ConfigId
	}
	return ""
}

func (x *ApplicationConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ApplicationConfig) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ApplicationConfig) GetPlatformType() PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return PlatformType_PT_NULL
}

func (x *ApplicationConfig) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ApplicationConfig) GetAppDownloadLink() string {
	if x != nil {
		return x.AppDownloadLink
	}
	return ""
}

func (x *ApplicationConfig) GetPrompts() []*PromptConfig {
	if x != nil {
		return x.Prompts
	}
	return nil
}

var File_common_config_proto protoreflect.FileDescriptor

var file_common_config_proto_rawDesc = []byte{
	0x0a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x11, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x3a, 0x0a, 0x10, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x56, 0x61, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xec, 0x01, 0x0a,
	0x0d, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x73, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x76, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x12, 0x36, 0x0a, 0x09, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56, 0x61, 0x72,
	0x52, 0x09, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x22, 0x91, 0x02, 0x0a, 0x0d,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x65, 0x6e,
	0x76, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70,
	0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x76, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x70,
	0x6f, 0x6f, 0x6c, 0x5f, 0x65, 0x6e, 0x76, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x76, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0xde, 0x01, 0x0a, 0x09, 0x47, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e,
	0x63, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x22, 0xe5, 0x02, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x0a, 0x67, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x47, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x67, 0x69,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0c, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x50, 0x61, 0x74, 0x68, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x46, 0x69, 0x6c,
	0x65, 0x73, 0x12, 0x44, 0x0a, 0x0c, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69,
	0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x22, 0xdf, 0x01, 0x0a, 0x18, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x0a, 0x67, 0x69, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09,
	0x67, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x74, 0x68, 0x22, 0xff, 0x01, 0x0a, 0x0c, 0x50,
	0x72, 0x6f, 0x6d, 0x70, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f,
	0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x52,
	0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xb3, 0x02, 0x0a,
	0x11, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x5f, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x69,
	0x6e, 0x6b, 0x12, 0x2e, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x15, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f,
	0x6d, 0x70, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6d, 0x70,
	0x74, 0x73, 0x42, 0x3c, 0x5a, 0x3a, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79,
	0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_config_proto_rawDescOnce sync.Once
	file_common_config_proto_rawDescData = file_common_config_proto_rawDesc
)

func file_common_config_proto_rawDescGZIP() []byte {
	file_common_config_proto_rawDescOnce.Do(func() {
		file_common_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_config_proto_rawDescData)
	})
	return file_common_config_proto_rawDescData
}

var file_common_config_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_common_config_proto_goTypes = []any{
	(*GeneralConfigVar)(nil),         // 0: common.GeneralConfigVar
	(*GeneralConfig)(nil),            // 1: common.GeneralConfig
	(*AccountConfig)(nil),            // 2: common.AccountConfig
	(*GitConfig)(nil),                // 3: common.GitConfig
	(*ProtobufConfig)(nil),           // 4: common.ProtobufConfig
	(*ProtobufDependenceConfig)(nil), // 5: common.ProtobufDependenceConfig
	(*PromptConfig)(nil),             // 6: common.PromptConfig
	(*ApplicationConfig)(nil),        // 7: common.ApplicationConfig
	(PromptPurpose)(0),               // 8: common.PromptPurpose
	(PromptCategory)(0),              // 9: common.PromptCategory
	(PlatformType)(0),                // 10: common.PlatformType
}
var file_common_config_proto_depIdxs = []int32{
	0,  // 0: common.GeneralConfig.variables:type_name -> common.GeneralConfigVar
	3,  // 1: common.ProtobufConfig.git_config:type_name -> common.GitConfig
	5,  // 2: common.ProtobufConfig.dependencies:type_name -> common.ProtobufDependenceConfig
	3,  // 3: common.ProtobufDependenceConfig.git_config:type_name -> common.GitConfig
	8,  // 4: common.PromptConfig.purpose:type_name -> common.PromptPurpose
	9,  // 5: common.PromptConfig.category:type_name -> common.PromptCategory
	10, // 6: common.ApplicationConfig.platform_type:type_name -> common.PlatformType
	6,  // 7: common.ApplicationConfig.prompts:type_name -> common.PromptConfig
	8,  // [8:8] is the sub-list for method output_type
	8,  // [8:8] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_common_config_proto_init() }
func file_common_config_proto_init() {
	if File_common_config_proto != nil {
		return
	}
	file_common_enum_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_config_proto_goTypes,
		DependencyIndexes: file_common_config_proto_depIdxs,
		MessageInfos:      file_common_config_proto_msgTypes,
	}.Build()
	File_common_config_proto = out.File
	file_common_config_proto_rawDesc = nil
	file_common_config_proto_goTypes = nil
	file_common_config_proto_depIdxs = nil
}
