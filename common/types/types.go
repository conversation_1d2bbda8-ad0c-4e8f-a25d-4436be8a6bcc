package types

type Relation struct {
	Id          string        `json:"id"`
	Type        string        `json:"type"`
	Children    [][]*Relation `json:"children,omitempty,optional"`
	ReferenceId string        `json:"reference_id,omitempty,optional"`
}

func (r *Relation) LessKey() string {
	return r.Id
}

type AccountConfig struct {
	Whole map[string]string `json:"whole"`
	Combo map[string]string `json:"combo"`
}

type KeyValuePair struct {
	Key   string `json:"key" validate:"required"`
	Value string `json:"value" validate:"required"`
}

type Parameter struct {
	Name        string             `json:"name"`
	Source      int64              `json:"source"`
	Manual      *VariableValue     `json:"manual"`
	Export      *VariableNodeValue `json:"export"`
	Environment *VariableValue     `json:"environment"`
	Function    *VariableFuncValue `json:"function,omitempty,optional"`
}

type VariableValue struct {
	Value string `json:"value" validate:"required"`
}

type VariableNodeValue struct {
	NodeId string `json:"node_id" validate:"required"`
	VariableValue
}

type VariableFuncValue struct {
	Name       string       `json:"name" validate:"required"`
	Type       int64        `json:"type" validate:"required,oneof=0 1"`
	Parameters []*Parameter `json:"parameters"`
}

type VariableHeaderValue struct {
	Key string `json:"key" validate:"required"`
}

type VariableBodyValue struct {
	Type       int8   `json:"type" validate:"required,oneof=0 1"`
	Expression string `json:"expression" validate:"required"`
}

type GeneralConfigVar struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type ApiGeneralConfig struct {
	ProjectId   string             `json:"project_id"`  // 项目ID
	ConfigId    string             `json:"config_id"`   // 通用配置ID
	Name        string             `json:"name"`        // 通用配置名称
	Description string             `json:"description"` // 通用配置描述
	BaseUrl     string             `json:"base_url"`    // HTTP请求基础URL
	Verify      bool               `json:"verify"`      // 是否验证服务器的TLS证书
	Variables   []GeneralConfigVar `json:"variables"`
}

type ApiAccountConfig struct {
	ProjectId    string `json:"project_id"`     // 项目ID
	ConfigId     string `json:"config_id"`      // 池账号配置ID
	Name         string `json:"name"`           // 账号配置名称
	Description  string `json:"description"`    // 池账号配置描述
	ProductType  int64  `json:"product_type"`   // 产品类型
	ProductName  string `json:"product_name"`   // 产品名称
	PoolEnvTable string `json:"pool_env_table"` // 账号池环境表名称
	PoolEnvName  string `json:"pool_env_name"`  // 账号池环境名称
}

type GitConfig struct {
	ProjectId   string `json:"project_id"`   // 项目ID
	ConfigId    string `json:"config_id"`    // Git配置ID
	Type        string `json:"type"`         // Git配置类型
	Name        string `json:"name"`         // Git配置名称
	Description string `json:"description"`  // Git配置描述
	Url         string `json:"url"`          // Git项目URL
	AccessToken string `json:"access_token"` // Git项目访问令牌
	Branch      string `json:"branch"`       // Git项目分支名称
}

type ProtobufConfig struct{}

type RateLimit struct {
	TargetRps    int64  `json:"target_rps" validate:"gt=0"`               // 目标的RPS
	InitialRps   int64  `json:"initial_rps" validate:"omitempty,gt=0"`    // 初始的RPS
	StepHeight   int64  `json:"step_height" validate:"omitempty,ne=0"`    // 每次改变RPS的量
	StepDuration string `json:"step_duration" validate:"omitempty,gte=2"` // 改变后的RPS的持续时间
}

type RateLimitV2 struct {
	TargetRps      int64  `json:"target_rps" validate:"gt=0"`                 // 目标的RPS
	InitialRps     int64  `json:"initial_rps" validate:"omitempty,gt=0"`      // 初始的RPS
	ChangeDuration string `json:"change_duration" validate:"omitempty,gte=2"` // 加压持续时间（从初始值到目标值）
	TargetDuration string `json:"target_duration" validate:"omitempty,gte=2"` // 施压持续时间（经过加压时间后维持的时间）
}

type PerfUserInfo struct {
	Account    string `json:"account"`      // 账号
	Fullname   string `json:"fullname"`     // 全名
	Email      string `json:"email"`        // 邮箱
	LarkUserId string `json:"lark_user_id"` // 飞书的用户ID
}

type PerfServiceMetaData struct {
	Name        string          `json:"name"`        // 服务名称
	Namespace   string          `json:"namespace"`   // 命名空间
	Developers  []*PerfUserInfo `json:"developers"`  // 研发人员
	Maintainers []*PerfUserInfo `json:"maintainers"` // 运维人员
}

type ExportVariable struct {
	Name       string `json:"name" validate:"required"`
	Expression string `json:"expression" validate:"required"`
}

type PerfCaseStepV2 struct {
	Name         string            `json:"name" validate:"required"`
	RateLimits   []*RateLimitV2    `json:"rate_limits" validate:"gte=1"`
	Url          string            `json:"url,optional" validate:"omitempty"`
	Method       string            `json:"method" validate:"required"`
	Headers      map[string]string `json:"headers,optional" validate:"omitempty"`
	Body         string            `json:"body" validate:"omitempty"`
	Exports      []*ExportVariable `json:"exports,optional" validate:"omitempty"`
	Sleep        string            `json:"sleep,optional" validate:"omitempty,gte=2"`
	ReferenceQps *int64            `json:"reference_qps,optional" validate:"omitempty,gt=0"` // QPS参考值
}

type BasicPerfData struct {
	PerfDataId string `json:"perf_data_id" validate:"omitempty"`       // 压测数据ID
	CustomVu   bool   `json:"custom_vu"`                               // 是否自定义虚拟用户数
	NumberOfVu uint32 `json:"number_of_vu" validate:"omitempty,gte=0"` // 虚拟用户数
}

type LoadGenerator struct {
	CustomLg         bool   `json:"custom_lg"`                                     // 是否自定义施压机资源
	NumberOfLg       uint32 `json:"number_of_lg" validate:"omitempty,gte=0"`       // 施压机数量
	RequestsOfCpu    string `json:"requests_of_cpu" validate:"omitempty,gte=1"`    // 最小分配的CPU资源
	RequestsOfMemory string `json:"requests_of_memory" validate:"omitempty,gte=1"` // 最小分配的内存资源
	LimitsOfCpu      string `json:"limits_of_cpu" validate:"omitempty,gte=1"`      // 最大分配的CPU资源
	LimitsOfMemory   string `json:"limits_of_memory" validate:"omitempty,gte=1"`   // 最大分配的内存资源
}

type LarkChat struct {
	ChatId string `json:"chat_id" validate:"eq=35,startswith=oc_" zh:"飞书群组ID"`
	Name   string `json:"name" validate:"gte=1,lte=64" zh:"飞书群组名称"`
}

type ClearStrategy struct {
	CronExpression string
	KeepDays       int
}

type StabilityCustomScript struct {
	Image string `json:"image" validate:"omitempty,gte=0" zh:"脚本镜像"`
}

type StabilityDevices struct {
	Udids []string `json:"udids" validate:"omitempty,gte=0" zh:"设备编号"`
	Count uint32   `json:"count,optional" zh:"设备数量"`
}

type SLABaseLine struct {
	FinishLaunchedLine int64 `json:"finish_launched_line"`
	AutoLoginLine      int64 `json:"auto_login_line"`
	NewHomePageLine    int64 `json:"new_home_page_line"`
}

type ApplicationConfig struct {
	ProjectId       string          `json:"project_id"`
	ConfigId        string          `json:"config_id"`
	Name            string          `json:"name"`
	Description     string          `json:"description"`
	PlatformType    int8            `json:"platform_type"`
	AppId           string          `json:"app_id"`
	AppDownloadLink string          `json:"app_download_link"`
	Prompts         []*PromptConfig `json:"prompts"`
}

type PromptConfig struct {
	ProjectId   string `json:"project_id"`
	ConfigId    string `json:"config_id"`
	Purpose     int8   `json:"purpose"`
	Category    int8   `json:"category"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Content     string `json:"content"`
}

type UIAgentComponentStep struct {
	Content     string                       `json:"content" validate:"required"`
	Expectation *UIAgentComponentExpectation `json:"expectation,optional"`
	WaitingTime float32                      `json:"waiting_time,default=2.5" validate:"gte=0,lte=300"`
}

type UIAgentComponentExpectation struct {
	Text  string `json:"text,optional"`
	Image string `json:"image,optional"`
}

type UIAgentDevice struct {
	ProjectDevice *UIAgentProjectDevice `json:"project_device,omitempty,optional"` // 项目设备
	UserDevice    *UIAgentUserDevice    `json:"user_device,omitempty,optional"`    // 用户设备
}

type UIAgentProjectDevice struct {
	DeviceType    int8   `json:"device_type"`    // 设备类型（真机、云手机）
	PlatformType  int8   `json:"platform_type"`  // 平台类型（Android、iOS）
	UDID          string `json:"udid"`           // 设备编号
	RemoteAddress string `json:"remote_address"` // 远程连接地址
	Token         string `json:"token"`          // 令牌（占用设备后得到的令牌）
}

type UIAgentUserDevice struct {
	DeviceType    int8   `json:"device_type"`    // 设备类型（真机、云手机）
	PlatformType  int8   `json:"platform_type"`  // 平台类型（Android、iOS）
	UDID          string `json:"udid"`           // 设备编号
	RemoteAddress string `json:"remote_address"` // 远程连接地址
}

type UIAgentComponentStepRecord struct {
	StepID    int64   `json:"step_id"`    // 步骤ID
	Index     int64   `json:"index"`      // 步骤索引
	Name      string  `json:"name"`       // 步骤名称
	Thought   string  `json:"thought"`    // 决策思考过程
	Action    string  `json:"action"`     // 动作
	Status    string  `json:"status"`     // 步骤状态
	Image     string  `json:"image"`      // 截图路径
	StartedAt string  `json:"started_at"` // 开始时间
	EndedAt   string  `json:"ended_at"`   // 结束时间
	CostTime  float64 `json:"cost_time"`  // 执行耗时
}
