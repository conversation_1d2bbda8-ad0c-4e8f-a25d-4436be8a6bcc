Name: ${WORKER_NAME}

Log:
  ServiceName: mqc.${WORKER_NAME}
  Encoding: plain
  Level: info
  Path: /app/logs/${WORKER_NAME}

#Prometheus:
#  Host: 0.0.0.0
#  Port: 9101
#  Path: /metrics
#
#Telemetry:
#  Name: mqc.staworker
#  Endpoint: http://tt-yw-tracing-jaeger.ttyuyin.com:9511
#  Sampler: 1.0
#  Batcher: zipkin
#
#DevServer:
#  Enabled: true
#  Port: 6470

Redis:
  Key: mqc.${WORKER_NAME}
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 23

DispatcherRedis:
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 4

Account:
  Endpoints:
    - 127.0.0.1:20111
  NonBlock: true
  Timeout: 0

Discovery:
  Target: 127.0.0.1:21511
  NonBlock: true
  Timeout: 0

Manager:
  Endpoints:
    - 127.0.0.1:20211
  NonBlock: true
  Timeout: 0

Reporter:
  Endpoints:
    - 127.0.0.1:20511
  NonBlock: true
  Timeout: 0

StabilityWorkerConsumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:${WORKER_NAME}
  ConsumerTag: mqc:${WORKER_NAME}
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 20
  MaxWorker: 0

StabilityWorkerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:statool
  Db: 20

DeviceHubProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:devicehub
  Db: 20

ReporterProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:reporter
  Db: 20

DispatcherProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:dispatcher
  Db: 20

ADB:
  PathOfADB: ../assets/adb/adb-darwin/adb

Constack:
  Url: cloud.ttyuyin.com:8100
  Token: Y2ljZA==.2565c2d93b2209fb0d52fe19cdc9557c1a4ba57b5a2b35cf10d4790b6e02e663

LocalPath: ./reports/stability_test

Task:
  TypeName: ${TASK_TYPE_NAME} # 任务类型
  TaskID: ${TASK_ID}          # 任务ID（可选）
  ExecuteID: ${EXECUTE_ID}    # 执行ID（可选）

Job:
  ClusterName: k8s-tc-bj-1-test                                 # 集群名称
  Namespace: ${MY_POD_NAMESPACE}                                # 命名空间
  Image: cr.ttyuyin.com/devops/20/staworker:${MY_IMAGE_VERSION} # 镜像名称
  PvcName: pvc-probe                                            # PVC名称
  MountPath: /app/data                                          # 挂载路径
  Resources: # 资源限制
    Limits:
      Cpu: "1"
      Memory: 1Gi
    Requests:
      Cpu: 500m
      Memory: 512Mi
