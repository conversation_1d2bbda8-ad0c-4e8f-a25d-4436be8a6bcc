package device

import (
	"context"
	"strings"
	"time"

	gid "github.com/electricbubble/gidevice"
	"github.com/electricbubble/gidevice/pkg/ipa"
	"github.com/electricbubble/gwda"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

const retryInterval = 3 * time.Second

var _ IDevice = (*IOSDevice)(nil)

type IOSDevice struct {
	logx.Logger
	ctx context.Context

	deviceType          commonpb.DeviceType
	udid, remoteAddress string

	usbmux gid.Usbmux
	device gid.Device

	driver gwda.WebDriver
}

func NewIOSDevice(ctx context.Context, deviceType commonpb.DeviceType, udid, remoteAddress string) (*IOSDevice, error) {
	connectHost, usb, device, err := utils.GIDConnect(udid, remoteAddress)
	if err != nil {
		return nil, err
	}

	driver, err := gwda.NewDriver(gwda.NewCapabilities(), remoteAddress)
	if err != nil {
		return nil, err
	}

	return &IOSDevice{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,

		deviceType:    deviceType,
		udid:          udid,
		remoteAddress: connectHost,

		usbmux: usb,
		device: device,

		driver: driver,
	}, nil
}

func (d *IOSDevice) Close() (err error) {
	if d.driver != nil {
		err = d.driver.Close()
	}

	if d.usbmux != nil {
		d.usbmux.Close()
	}

	return err
}

func (d *IOSDevice) DeviceType() commonpb.DeviceType {
	return d.deviceType
}

func (d *IOSDevice) PlatformType() commonpb.PlatformType {
	return commonpb.PlatformType_IOS
}

func (d *IOSDevice) UDID() string {
	return d.udid
}

func (d *IOSDevice) RemoteAddress() string {
	return d.remoteAddress
}

func (d *IOSDevice) Driver() gwda.WebDriver {
	return d.driver
}

func (d *IOSDevice) AppInstall(appPath string) error {
	info, err := ipa.Info(appPath)
	if err != nil {
		return errors.Wrapf(err, "failed to get ipa info, udid: %s, file: %s", d.udid, appPath)
	}

	var apps []*ipa.InfoPlist
	if err = caller.RetryDo(
		caller.MaxRetryCount, func() error {
			defer func() {
				if err != nil {
					time.Sleep(retryInterval)
				}
			}()

			apps, err = d.device.InstallationProxyBrowse(gid.WithBundleIDs(info.CFBundleIdentifier))
			if err != nil {
				return errors.Wrapf(
					err,
					"failed to get the app list by the installation proxy, udid: %s, bundle_id: %s",
					d.udid, info.CFBundleIdentifier,
				)
			}

			return nil
		},
	); err != nil {
		return err
	}

	needToInstall := true
	if len(apps) > 0 {
		if strings.EqualFold(apps[0].CFBundleVersion, info.CFBundleVersion) {
			needToInstall = false
		} else {
			d.Infof(
				"need to reinstall the app, udid: %s, file: %s, bundle_id: %s, version: %s => %s",
				d.udid, appPath, info.CFBundleIdentifier, apps[0].CFBundleVersion, info.CFBundleVersion,
			)
		}
	}

	if needToInstall {
		// try to kill the app by bundle id
		if err = d.device.AppKillByBundleID(info.CFBundleIdentifier); err != nil {
			d.Warnf(
				"failed to kill the app, udid: %s, file: %s, bundle_id: %s, error: %+v",
				d.udid, appPath, info.CFBundleIdentifier, err,
			)
		}
		if err = d.device.AppInstall(appPath); err != nil {
			return errors.Wrapf(
				err,
				"failed to install the app, udid: %s, file: %s, bundle_id: %s, version: %s",
				d.udid, appPath, info.CFBundleIdentifier, info.CFBundleVersion,
			)
		}

		d.Infof(
			"succeed to install the app, udid: %s, file: %s, bundle_id: %s, version: %s",
			d.udid, appPath, info.CFBundleIdentifier, info.CFBundleVersion,
		)
	} else {
		d.Debugf(
			"the app has been installed, udid: %s, file: %s, bundle_id: %s, version: %s",
			d.udid, appPath, info.CFBundleIdentifier, info.CFBundleVersion,
		)
	}

	return nil
}

func (d *IOSDevice) AppUninstall(bundleID string) error {
	apps, err := d.device.InstallationProxyBrowse(gid.WithBundleIDs(bundleID))
	if err != nil {
		return errors.Wrapf(
			err,
			"failed to get the app list by the installation proxy, udid: %s, bundle_id: %s",
			d.udid, bundleID,
		)
	}

	if len(apps) > 0 && apps[0].CFBundleIdentifier == bundleID {
		if err = d.AppUninstall(bundleID); err != nil {
			return errors.Wrapf(err, "failed to uninstall the app, udid: %s, bundle_id: %s", d.udid, bundleID)
		}

		d.Infof("succeed to uninstall the app, udid: %s, bundle_id: %s", d.udid, bundleID)
	} else {
		d.Debugf("the app has not been installed, udid: %s, bundle_id: %s", d.udid, bundleID)
	}

	return nil
}

func (d *IOSDevice) AppLaunch(bundleID string, args ...string) error {
	pid, err := d.device.AppLaunch(bundleID)
	if err != nil {
		return errors.Wrapf(err, "failed to launch the app, udid: %s, bundle_id: %s", d.udid, bundleID)
	}

	d.Infof("succeed to launch the app, udid: %s, bundle_id: %s, pid: %d", d.udid, bundleID, pid)
	return nil
}
