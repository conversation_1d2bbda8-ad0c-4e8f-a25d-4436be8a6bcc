package grpc

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"time"

	"github.com/devfeel/mapper"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/utils"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/dynamicpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	tgrpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/types"
)

const (
	productName    types.ProductName = "grpc"
	clientTypeGRPC                   = types.ClientType(productName)
)

var (
	_ clienttypes.IClient = (*Client)(nil)

	errEmptyURL          = errors.New("the url cannot be empty")
	errEmptyProtoManager = errors.New("the proto manager cannot be empty")
)

func ProductName() types.ProductName {
	return productName
}

func NewClient(
	ctx context.Context, exitCh <-chan lang.PlaceholderType, req *clienttypes.CreateClientReq,
) (clienttypes.IClient, error) {
	ci, err := parseCreateClientReq(req)
	if err != nil {
		return nil, errors.Errorf(
			"failed to create %s client, req: %s, error: %v", productName, jsonx.MarshalIgnoreError(req), err,
		)
	}

	c := &Client{
		BasicClient: clienttypes.NewBasicClient(ctx, exitCh, req),

		key:        clienttypes.GetClientKeyByVariables(req.Variables),
		clientInfo: ci,
	}

	c.transport = tgrpc.NewClient(
		c.clientInfo.URL, tgrpc.ClientConf{
			Authority:          c.clientInfo.Authority,
			UserAgent:          c.clientInfo.UserAgent,
			NoTLS:              c.clientInfo.NoTLS,
			InsecureSkipVerify: c.clientInfo.InsecureSkipVerify,
		},
	)

	return c, nil
}

func parseCreateClientReq(req *clienttypes.CreateClientReq) (ci ClientInfo, err error) {
	if req.URL == "" {
		return ci, errEmptyURL
	}
	if req.ProtoManager == nil {
		return ci, errEmptyProtoManager
	}

	u, err := url.Parse(req.URL)
	if err != nil {
		return ci, err
	}

	host := u.Host
	if host == "" {
		host = u.Path
	}
	if u.Port() == "" {
		host = fmt.Sprintf("%s:%d", host, constants.ConstPort80)
	}
	ci.URL = host

	ci.InsecureSkipVerify = true
	if req.Variables != nil {
		if err = mapper.MapperMap(req.Variables, &ci); err != nil {
			return ci, err
		}
	}

	return ci, nil
}

// ProductName 获取客户端的产品名称
func (c *Client) ProductName() types.ProductName {
	return productName
}

// ClientType 获取客户端类型
func (c *Client) ClientType() types.ClientType {
	return clientTypeGRPC
}

func (c *Client) Key() string {
	return c.key
}

// Environment 获取客户端对应的环境（返回空字符串表示不区分环境）
func (c *Client) Environment() string {
	return c.clientInfo.URL
}

func (c *Client) Close() (err error) {
	c.closeOnce.Do(
		func() {
			if c.Enabled() {
				c.SetDisable()
			}

			if c.transport != nil {
				err = c.transport.Close()
			}
		},
	)

	return err
}

func (c *Client) NewRequest(req *clienttypes.CreateRequestReq) (clienttypes.IRequest, error) {
	var (
		pm = c.CreateClientInfo.ProtoManager

		name    = req.Step.GetName()
		method  = req.Step.GetMethod()
		headers = req.Step.GetHeaders()
		body    = req.Step.GetBody()
	)
	if req.Variables != nil {
		headers = common.TemplateExecuteByMap[map[string]string](req.Step.GetHeaders(), req.Variables)
		body = common.TemplateExecuteByString(name, req.Step.GetBody(), req.Variables)
	}

	md, err := c.CreateClientInfo.ProtoManager.FindMethodDescriptorByName(method)
	if err != nil {
		return nil, err
	}

	r := &Request{
		name:             name,
		seq:              c.GetSeq(),
		raw:              utils.StringToByteSlice(body),
		startedAt:        time.Time{},
		host:             c.clientInfo.URL,
		methodDescriptor: md,
		inputDescriptor:  md.Input(),
		outputDescriptor: md.Output(),
		headers:          metadata.New(headers),
	}
	r.r = tgrpc.NewRequest(md, c.grpcRequestSupplier(pm, r))

	return r, nil
}

func (c *Client) grpcRequestSupplier(pm *protobuf.ProtoManager, req *Request) tgrpc.RequestSupplier {
	key := c.Key()

	return func(msg *dynamicpb.Message) error {
		// JSON -> PB Message
		if err := pm.UnmarshalMessage(msg, req.raw); err != nil {
			return errors.Wrapf(
				err,
				"failed to unmarshal request data to message[%s], key: %s, method: %s, seq: %d",
				req.inputDescriptor.FullName(), key, req.methodDescriptor.FullName(), req.seq,
			)
		}

		return io.EOF
	}
}

func (c *Client) Send(ctx context.Context, req clienttypes.IRequest) (clienttypes.IResponse, error) {
	return c.Call(ctx, req, c.Key(), c.ClientType(), c.send)
}

func (c *Client) send(ctx context.Context, req clienttypes.IRequest) (clienttypes.IResponse, error) {
	key := c.Key()

	// logger := logx.WithContext(ctx)

	v, ok := req.(*Request)
	if !ok {
		return nil, errors.Errorf("invalid request type, expected %T, but got %T, key: %s", (*Request)(nil), req, key)
	}

	if c.transport == nil {
		return nil, errors.Errorf(
			"grpc transport is null, key: %s, method: %s, seq: %d",
			key, v.methodDescriptor.FullName(), v.seq,
		)
	} else if v.methodDescriptor.IsStreamingClient() || v.methodDescriptor.IsStreamingServer() {
		return nil, errors.Errorf(
			"non-unary gRPC methods are not currently supported, key: %s, method: %s, seq: %d",
			key, v.methodDescriptor.FullName(), v.seq,
		)
	}

	resp := &Response{
		ctx:              ctx,
		seq:              v.seq,
		methodDescriptor: v.methodDescriptor,
		outputDescriptor: v.outputDescriptor,
	}
	resp.r = tgrpc.NewResponse(v.methodDescriptor, resp)

	if v.startedAt.IsZero() {
		v.startedAt = time.Now()
	}

	invokeCtx, cancel := context.WithTimeout(ctx, clienttypes.WaiteRespTimeout)
	defer cancel()

	err := c.transport.InvokeRPC(invokeCtx, v.r, v.headers, resp.r)
	if err != nil && !errors.Is(err, resp.err) {
		//logger.Errorf(
		//	"failed to invoke rpc, key: %s, method: %s, seq: %d, error: %+v",
		//	key, v.methodDescriptor.FullName(), v.seq, err,
		//)
		resp.err = err
	}
	resp.elapsed = time.Since(v.startedAt)

	//logger.Debugf(
	//	"the response data of %q which is invoked by grpc, key: %s, data: %s",
	//	v.methodDescriptor.FullName(), key, jsonx.MarshalIgnoreError(resp.body),
	//)

	return resp, nil
}
