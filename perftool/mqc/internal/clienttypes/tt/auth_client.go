package tt

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"github.com/devfeel/mapper"
	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/hash"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zeromicro/go-zero/core/trace"
	"github.com/zeromicro/go-zero/core/utils"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/encoding/gzip"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/reflect/protoreflect"

	qetconstants "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	tgrpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/grpc"
	ttcp "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/tcp"
	twebsocket "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/websocket"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/types"
)

const clientTypeTTAuth = types.ClientType(productName + "_auth")

var (
	_ clienttypes.IClient  = (*AuthClient)(nil)
	_ clienttypes.IMonitor = (*AuthClient)(nil)
)

func NewAuthClient(
	ctx context.Context, exitCh <-chan lang.PlaceholderType, req *clienttypes.CreateClientReq,
) (v clienttypes.IClient, err error) {
	ci, ai, err := parseCreateAuthClientReq(req)
	if err != nil {
		return nil, errors.Errorf(
			"failed to create %s client, req: %s, error: %v", productName, jsonx.MarshalIgnoreError(req), err,
		)
	}

	c := &AuthClient{
		client: &client[*AuthClient]{
			BasicClient: clienttypes.NewBasicClient(ctx, exitCh, req),

			grpcWaitResponseTimeout: clienttypes.WaiteRespTimeout,

			clientInfo: ci,
			authInfo:   ai,
		},
	}
	c.tcpOrWsProtocol = NewProtocol(c.client)
	c.tcpOrWsPendingRequests = &TcpOrWsPendingRequests[*AuthClient]{
		client: c,
		ctx:    ctx,
		exitCh: exitCh,
		cache: hashmap.New[string, *TCPOrWSRequest](
			constants.ConstDefaultMakeMapSize, generic.Equals[string], generic.HashString,
		),
		timeout: clienttypes.WaiteRespTimeout,
	}
	c.metadata = clientMetadata{
		clientVersion: c.clientVersion(),
		terminalType:  c.terminalType(),
		deviceID:      c.deviceID(),
	}
	c.metadata.deviceIDString = fmt.Sprintf("%x", c.metadata.deviceID)

	// 准备请求数据
	c.prepareReqData()

	// 定时清理超时的请求数据
	threading.GoSafe(c.tcpOrWsPendingRequests.handle)

	return c, nil
}

func parseCreateAuthClientReq(req *clienttypes.CreateClientReq) (ci ClientInfo, ai AuthInfo, err error) {
	if req.URL == "" {
		return ci, ai, errEmptyURL
	}
	if req.ProtoManager == nil {
		return ci, ai, errEmptyProtoManager
	}

	ci = defaultClientInfo()

	u, err := url.Parse(req.URL)
	if err != nil {
		return ci, ai, err
	}

	ci.Url = fmt.Sprintf("%s://%s", u.Scheme, u.Host)
	if u.User != nil {
		ci.Username = u.User.Username()
		ci.Password, _ = u.User.Password()
	}

	switch u.Scheme {
	case string(qetconstants.TCP), string(qetconstants.GRPC):
		host := u.Host
		if host == "" {
			host = u.Path
		}
		if u.Port() == "" {
			host = fmt.Sprintf("%s:%d", host, constants.ConstPort80)
		}

		if u.Scheme == string(qetconstants.TCP) {
			ci.TcpURL = host
		} else {
			ci.GrpcURL = host
		}
	case string(qetconstants.WS), string(qetconstants.WSS):
		ci.WebsocketURL = ci.Url
	}

	if req.Variables != nil {
		if err = mapper.MapperMap(req.Variables, &ci); err != nil {
			return ci, ai, err
		}
	}
	if ci.ClientType == ConstClientTypePCLFG {
		ci.AppID = ConstAppIDTTPCLFG
	}

	if ci.TcpURL == "" && ci.WebsocketURL == "" && ci.ClientType != ConstClientTypePCLFG {
		return ci, ai, errEmptyTcpOrWsURL
	} else if ci.GrpcURL == "" && ci.ClientType == ConstClientTypePCLFG {
		return ci, ai, errEmptyGrpcURL
	} else if ci.Username == "" {
		return ci, ai, errEmptyUsername
	} else if ci.Password == "" {
		return ci, ai, errEmptyPassword
	}

	ai = AuthInfo{
		Username:    ci.Username,
		Password:    ci.Password,
		MD5Password: hash.Md5Hex(utils.StringToByteSlice(ci.Password)),
	}

	return ci, ai, nil
}

func (c *AuthClient) prepareReqData() {
	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		c.authReqData = map[string]any{
			keyOfPasswordCredential: map[string]any{
				keyOfIdentifier: c.authInfo.Username,
				keyOfPassword:   c.authInfo.MD5Password,
			},
		}
		c.authReqBody = jsonx.MarshalToStringIgnoreError(c.authReqData)
		c.authReqStep = &commonpb.PerfCaseStepV2{
			Name:   AuthMethodNameZH,
			Method: methodFullNameOfSignIn,
			Body:   c.authReqBody,
			Sleep:  AuthSleepTime,

			Cmd:      cmdOfAuth,
			GrpcPath: methodNameOfSignIn,
		}
	} else {
		c.authReqData = map[string]any{
			keyOfBaseReq: map[string]any{
				keyOfAppID:    uint32(c.clientInfo.AppID),
				keyOfMarketID: uint32(c.clientInfo.MarketID),
			},
			keyOfUserPhone:        c.authInfo.Username,
			keyOfPwd:              c.authInfo.MD5Password,
			keyOfLoginType:        uint32(ConstLoginTypeManual),
			keyOfLoginAccountType: uint32(ConstLoginAccountTypeTTFuzzy),
		}
		if c.clientInfo.ClientType == ConstClientTypePCTT {
			// 对于PC端，`signature`字段为数字版本号AES加密
			c.authReqData[keyOfSignature], _ = c.tcpOrWsProtocol.Encrypt(
				utils.StringToByteSlice(strconv.FormatInt(int64(c.metadata.clientVersion), 10)),
				ConstCryptAlgorithmAesDecryptWithPrivateKey,
			)
		}

		c.authReqBody = jsonx.MarshalToStringIgnoreError(c.authReqData)
		c.authReqStep = &commonpb.PerfCaseStepV2{
			Name:   AuthMethodNameZH,
			Method: methodFullNameOfAuth,
			Body:   c.authReqBody,
			Sleep:  AuthSleepTime,

			Cmd:      cmdOfAuth,
			GrpcPath: methodNameOfAuth,
		}
	}
}

func (c *AuthClient) RequiredAfterAuth() bool {
	return false
}

func (c *AuthClient) GetMonitorParams(
	_ *types.GeneralConfig, _ *protobuf.ProtoManager, _ *commonpb.PerfCaseContentV2,
) clienttypes.MonitorParams {
	return clienttypes.MonitorParams{
		Commands: []uint32{cmdOfAuth},
	}
}

func (c *AuthClient) ProductName() types.ProductName {
	return productName
}

func (c *AuthClient) ClientType() types.ClientType {
	return clientTypeTTAuth
}

func (c *AuthClient) newTransports() error {
	if c.clientInfo.TcpURL == "" && c.clientInfo.WebsocketURL == "" && c.clientInfo.ClientType != ConstClientTypePCLFG {
		// 非PC极速版通过`tcp`或者`websocket`进行登录
		return errEmptyTcpOrWsURL
	}
	if c.clientInfo.GrpcURL == "" && c.clientInfo.ClientType == ConstClientTypePCLFG {
		// PC极速版通过`grpc`进行登录
		return errEmptyGrpcURL
	}

	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		c.newGRPCTransport()
	} else if c.clientInfo.TcpURL != "" {
		c.newTCPTransport()
	} else if c.clientInfo.WebsocketURL != "" {
		c.newWebsocketTransport()
	}

	return nil
}

func (c *AuthClient) newTCPTransport() {
	if c.clientInfo.TcpURL == "" {
		return
	} else if c.tcpURL == "" {
		c.tcpURL = c.clientInfo.TcpURL
	}

	if c.clientInfo.GrpcAuthority != "" {
		c.grpcAuthority = c.clientInfo.GrpcAuthority
	}

	if c.tcpTransport != nil {
		_ = c.tcpTransport.Close()
	}

	c.tcpTransport = ttcp.NewClient(
		c.tcpURL, c, ttcp.ClientConf{
			QueueLen:     ClientQueueLen,
			IdleTimeout:  clienttypes.ClientIdleTimeout,
			ReadTimeout:  clienttypes.ClientReadTimeout,
			WriteTimeout: clienttypes.ClientWriteTimeout,
			DialTimeout:  clienttypes.ClientDialTimeout,
		},
	)
}

func (c *AuthClient) newWebsocketTransport() {
	if c.clientInfo.WebsocketURL == "" {
		return
	} else if c.wsURL == "" {
		c.wsURL = c.clientInfo.WebsocketURL
	}

	if c.wsTransport != nil {
		_ = c.wsTransport.Close()
	}

	c.wsTransport = twebsocket.NewClient(
		c.wsURL, c, twebsocket.ClientConf{
			QueueLen:     ClientQueueLen,
			IdleTimeout:  clienttypes.ClientIdleTimeout,
			ReadTimeout:  clienttypes.ClientReadTimeout,
			WriteTimeout: clienttypes.ClientWriteTimeout,
			DialTimeout:  clienttypes.ClientDialTimeout,
		},
	)
}

func (c *AuthClient) newGRPCTransport() {
	if c.clientInfo.GrpcURL == "" {
		return
	} else if c.grpcURL == "" {
		c.grpcURL = c.clientInfo.GrpcURL
	}

	if c.grpcTransport != nil {
		_ = c.grpcTransport.Close()
	}

	c.grpcTransport = tgrpc.NewClient(c.grpcURL, tgrpc.ClientConf{Authority: c.grpcAuthority})
}

func (c *AuthClient) closeTransports() (err error) {
	defer func() {
		c.tcpTransport = nil
		c.wsTransport = nil
		c.grpcTransport = nil
	}()

	if c.tcpTransport != nil {
		err = multierror.Append(err, c.tcpTransport.Close())
	}

	if c.wsTransport != nil {
		err = multierror.Append(err, c.wsTransport.Close())
	}

	if c.grpcTransport != nil {
		err = multierror.Append(err, c.grpcTransport.Close())
	}

	return err
}

func (c *AuthClient) NewRequest(req *clienttypes.CreateRequestReq) (clienttypes.IRequest, error) {
	if req.Step == nil {
		req.Step = &commonpb.PerfCaseStepV2{
			Name:   c.authReqStep.Name,
			Method: c.authReqStep.Method,
			Body:   c.authReqBody,
			Sleep:  c.authReqStep.Sleep,

			Cmd:      c.authReqStep.Cmd,
			GrpcPath: c.authReqStep.GrpcPath,
		}
	} else {
		req.Step.Method = c.authReqStep.Method
		req.Step.Body = c.authReqBody
		req.Step.Cmd = c.authReqStep.Cmd
		req.Step.GrpcPath = c.authReqStep.GrpcPath
	}

	if req.Variables != nil {
		req.Step.Body = common.TemplateExecuteByString(req.Step.GetName(), req.Step.GetBody(), req.Variables)
	}

	return c.newTCPOrWSOrGRPCRequest(req.Step)
}

func (c *AuthClient) newTCPOrWSOrGRPCRequest(step *commonpb.PerfCaseStepV2) (req clienttypes.IRequest, err error) {
	var (
		key = c.Key()
		pm  = c.CreateClientInfo.ProtoManager

		name    = step.GetName()
		method  = step.GetMethod()
		headers = step.GetHeaders()
		body    = utils.StringToByteSlice(step.GetBody())
		cmd     = step.GetCmd()

		md protoreflect.MethodDescriptor
		id protoreflect.MessageDescriptor
		od protoreflect.MessageDescriptor
	)

	if method == "" {
		return nil, errors.Errorf(
			"the method cannot be empty while creating tcp or websocket request, key: %s", key,
		)
	}

	// TCP or Websocket or gRPC
	md, err = pm.FindMethodDescriptorByName(method)
	if err != nil {
		return nil, err
	}
	id = md.Input()
	od = md.Output()

	if body == nil {
		body = emptyReqBody
	}

	r := &Request{
		name:    name,
		cmd:     cmd,
		seq:     c.GetSeq(),
		raw:     body,
		handled: false,
	}

	if c.clientInfo.ClientType == ConstClientTypePCLFG {
		req = c.newGRPCRequest(pm, r, md, id, od, metadata.New(headers))
	} else {
		req, err = c.newTCPOrWSRequest(pm, r, md, id, od)
	}

	return req, err
}

func (c *AuthClient) newTCPOrWSRequest(
	pm *protobuf.ProtoManager, r *Request,
	md protoreflect.MethodDescriptor, id, od protoreflect.MessageDescriptor,
) (req *TCPOrWSRequest, err error) {
	if c.wsTransport != nil {
		r.addr = c.wsURL
	} else {
		r.addr = fmt.Sprintf("%s://%s", qetconstants.TCP, c.tcpURL)
	}

	// JSON -> PB（bytes）
	data := r.raw
	data = c.tryToSetBaseReq(data)
	data = c.tryToSetClientVersion(md, data)
	r.raw, err = pm.UnmarshalPB(string(id.FullName()), data)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to unmarshal the request data, method: %s, cmd: %d, seq: %d",
			md.FullName(), r.cmd, r.seq,
		)
	}

	return &TCPOrWSRequest{
		Request:          r,
		methodDescriptor: md,
		inputDescriptor:  id,
		outputDescriptor: od,
		data:             data,
	}, nil
}

func (c *AuthClient) newGRPCRequest(
	pm *protobuf.ProtoManager, r *Request,
	md protoreflect.MethodDescriptor, id, od protoreflect.MessageDescriptor,
	headers metadata.MD,
) *GRPCRequest {
	r.addr = fmt.Sprintf("%s://%s", qetconstants.GRPC, c.grpcURL)

	req := &GRPCRequest{
		Request:             r,
		methodDescriptor:    md,
		inputDescriptor:     id,
		outputDescriptor:    od,
		headers:             headers,
		data:                r.raw,
		supportCommonStatus: true,
	}

	req.r = tgrpc.NewRequest(
		req.methodDescriptor, c.grpcRequestSupplier(pm, req),
		tgrpc.WithCallOptions(grpc.UseCompressor(gzip.Name)),
	)

	return req
}

func (c *AuthClient) Send(ctx context.Context, req clienttypes.IRequest) (clienttypes.IResponse, error) {
	return c.Call(ctx, req, c.Key(), clientTypeTTAuth, c.send)
}

func (c *AuthClient) send(ctx context.Context, req clienttypes.IRequest) (resp clienttypes.IResponse, err error) {
	key := c.Key()

	if err = c.newTransports(); err != nil {
		return nil, err
	}
	defer func() {
		_ = c.closeTransports()
	}()

	switch v := req.(type) {
	case *TCPOrWSRequest:
		resp, err = c.sendTCPOrWSRequest(ctx, v)
	case *GRPCRequest:
		resp, err = c.sendGRPCRequest(ctx, v)
	default:
		err = errors.Errorf(
			"invalid request type, expected %T or %T, but got %T, key: %s",
			(*TCPOrWSRequest)(nil), (*GRPCRequest)(nil), req, key,
		)
	}

	return resp, err
}

func (c *AuthClient) sendGRPCRequest(ctx context.Context, req *GRPCRequest) (resp *GRPCResponse, err error) {
	key := c.Key()

	if c.grpcTransport == nil {
		return nil, errors.Errorf(
			"grpc transport is null, key: %s, method: %s, cmd: %d, seq: %d",
			key, req.methodDescriptor.FullName(), req.cmd, req.seq,
		)
	} else if req.methodDescriptor.IsStreamingClient() || req.methodDescriptor.IsStreamingServer() {
		return nil, errors.Errorf(
			"non-unary gRPC methods are not currently supported, key: %s, method: %s, cmd: %d, seq: %d",
			key, req.methodDescriptor.FullName(), req.cmd, req.seq,
		)
	}

	// 设置固定的请求头信息
	req.headers.Set(reqHeaderKeyOfXTTClientBundleID, c.clientInfo.BundleID)
	req.headers.Set(reqHeaderKeyOfXTTMarket, strconv.FormatUint(uint64(c.clientInfo.MarketID), 10))
	req.headers.Set(reqHeaderKeyOfXTTClientVersion, strconv.FormatInt(int64(c.metadata.clientVersion), 10))
	req.headers.Set(reqHeaderKeyOfXTTDeviceID, c.metadata.deviceIDString)
	req.headers.Set(reqHeaderKeyOfXTTTerminalType, strconv.FormatUint(uint64(c.metadata.terminalType), 10))
	req.headers.Set(reqHeaderKeyOfXTTClientType, strconv.FormatUint(uint64(c.clientInfo.ClientType), 10))
	if req.supportCommonStatus {
		req.headers.Set(reqHeaderKeyOfXTTSupportCommonStatus, "true")
	}
	if traceID := trace.TraceIDFromContext(ctx); traceID != "" {
		req.headers.Set(reqHeaderKeyOfRequestID, traceID)
	}
	if c.clientInfo.SubEnvFlag != "" {
		req.headers.Set(reqHeaderKeyOfXQWTrafficMark, c.clientInfo.SubEnvFlag)
	}

	pm := c.CreateClientInfo.ProtoManager
	commonStatusMessage, err := pm.CreateMessage(messageFullNameOfCommonStatus)
	if err != nil {
		return nil, err
	}
	defer pm.PutMessage(commonStatusMessage)

	resp = &GRPCResponse{
		Response: &Response{
			ctx: ctx,
			cmd: req.cmd,
			seq: req.seq,
		},
		methodDescriptor:    req.methodDescriptor,
		outputDescriptor:    req.outputDescriptor,
		supportCommonStatus: req.supportCommonStatus,
		commonStatusMessage: commonStatusMessage,
	}
	resp.r = tgrpc.NewResponse(req.methodDescriptor, resp)

	if req.startedAt.IsZero() {
		req.startedAt = time.Now()
	}

	invokeCtx, cancel := context.WithTimeout(ctx, c.grpcWaitResponseTimeout)
	defer cancel()

	err = c.grpcTransport.InvokeRPC(invokeCtx, req.r, req.headers, resp.r)
	err = resp.handle(err)
	if err != nil && !errors.Is(err, resp.err) {
		resp.err = err
	}

	if resp.err == nil && resp.ttCode != 0 {
		resp.err = errors.Errorf(
			"code: %d, message: %s, tt-code: %d, tt-message: %s",
			resp.status.Code(), resp.status.Message(), resp.ttCode, resp.ttMessage,
		)
	}

	return resp, nil
}

func (c *AuthClient) Close() (err error) {
	c.closeOnce.Do(
		func() {
			if c.Enabled() {
				c.SetDisable()
			}

			err = c.closeTransports()
		},
	)

	return err
}

// Recv 接收数据
func (c *AuthClient) Recv(pkg []byte) {
	key := c.Key()

	req, resp, err := c.handleRecvData(pkg)
	if err != nil {
		c.Errorf("failed to unpack the response, key: %s, error: %+v", key, err)
	} else if resp != nil && resp.isPush {
		if resp.cmd == cmdOfKickOut {
			c.Warnf("receive a kick out message, key: %s, cmd: %d, seq: %d", key, resp.cmd, resp.seq)
			_ = c.Close()
		} else {
			c.Debugf("receive a push message, key: %s, cmd: %d, seq: %d", key, resp.cmd, resp.seq)
		}
	} else if resp != nil && req == nil {
		c.Warnf(
			"the request corresponding to the response could not be found, key: %s, cmd: %d, seq: %d, ret: %d(%s), svrRet: %d(%s)",
			key, resp.cmd, resp.seq,
			resp.ret1, MessageFromCode(int(resp.ret1)),
			resp.ret2, MessageFromCode(int(resp.ret2)),
		)
	}
}

// ParsePackage 解析数据包
func (c *AuthClient) ParsePackage(buf []byte) (int, int) {
	return parsePackage(buf)
}
